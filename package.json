{"name": "vue-admin-template", "version": "0.0.1", "type": "module", "description": "AI洞察中心", "scripts": {"dev": "vite --mode dev", "uat": "vite --mode uat", "pro": "vite --mode pro", "ts:check": "vue-tsc --noEmit", "lint:eslint": "eslint --config eslint.config.ts --fix ./src && git add .", "build:pro": "vite build --mode pro", "build:uat": "vite build --mode uat", "build:test": " vite build --mode dev", "prepare": "husky"}, "keywords": ["vue-admin"], "author": "MMT FE", "license": "ISC", "lint-staged": {"*.{js,ts,vue,jsx,tsx}": ["eslint --config eslint.config.ts ./src"]}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@higgins-mmt/vite-plugin-i18n-transformer": "^1.2.0", "@iconify/vue": "^4.3.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.1", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@unocss/eslint-plugin": "^65.5.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "eslint": "^9.25.1", "husky": "^9.1.7", "jiti": "^2.4.2", "lint-staged": "^15.5.1", "sass": "^1.87.0", "typescript": "^5.8.3", "unocss": "^65.5.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.2", "vue-tsc": "^2.2.8"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^6.1.0", "@visactor/vtable": "^1.18.1", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^12.8.2", "animate.css": "^4.1.1", "await-to-js": "^3.0.0", "axios": "^1.8.4", "bignumber.js": "^9.3.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.8", "fuse.js": "^7.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.3.1", "qs": "^6.14.0", "socket.io-client": "^4.8.1", "tinymce": "^7.8.0", "universal-cookie": "^7.2.2", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-hooks-plus": "2.2.3", "vue-i18n": "^11.1.3", "vue-matomo": "^4.2.0", "vue-router": "^4.5.0", "vxe-pc-ui": "^4.5.27", "vxe-table": "^4.13.7", "xe-utils": "^3.7.4"}}