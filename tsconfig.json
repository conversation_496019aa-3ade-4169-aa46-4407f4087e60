{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
    },
    "allowJs": true,
    "noImplicitAny": false,
    "sourceMap": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "vite.config.ts"
  ],
  "exclude": ["dist", "node_modules"]
}
