import type { ApiConfig, } from "@/components/ApiSelect"
import { get, postJSON, } from "@/utils/fetch.ts"
import { queryCategoryTree, } from "@/views/Label/apis/Info.ts"

export namespace UserInfoAPI {
  export interface UserInfo {
    /**
     * 用户Id
     */
    userId: string
    /**
     * 用户名称
     */
    name: string
    /**
     * 用户头像
     */
    avatarUrl: string
    /**
     * 用户邮箱
     */
    email: string
  }
  export type Response = NewResponseData<UserInfo>
  /**
   * 退出登录
   */
  export interface UserLogoutType {
    loginPage: string
  }
}

export function fetchUserInfo() {
  return postJSON<UserInfoAPI.Response>({
    url: `/common/sso/client/user`,
  },)
}

// 退出登录
export function clientLogout() {
  return postJSON<NewResponseData<UserInfoAPI.UserLogoutType>>({ url: `/common/sso/client/logout`, },)
}

export namespace AllUserAPI {
  export interface Data {
    /**
     * 子类
     */
    child?: Data[] | null
    /**
     * 用户Id
     */
    id?: number | null
    /**
     * 用户名称 只有在最后一级才是用户名称 其他的都是组织架构名称
     */
    name?: null | string
  }
  export type Response = NewResponseData<Data[]>
}

export function fetchAllUser() {
  return get<AllUserAPI.Response>({
    url: `/base/allUsers`,
  },)
}

export const allUserApiConfig: ApiConfig = {
  api: queryCategoryTree,
  config: {
    label: "categoryName",
    value: "categoryCode",
    children: "sonCategory",
  },
}
