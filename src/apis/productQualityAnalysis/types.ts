/**
 * 标签管理列表相关
 */
export namespace LabelManagePageList {
  export interface Params {
    tagTypeList: string[]
    accuracy?: number
    relationOperator?: string
    firstLevelTagIdList: (number | string)[]
    secondLevelTagIdList: (number | string)[]
    statusList: (number | string)[]
  }
  export interface Row {
    id: number
    firstLevelTagId: number
    firstLevelTagName: string
    secondLevelTagId: number
    secondLevelTagName: string
    secondLevelTagEn?: string
    tagDescription?: string
    positiveExample?: string
    negativeExample?: string
    labelingStatus?: string
    labelingStatusName?: string
    accuracy?: number
    f1Score?: number
    sampleCount?: number
    status: string
    tagType: string
    tagTypeName?: string
    statusName?: string
    effectiveDate?: string
    modifyByName?: string
    modifyTime?: string
  }
  export type List = Row[]
  export type Response = NewPageResponseData<Row>
  export type Request = Params & PageParams
}

/**
 * 标签管理新增/编辑相关
 */
export namespace LabelManageDataEdit {
  export interface Row {
    id?: number
    tagType: string
    tagTypeName?: string
    firstLevelTagId?: number
    firstLevelTagName: string
    secondLevelTagId?: number
    secondLevelTagName: string
    secondLevelTagEn: string
    tagDescription?: string
    positiveExample?: string
    negativeExample?: string
    status?: string
    _X_ROW_KEY?: string
  }
  export type Params = Row[]
  export type Response = NewResponseData<any>
}

/**
 * 样本列表
 */
export namespace LabelManageSimpleList {
  export interface Row {
    id: number
    tagId: number
    site: string
    commentId: string
    commentDate: string
    rating: number
    commentTitle: string
    commentText: string
    suggestionTag: string
    tagCategory: string
    exampleType: number
    exampleTypeName: string
    aiExampleType: number
    aiExampleTypeName: string
    effectiveDate: string
    createByName: string
    modifyByName: string
  }
  export type List = Row[]
  export type Response = NewResponseData<List>
}

/**
 * 评论管理列表相关
 */
export namespace CommentManagePageList {
  export interface Params {
    siteCodeList?: string[]
    reviewRatingList?: number[]
    styleList?: number[]
    commentDate?: string[]
    tagIdList?: number[]
    vendorIdList?: string[]
    isReviewUpdate?: boolean
    reviewDatetimeStart?: string
    reviewDatetimeEnd?: string
  }
  export interface Row {
    id: number
    siteCode: string
    productThumbnail: string
    productTitle: string
    color: string
    size: string
    childAsin: string
    reviewRating: number
    reviewDatetime: string
    reviewTitle: string
    reviewText: string
    reviewUserName: string
    reviewUrl: string
    style: string
    ct: string
    stylePosition: string
    productStarRating: number
    launchSeason: string
    vendorName: string
    tagNameList: string[]
    tagNameStr: string
    tagRespList: {
      id: number
      reviewId: number
      tagId: number
      tagName: string
      firstLevelTagId: number
      firstLevelTagName: string
    }[]
    isReviewUpdate: number
    createById: number
    createByName: string
    createTime: string
    modifyById: number
    modifyByName: string
    modifyTime: string
  }
  export type List = Row[]
  export type Response = NewPageResponseData<Row>
  export type Request = Params & PageParams
}

/**
 * 评论样本管理列表相关
 */
export namespace CommentSimpleManagePageList {
  export interface Params {
    siteList?: string[]
    tagCategoryList?: number[]
    commentDate?: string[]
    suggestionTagList?: number[]
    commentDateStart?: string
    commentDateEnd?: string
  }
  export interface Row {
    id: number
    aiExampleType: number
    aiExampleTypeName: string
    commentDate: string
    commentId: string
    commentText: string
    commentTitle: string
    effectiveDate: string
    exampleType: number
    exampleTypeName: string
    modifyByName: string
    modifyTime: string
    rating: number
    site: string
    suggestionTag: string
    tagCategory: string
    tagId: number
  }
  export type List = Row[]
  export type Response = NewPageResponseData<Row>
  export type Request = Params & PageParams
}

/**
 * 标签相关
 */
export namespace CommentManageTags {
  export interface Row {
    accuracy: number
    effectiveDate: string
    f1Score: number
    firstLevelTagId: number
    firstLevelTagName: string
    id: number
    labelingStatus: string
    labelingStatusName: string
    modifyByName: string
    modifyTime: string
    negativeExample: string
    positiveExample: string
    sampleCount: number
    secondLevelTagEn: string
    secondLevelTagId: number
    secondLevelTagName: string
    status: string
    statusName: string
    tagDescription: string
    tagType: string
    tagTypeName: string
  }
  export type Response = NewResponseData<Record<string, Row[]>>
}
