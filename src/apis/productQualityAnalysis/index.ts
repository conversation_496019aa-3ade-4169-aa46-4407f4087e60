import type {
  CommentManagePageList,
  CommentManageTags,
  CommentSimpleManageEditData,
  CommentSimpleManagePageList,
  GetOssSignAPI,
  LabelManageDataEdit,
  LabelManagePageList,
  LabelManageSimpleList,
} from "./types"

import { get, postJSON, } from "@/utils/fetch"
// 获取标签管理列表
export function getLabelManageList(data: LabelManagePageList.Request,) {
  return postJSON<LabelManagePageList.Response>({
    url: `/qulityTagManager/queryQualityTagManagerPage`,
    data,
  },)
}

// 获取标签下拉列表
export function getLabelOptionList() {
  return get<CommentManageTags.Response>({
    url: `/qulityTagManager/qualityTagList`,
  },)
}

// 获取启用标签下拉列表
export function getEnableLabelOptionList() {
  return get<CommentManageTags.Response>({
    url: `/qulityTagManager/qualityTagListOnStart`,
  },)
}

/**
 * 标签结构查询下拉框
 */
interface labelStructure {
  tagLevel: number | string
  tagTypeList?: string[]
  parentIdList: (number | string)[]
}
export function queryTagStructureBoxNew(formData: labelStructure,) {
  if (formData.parentIdList?.length > 0) {
    return postJSON<NewBasicResponseData>({
      url: `/tagStructure/queryTagStructureBoxNew`,
      data: formData,
    },)
  } else {
    return Promise.resolve()
  }
}

/**
 * 启用或禁用
 *
 */
interface editStatus {
  id: number
  oldStatus: string
  newStatus: string
}
interface StatusResponse {
  updateStatusReqList: editStatus[]
}
export function tagManageStartOrBan(formData: StatusResponse,) {
  return postJSON<NewBasicResponseData>({
    url: `/qulityTagManager/startOrBan`,
    data: formData,
  },)
}

// 新增标签管理
export function addLabelManageData(data: LabelManageDataEdit.Params,) {
  return postJSON<LabelManageDataEdit.Response>({
    url: `/qulityTagManager/saveQualityTagManager`,
    data,
  },)
}

// 批量编辑标签管理
export function editLabelManageData(data: LabelManageDataEdit.Params,) {
  return postJSON<LabelManageDataEdit.Response>({
    url: `/qulityTagManager/updateBatch`,
    data,
  },)
}

// 批量打标
export function markLabelById(ids: number[],) {
  return get<LabelManageDataEdit.Response>({
    url: `/qulityTagManager/sampleMarking?idList=${ids.join(",",)}`,
  },)
}

// 根据id获取样本列表
export function getSimpleListById(id: number,) {
  return get<LabelManageSimpleList.Response>({
    url: `/qulityTagManager/querySampleDetailByTagId?id=${id}`,
  },)
}

// 获取style列表
export function getStyleList() {
  return get<NewResponseData<{ styleNumber: string, styleStructure: string }[]>>({
    url: "/base/wms/style/all",
  },)
}
// 获取供应商列表
export function getVendorList() {
  return get<NewResponseData<{ vendorName: string, vendorId: string }[]>>({
    url: "/base/getFactory",
  },)
}

// 批量保存样本数据
export function saveSimpleList(data: LabelManageSimpleList.List,) {
  return postJSON<LabelManageDataEdit.Response>({
    url: `/qualityReviewSample/batchUpdate`,
    data,
  },)
}

// 获取评论管理列表
export function getCommentManageList(data: CommentManagePageList.Request,) {
  return postJSON<CommentManagePageList.Response>({
    url: `/amazon/sellerReviews/page`,
    data,
  },)
}

// 评论管理保存标签
export function saveTagInfo(data: { reviewId: number, tagList: { tagId: number, tagName: string, style?: { color: string, backgroundColor: string } }[] },) {
  return postJSON<NewResponseData<boolean>>({
    url: `/amazon/sellerReviews/updateTagInfo`,
    data,
  },)
}

// 获取评论样本管理列表
export function getCommentSimpleManageList(data: CommentSimpleManagePageList.Request,) {
  return postJSON<CommentSimpleManagePageList.Response>({
    url: `/qualityReviewSample/queryReviewSamplePage`,
    data,
  },)
}

// 根据id删除评论样本
export function deleteCommentSimpleById(id: number,) {
  return get<NewResponseData<boolean>>({
    url: `/qualityReviewSample/deleteReviewSampleById?id=${id}`,
  },)
}

// 获取oss签名
export function getOssSign(data: GetOssSignAPI.Request,) {
  return postJSON<GetOssSignAPI.Response>({ url: "/base/getOssSign", data, },)
}

// 根据url导入评论管理列表
export function importCommentSimpleByUrl(data: { fileUrl: string, fileName: string },) {
  return postJSON<NewResponseData<CommentSimpleManageEditData.ImportRow[]>>({
    url: `/qualityReviewSample/importSampleExcel`,
    data,
  },)
}

// 提交导入评论样本列表
export function submitCommentSimpleList(data: { reqList: CommentSimpleManageEditData.ImportRow[] },) {
  return postJSON<NewResponseData<any>>({
    url: `/qualityReviewSample/importSample`,
    data,
  },)
}
