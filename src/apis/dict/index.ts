import { get, } from "@/utils/fetch.ts"

export type DictKey =
  "PLATFORM_PRODUCT_PEOPLE" |
/**
 * 状态
 */
  "commonStatus" |
/**
 * 平台站点店铺
 */
  "platAndSiteAndShop" |
/**
 * 标签类型
 */
  "PLM_TAG_TYPE" |
/**
 * 标签状态
 */
  "PLM_STATUS" |
/**
 * 正确率运算关系
 */
  "PLM_RELATION_OPERATE" |
/**
 * 评论站点
 */
  "station" |
  /**
   * 评论样本站点
   */
  "VOC_REPORT_STATION" |
/**
 * 评分
 */
  "PLM_AIDC_STAR_RATING"

export namespace DictAPI {
  export interface Data {
    dictItem?: DictKey
    dictValueList?: DictValue[]
    id?: number
  }

  export interface DictValue {
    /**
     * 展示值-中文
     */
    dictCnName?: string
    /**
     * 展示值-英文
     */
    dictEnName?: string
    /**
     * 传值
     */
    dictValue?: string
    id?: number
  }

  export type Response = NewResponseData<Data[]>
}

export function getDictList() {
  return get<DictAPI.Response>({
    url: `/dict/value/all`,
  },)
}
