import { get, postJSON, } from "@/utils/fetch.ts"

/**
 * QueryLWVocReq
 */
export interface VocRequest {
  /**
   * id,id和styleNo至少一个不为空
   */
  id?: number
  reportDate?: string
  /**
   * styleNo
   */
  styleNo?: string

  [property: string]: any
}

export interface QueryOldestLWVocResp {
  /**
   * 提交时间
   */
  createTime?: string
  /**
   * id
   */
  id?: number
  /**
   * 完成时间
   */
  reportFinishTime?: string
  /**
   * Style编号
   * style
   */
  styleNo?: string

  [property: string]: any
}

export interface QueryLWReviewVocResp {
  /**
   * 优点描述
   */
  advantage?: string
  /**
   * 缺点描述
   */
  defect?: string
  /**
   * 评论原文
   */
  reviewText?: string

  [property: string]: any
}

/**
 * 最新打标结果记录响应
 */
export interface LatestMarkingRecordsResp {
  /**
   * 记录ID
   */
  id?: number
  /**
   * Style编号
   */
  style?: string
  /**
   * 提交时间
   */
  submitTime?: string
  /**
   * 完成时间
   */
  completeTime?: string
  /**
   * 评论数据文件名
   */
  reviewDataFile?: string
  /**
   * 描述信息
   */
  description?: string

  [property: string]: any
}

/**
 * AI打标请求
 */
export interface StartMarkingRequest {
  /**
   * Style编号
   */
  styleNo: string

  /**
   * 其他参数
   */
  [property: string]: any
}

/**
 * AI打标响应
 */
export interface StartMarkingResp {
  /**
   * 任务ID
   */
  taskId?: string
  /**
   * 状态
   */
  status?: string
  /**
   * 消息
   */
  message?: string

  [property: string]: any
}

/**
 * 打标结果统计响应
 */
export interface MarkingResultResp {
  /**
   * 一级标签
   */
  category?: string
  /**
   * 二级标签
   */
  subcategory?: string
  /**
   * 三级标签
   */
  thirdLevel?: string
  /**
   * 优点描述
   */
  description?: string
  /**
   * 数量
   */
  count?: number
  /**
   * 百分比
   */
  percentage?: string

  [property: string]: any
}

/**
 * Review详细数据响应
 */
export interface ReviewDetailResp {
  /**
   * 评论原文
   */
  reviewText?: string
  /**
   * 优点标签数组
   */
  advantages?: string[]
  /**
   * 缺点标签数组
   */
  disadvantages?: string[]
  /**
   * 评分
   */
  rating?: number
  /**
   * 评论日期
   */
  date?: string

  [property: string]: any
}

/**
 * 产品信息响应
 */
export interface ProductInfoResp {
  /**
   * Style编号
   */
  styleNo?: string
  /**
   * 产品图片URL
   */
  styleImg?: string
  /**
   * 评论数量
   */
  reviewCount?: number
  advantageList?: AdvantageItem[]
  defectList?: AdvantageItem[]

  [property: string]: any
}

export interface AdvantageItem {
  firstLevelTag?: string
  secondLevelTag?: string
  thirdLevelTag?: string
  prosConsDesc?: string
  prosCons?: string
  styleNo?: string
  /**
   * 频率
   */
  frequency?: number
  /**
   * 百分比
   */
  percentage?: string

  [property: string]: any
}

/**
 * QueryLWVocStatusResp
 */
export interface QueryLWVocStatusResp {
  /**
   * 正在分析中的报告数量
   */
  analyzingCount?: number
  /**
   * 当前报告状态,analyzing-分析中,success-分析成功,fail-分析失败
   */
  reportStatus?: string
  /**
   * 失败原因描述
   */
  statusDesc?: string

  [property: string]: any
}

// 查询最新的10条打标结果数据
export function queryOldestVoc() {
  return get<QueryOldestLWVocResp>({
    url: `/lightWeightVoc/queryOldestVoc`,
  },)
}

// 查看Review以及打标结果
export function queryReviewVoc(data: VocRequest,) {
  return postJSON<NewResponseData<ReviewDetailResp>>({
    url: `/lightWeightVoc/queryReviewVoc`,
    data,
  },)
}

// 查询打标统计结果
export function queryTagVoc({ styleNo, }: { styleNo: string },) {
  return postJSON<NewResponseData<QueryLWReviewVocResp>>({
    url: `/lightWeightVoc/queryTagVoc`,
    data: {
      styleNo,
    },
  },)
}

// 查询voc轻量报告状态，以及分析中的报告数
export function queryVocStatus(params: VocRequest,) {
  return get<NewResponseData<QueryOldestLWVocResp>>({
    url: `/lightWeightVoc/queryVocStatus`,
    params,
  },)
}

// 生成voc轻量报告
export function generateReport(params: VocRequest,) {
  return get<NewResponseData<QueryOldestLWVocResp>>({
    url: `/lightWeightVoc/generateReport`,
    params,
  },)
}
