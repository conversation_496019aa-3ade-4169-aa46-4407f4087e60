import { get, postJSON, } from "@/utils/fetch.ts"

/**
 * QueryLWVocReq
 */
export interface VocRequest {
  /**
   * id,id和styleNo至少一个不为空
   */
  id?: number
  reportDate?: string
  /**
   * styleNo
   */
  styleNo?: string

  [property: string]: any
}

export interface QueryOldestLWVocResp {
  /**
   * 提交时间
   */
  createTime?: string
  /**
   * id
   */
  id?: number
  /**
   * 完成时间
   */
  reportFinishTime?: string
  /**
   * Style编号
   * style
   */
  styleNo?: string

  [property: string]: any
}

export interface QueryLWReviewVocResp {
  /**
   * 优点描述
   */
  advantage?: string
  /**
   * 缺点描述
   */
  defect?: string
  /**
   * 评论原文
   */
  reviewText?: string

  [property: string]: any
}

/**
 * 最新打标结果记录响应
 */
export interface LatestMarkingRecordsResp {
  /**
   * 记录ID
   */
  id?: number
  /**
   * Style编号
   */
  style?: string
  /**
   * 提交时间
   */
  submitTime?: string
  /**
   * 完成时间
   */
  completeTime?: string
  /**
   * 评论数据文件名
   */
  reviewDataFile?: string
  /**
   * 描述信息
   */
  description?: string

  [property: string]: any
}

/**
 * AI打标请求
 */
export interface StartMarkingRequest {
  /**
   * Style编号
   */
  styleNo: string
  /**
   * 其他参数
   */
  [property: string]: any
}

/**
 * AI打标响应
 */
export interface StartMarkingResp {
  /**
   * 任务ID
   */
  taskId?: string
  /**
   * 状态
   */
  status?: string
  /**
   * 消息
   */
  message?: string

  [property: string]: any
}

/**
 * 打标结果统计响应
 */
export interface MarkingResultResp {
  /**
   * 一级标签
   */
  category?: string
  /**
   * 二级标签
   */
  subcategory?: string
  /**
   * 三级标签
   */
  thirdLevel?: string
  /**
   * 优点描述
   */
  description?: string
  /**
   * 数量
   */
  count?: number
  /**
   * 百分比
   */
  percentage?: string

  [property: string]: any
}

/**
 * Review详细数据响应
 */
export interface ReviewDetailResp {
  /**
   * 评论原文
   */
  reviewText?: string
  /**
   * 优点标签数组
   */
  advantages?: string[]
  /**
   * 缺点标签数组
   */
  disadvantages?: string[]
  /**
   * 评分
   */
  rating?: number
  /**
   * 评论日期
   */
  date?: string

  [property: string]: any
}

/**
 * 产品信息响应
 */
export interface ProductInfoResp {
  /**
   * Style编号
   */
  style?: string
  /**
   * 产品图片URL
   */
  image?: string
  /**
   * 评论数量
   */
  reviewCount?: number
  /**
   * 价格
   */
  price?: string
  /**
   * 目标数据链接
   */
  targetDataUrl?: string

  [property: string]: any
}

/**
 * QueryLWVocStatusResp
 */
export interface QueryLWVocStatusResp {
  /**
   * 正在分析中的报告数量
   */
  analyzingCount?: number
  /**
   * 当前报告状态,analyzing-分析中,success-分析成功,fail-分析失败
   */
  reportStatus?: string
  /**
   * 失败原因描述
   */
  statusDesc?: string

  [property: string]: any
}

// 查询最新的10条打标结果数据
export function queryOldestVoc() {
  return get<QueryOldestLWVocResp>({
    url: `/lightWeightVoc/queryOldestVoc`,
  },)
}

// 查看Review以及打标结果
export function queryReviewVoc() {
  return postJSON<QueryLWReviewVocResp>({
    url: `/lightWeightVoc/queryReviewVoc`,
  },)
}

// 查询打标统计结果
export function queryTagVoc() {
  return postJSON<QueryLWReviewVocResp>({
    url: `/lightWeightVoc/queryTagVoc`,
  },)
}

// 查询voc轻量报告状态，以及分析中的报告数
export function queryVocStatus() {
  return get<QueryOldestLWVocResp>({
    url: `/lightWeightVoc/queryVocStatus`,
  },)
}

// 生成voc轻量报告
export function generateReport() {
  return get<QueryOldestLWVocResp>({
    url: `/lightWeightVoc/generateReport`,
  },)
}

// ==================== VocMarking页面相关接口 ====================

/**
 * 获取最新打标结果记录列表
 */
export function getLatestMarkingRecords() {
  return get<LatestMarkingRecordsResp[]>({
    url: `/vocMarking/getLatestRecords`,
  })
}

/**
 * 开始AI打标
 */
export function startAIMarking(data: StartMarkingRequest) {
  return postJSON<StartMarkingResp>({
    url: `/vocMarking/startMarking`,
    data,
  })
}

/**
 * 查询打标状态
 */
export function queryMarkingStatus(taskId: string) {
  return get<QueryLWVocStatusResp>({
    url: `/vocMarking/queryStatus`,
    params: { taskId },
  })
}

/**
 * 获取打标结果统计数据
 */
export function getMarkingResults(styleNo: string) {
  return get<MarkingResultResp[]>({
    url: `/vocMarking/getResults`,
    params: { styleNo },
  })
}

/**
 * 获取Review详细数据
 */
export function getReviewDetails(styleNo: string) {
  return get<ReviewDetailResp[]>({
    url: `/vocMarking/getReviewDetails`,
    params: { styleNo },
  })
}

/**
 * 获取产品信息
 */
export function getProductInfo(styleNo: string) {
  return get<ProductInfoResp>({
    url: `/vocMarking/getProductInfo`,
    params: { styleNo },
  })
}

/**
 * 下载文件
 */
export function downloadFile(fileName: string) {
  return get<Blob>({
    url: `/vocMarking/downloadFile`,
    params: { fileName },
    responseType: 'blob',
  })
}

/**
 * 查看Style结果（点击最新记录中的Style）
 */
export function viewStyleResults(styleNo: string) {
  return get<{
    productInfo: ProductInfoResp
    markingResults: MarkingResultResp[]
  }>({
    url: `/vocMarking/viewStyleResults`,
    params: { styleNo },
  })
}
