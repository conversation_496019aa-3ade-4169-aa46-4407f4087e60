import { IMPORT_META_ENV, } from "@/constants"
import { fetch, postJSON, } from "@/utils/fetch.ts"
import to from "await-to-js"

export namespace OMSExportAPI {
  export interface Params {
    appName?: string
    exportType: string
    reqParam?: string
  }
  export type Response = NewBasicResponseData
  export type Request = Params
}
/**
 * PageSort
 */
export interface PageSort {
  /**
   * 是否正排序？true是，false否
   */
  ascField?: boolean
  /**
   * 排序的字段
   */
  field?: string
  [property: string]: any
}
/**
 * ExportTaskFilterDTO
 */
export interface ExportRequest {
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人
   */
  createById?: number
  /**
   * 创建时间
   */
  createTimeEnd?: Date
  /**
   * 创建时间
   */
  createTimeStart?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 主键
   */
  idList?: number[]
  /**
   * 请求requestId
   */
  requestId?: string
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * state
   */
  state?: number
  [property: string]: any
}
export interface ExportTaskDTO {
  /**
   * 创建人userId
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: boolean
  /**
   * 错误消息
   */
  errorMsg?: string
  /**
   * 失败次数
   */
  hasRetryCount?: number
  /**
   * id
   */
  id?: number
  /**
   * 修改人userId
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 模板名称
   */
  moduleName?: string
  /**
   * 是否需要飞书通知创建人: 0不通知， 1通知
   */
  needFeishuNotice?: number
  /**
   * 下载URL
   */
  ossUrl?: string
  /**
   * 请求唯一字符串
   */
  requestId?: string
  /**
   * 计划状态：(待执行-0、执行中-1、执行成功-2、执行失败-9)
   */
  state?: number
  /**
   * 计划状态：(待执行-0、执行中-1、执行成功-2、执行失败-9)
   */
  stateI18?: string
  [property: string]: any
}
export function executeExport(data: OMSExportAPI.Request,) {
  return to<OMSExportAPI.Response>(fetch.post("/pdm-export/execute-export/execute", data, {
    baseURL: IMPORT_META_ENV.VITE_DOWNLOAD_URL,
  },),)
}
// 公共导出服务
export function exportPageApi(data: ExportRequest,) {
  return postJSON<NewPageResponseData<ExportTaskDTO>>({
    url: "/export/page",
    data,
  },)
}
