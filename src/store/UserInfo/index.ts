import type { UserInfoAPI, } from "@/apis/userInfo"
import { clientLogout, fetchUserInfo, } from "@/apis/userInfo"
import { defineStore, } from "pinia"

export const useUserStore = defineStore("userInfo", {
  state: (): UserInfoAPI.UserInfo => {
    return {
      userId: "",
      name: "",
      avatarUrl: "",
      email: "",
    }
  },
  actions: {
    removeUserInfo() {
      this.$state = {
        userId: "",
        name: "",
        avatarUrl: "",
        email: "",
      }
    },
    async getUserInfo() {
      const result = await fetchUserInfo()
      if (result && result.data) {
        this.$state = result.data
        result.data.name && window._paq?.push(["setUserId", result.data.name,],) // 埋点-名字
        return true
      }
      return false
    },
    async logoutConfirm() {
      const result = await clientLogout()
      const { removeToken, } = useToken()
      if (result && result.data) {
        this.removeUserInfo()
        removeToken()
        location.replace(result.data.loginPage,)
      }
    },
  },
},)
