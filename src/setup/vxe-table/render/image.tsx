import type { VxeGlobalRendererHandles, } from "vxe-table"
import { ElImage, } from "element-plus"
import { VxeUI, } from "vxe-table"
import XEUtils from "xe-utils"

export interface UploadValue {
  /**
   * 文件名
   */
  fileName?: string
  /**
   * 文件相对路径
   */
  fileUrl?: string
  id?: number
  uid?: number
  /**
   * 文件签名下载地址
   */
  signatureUrl?: string
  /**
   * 文件类型
   */
  fileType?: string
}

interface Props {
  imageList?: string[]
  imageStyle?: CSSStyleSheet
  wrapStyle?: CSSStyleSheet
  imageClass?: string
  wrapClass?: string
}

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: Props
}

VxeUI.renderer.add("Image", {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams,
  ) {
    const { row, column, } = params
    const { props, } = renderOpts
    const style = {
      width: "60px",
      height: "60px",
    }
    const property: string | string[] | UploadValue | UploadValue[] = XEUtils.get(row, column.field,)
    let src: string | undefined
    const imageList = props?.imageList || []
    if (typeof property === "string") {
      src = property
      if (!imageList.length) {
        imageList.push(property,)
      }
    } else if (Array.isArray(property,) && property?.length > 0) {
      const item = property[0]
      if (typeof item === "string") {
        src = item
        if (!imageList.length) {
          imageList.push(...(property as string[]),)
        }
      } else if (item.signatureUrl) {
        src = item.signatureUrl
        if (!imageList.length) {
          imageList.push(...(property as UploadValue[]).map((e: UploadValue,) => e.signatureUrl!,),)
        }
      }
    } else if (property && "signatureUrl" in property) {
      src = property.signatureUrl
      if (!imageList.length) {
        imageList.push(property?.signatureUrl,)
      }
    }

    return [
      src
        ? (
            <div class={props?.wrapClass || "flex items-center justify-center"}>
              <ElImage
                src={src}
                class={props?.imageClass}
                previewSrcList={imageList}
                fit="contain"
                hide-on-click-modal
                loading="lazy"
                previewTeleported={true}
                style={props?.imageStyle || style}
              />
            </div>
          )
        : (
            <span></span>
          ),
    ]
  },
},)
