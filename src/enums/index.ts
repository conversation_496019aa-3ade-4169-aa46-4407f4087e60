// start-启用,ban-禁止

/**
 * 标签启用警用
 */
export enum LabelStatusEnum {
  START = "start",
  BAN = "ban",
}
export enum DimensionsEnums {
  asin = "asin",
  style = "style",
  category = "category",
}
export enum StatusEnum {
  analyzing = "analyzing",
  fail = "fail",
  success = "success",
}
export enum respotTypeEnum {
  single = "single",
  multi = "multi",
}
export enum CommentTypeEnum {
  advantage = "advantage",
  shortcoming = "shortcoming",
}
