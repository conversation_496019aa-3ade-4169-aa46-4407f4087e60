import type { OMSExportAPI, } from "@/apis/export"
import { executeExport, } from "@/apis/export"
import router from "@/setup/vue-router"
import { ElButton, } from "element-plus"

function handleJump() {
  const { href, } = router.resolve({
    path: "/export/index",
  },)
  window.open(href, "_blank",)
}

export async function useExportMsg() {
  await ElMessageBox({
    type: "success",
    title: "导出成功",
    showCancelButton: false,
    showConfirmButton: true,
    message: () => {
      return (
        <div class="flex items-center">
          <span>导出成功，请到</span>
          <ElButton
            type="primary"
            class="!p-0"
            link
            onClick={() => {
              handleJump()
            }}
          >
            下载中心
          </ElButton>
          <span>查看</span>
        </div>
      )
    },
  },)
}

export function useHandleExport() {
  const loading = ref(false,)
  const handleExport = async(data: OMSExportAPI.Request,) => {
    loading.value = true
    const [error, result,] = await executeExport({
      ...data,
      appName: "plm-aidc",
    },)
    loading.value = false
    if (error === null && result) {
      await useExportMsg()
    }
  }

  return {
    loading,
    handleExport,
  }
}
