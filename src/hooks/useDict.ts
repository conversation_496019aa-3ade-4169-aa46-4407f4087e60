import type { Dict<PERSON><PERSON>, Dict<PERSON><PERSON>, } from "@/apis/dict"
import { getDictList, } from "@/apis/dict"
import { transformOptionsToMap, } from "@/utils"

type AppendSuffix<S extends string, Suffix extends "List" | "Map", > = `${S}${Suffix}`

type DictList = {
  [key in AppendSuffix<DictKey, "List">]?: DictAPI.DictValue[]
}

type DictMap = {
  [key in AppendSuffix<DictKey, "Map">]?: Record<string, string | number | undefined>
}

export type DictState = DictList & DictMap & {
  fetching: boolean
  fetched: boolean
}

export const dictState = reactive<DictState>({
  fetching: false,
  fetched: false,
},)

export function useDict() {
  async function fetchDictList() {
    if (dictState.fetching || dictState.fetched) {
      return dictState
    }
    dictState.fetching = true
    const result = await getDictList()
    dictState.fetching = false
    if (result?.data) {
      dictState.fetched = true
      const { data, } = result
      const dictKeysMap: {} = {}
      const dictKeys: DictKey[] = []
      data.forEach((item: DictAPI.Data,) => {
        if (item.dictItem) {
          if (!dictKeysMap[item.dictItem]) {
            dictKeys.push(item.dictItem,)
            dictKeysMap[item.dictItem] = []
          }
          dictKeysMap[item.dictItem] = item.dictValueList || []
        }
      },)
      dictKeys.forEach((item,) => {
        const key = item
        if (key) {
          dictState[`${key}List`] = dictKeysMap[item]
          dictState[`${key}Map`] = transformOptionsToMap(dictKeysMap[item] as Record<string, string>[], {
            keyField: "value",
            valueField: "label",
          },)
        }
      },)
    }
    return dictState
  }

  fetchDictList()

  return {
    dictState,
    fetchDictList,
  }
}
