interface ExportData {
  onInit: () => void
  onDestroy: () => boolean | void
  fileList: Ref<File[]>
}

function useCopyFile(): ExportData {
  const fileList = ref<File[]>([],)

  const handle = (event: any,) => {
    if ((event.ctrlKey || event.metaKey) && event.key === "v") {
      // 模拟粘贴事件，以便立即触发粘贴处理逻辑
      let el: HTMLElement | null = document.getElementById("pasteArea",)
      if (!el) {
        el = document.createElement("textarea",)
        el.id = "pasteArea"
        el.style.width = "0"
        el.style.height = "0"
        el.style.position = "absolute"
        el.style.zIndex = "-1"
        el.style.left = "0"
        el.style.top = "0"
        document.body.appendChild(el,)
      }
      el.focus()

      el.onpaste = (event,) => {
        event.preventDefault()
        const clipboardData = event.clipboardData
        const items = clipboardData?.items ?? []
        for (let i = 0; i < items.length; i++) {
          if (items[i].type.includes(".sheet",) || items[i].type.includes(".ms-excel",)) {
            const blob = items[i].getAsFile()
            if (!blob) {
              continue
            }
            fileList.value.push(blob,)
          }
        }
      }

      el.dispatchEvent(new Event("paste",),)
    }
  }

  const onInit = () => {
    document.addEventListener("keydown", handle,)
  }

  const onDestroy = (): boolean | void => {
    document.removeEventListener("keydown", handle,)
    const el: HTMLElement | null = document.getElementById("pasteArea",)
    if (!el) {
      return false
    }
    el.onpaste = null
  }

  return {
    onInit,
    onDestroy,
    fileList,
  }
}

export { useCopyFile, }
