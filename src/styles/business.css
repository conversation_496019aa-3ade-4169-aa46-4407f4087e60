:root {
  /* 主题色 */
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  /* 单元格左右padding*/
  --cell-padding-x: 12px;

  /* 单元格文字高度*/
  --cell-line-height: 21px;
  --el-menu-item-height:48px;
  /* 文字颜色*/
  --primary-text-color: #606266;
  --regular-text-color: #646464;
  --secondary-text-color: #909399;
  --placeholder-text-color: #A8ABB2;
  --el-text-color-regular:#969696;
  /* 字体*/
  --font-family: Inter, 'Helvetica Neue', Helvetica, 'PingFang SC',
  'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;

  /* 字体大小*/
  --font-size-3xl: 32px;
  --font-size-2xl: 28px;
  --font-size-xl: 24px;
  --font-size-lg: 18px;
  --font-size-base:14px;
  --font-size-sm: 14px;
  --font-size-xs: 12px;

  /* 行高 */
  --line-height-base: 1.5;

}


:root {
  --el-color-primary: var(--primary-color);
  --el-color-success: var(--success-color);
  --el-color-warning: var(--warning-color);
  --el-color-danger: var(--danger-color);

  --el-font-size-extra-large: var(--font-size-2xl);
  --el-font-size-large: var(--font-size-xl);
  --el-font-size-medium: var(--font-size-lg);
  --el-font-size-base: var(--font-size-base);
  --el-font-size-small: var(--font-size-sm);
  --el-font-size-extra-small: var(--font-size-xs);

  --vxe-ui-font-primary-color: var(--primary-color);
  --vxe-ui-font-size-default: var(--font-size-base);
  --vxe-ui-table-cell-padding-left: var(--cell-padding-x);
  --vxe-ui-table-cell-padding-right: var(--cell-padding-x);
  --vxe-ui-font-color: var(--regular-text-color);
  --vxe-ui-table-header-font-color: var(--primary-text-color);
  --vxe-ui-table-row-line-height: var(--cell-line-height);
  --vxe-ui-font-family: var(--font-family);
  --vxe-ui-status-success-color: var(--success-color);
  --vxe-ui-status-info-color: var(--info-color);
  --vxe-ui-status-warning-color: var(--warning-color);
  --vxe-ui-status-danger-color: var(--danger-color);
  --vxe-ui-status-error-color: var(--danger-color);
  --vxe-ui-input-placeholder-color: var(--placeholder-text-color);
  --vxe-ui-font-placeholder-color: var(--placeholder-text-color);
  --vxe-ui-font-secondary-color: var(--secondary-text-color);
  --vxe-ui-layout-background-color: var(--el-bg-color);

}