:root {

  /* left menu start */
  --left-menu-max-width: 264px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: white;

  --left-menu-bg-light-color: white;

  --left-menu-bg-active-color: var(--el-color-primary);
  --el-menu-item-height:48px;
  --el-menu-item-font-size:var(--el-font-size-small);
  --left-menu-text-color:#303133;
  --el-menu-hover-bg-color: var(--el-color-primary);

  --left-menu-text-active-color: #fff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-width: 126px;
  --logo-height:64px;
  --logo-inner-height:26px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: #fff;

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 60px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 20px;

  --app-content-bg-color: rgb(245,247,250);

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;
}

.dark {
  --app-content-bg-color: var(--el-bg-color);
}

*,
:after,
:before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
