<script lang="tsx">
import { useDesign, } from "@/hooks/useDesign"
import Collapse from "@/layout/components/Collapse.vue"
import TagView from "@/layout/components/TagView.vue"
import { useAppStore, } from "@/store/App"
import { computed, } from "vue"
import LocaleDropdown from "./LocaleDropdown.vue"
import Screenfull from "./Screenfull.vue"
import SizeDropdown from "./SizeDropdown.vue"
import UserInfo from "./UserInfo.vue"

const { getPrefixCls, variables, } = useDesign()

const prefixCls = getPrefixCls("tool-header",)

export default defineComponent({
  name: "ToolHeader",
  setup() {
    const appStore = useAppStore()
    // 标签页
    const tagsView = computed(() => appStore.getTagsView,)
    // 全屏图标
    const screenfull = computed(() => appStore.getScreenfull,)

    // 尺寸图标
    const size = computed(() => appStore.getSize,)

    // 折叠图标
    const hamburger = computed(() => appStore.getHamburger,)

    // 多语言图标
    const locale = computed(() => appStore.getLocale,)
    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          "h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between",
        ]}
      >
        {hamburger.value
          ? (
              <Collapse class="custom-hover" style="padding-top:20px" color="var(--top-header-text-color)"></Collapse>
            )
          : undefined}
        {tagsView.value
          ? (
              <TagView></TagView>
            )
          : undefined}
        <div class="h-full flex items-center">
          {screenfull.value
            ? (
                <Screenfull class="custom-hover" color="var(--top-header-text-color)"></Screenfull>
              )
            : undefined}
          {size.value
            ? (
                <SizeDropdown class="custom-hover" color="var(--top-header-text-color)"></SizeDropdown>
              )
            : undefined}
          {locale.value
            ? (
                <LocaleDropdown
                  class="custom-hover"
                  color="var(--top-header-text-color)"
                >
                </LocaleDropdown>
              )
            : undefined}
          <UserInfo></UserInfo>
        </div>
      </div>
    )
  },
},)
</script>

<style lang="scss" scoped>
@use "@/styles/variables.module.scss" as *;

$prefix-cls: "#{$adminNamespace}-tool-header";

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
  box-shadow: 0 4px 20px 0 rgba(0,0,0,0.04);
}
</style>
