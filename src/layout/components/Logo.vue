<script setup lang="ts">
import { useDesign, } from "@/hooks/useDesign"
import { useAppStore, } from "@/store/App"

const { getPrefixCls, } = useDesign()

const prefixCls = getPrefixCls("logo",)

const appStore = useAppStore()

const show = ref(true,)

const layout = computed(() => appStore.getLayout,)

const collapse = computed(() => appStore.getCollapse,)

onMounted(() => {
  if (unref(collapse,)) {
    show.value = false
  }
},)

watch(
  () => collapse.value,
  (collapse: boolean,) => {
    if (unref(layout,) === "topLeft" || unref(layout,) === "cutMenu") {
      show.value = true
      return
    }
    show.value = !collapse
  },
)

watch(
  () => layout.value,
  (layout,) => {
    if (layout === "top" || layout === "cutMenu") {
      show.value = true
      return
    }
    show.value = !unref(collapse,)
  },
)
</script>

<template>
  <div class="__inner">
    <RouterLink
      :class="[
        prefixCls,
        layout !== 'classic' ? `${prefixCls}__Top` : '',
        show ? 'pl-4' : '',
      ]"
      class="relative flex cursor-pointer items-center overflow-hidden decoration-none !h-[var(--logo-height)]"
      to="/"
    >
      <img
        v-show="show"
        alt=""
        class="h-[calc(var(--logo-inner-height))]"
        src="@/assets/imgs/logo.png"
      />
      <div v-show="show" class="ml-10px text-14px font-700" style="color: rgb(48, 49, 51,0.8)">
        AI数据洞察中心
      </div>
      <div v-show="!show" class="ml-10px text-14px font-700" style="color: rgb(48, 49, 51,0.8)">
        AIDC
      </div>

      <!--      <div -->
      <!--        :class="[ -->
      <!--          { -->
      <!--            'text-[var(&#45;&#45;logo-title-text-color)]': layout === 'classic', -->
      <!--            'text-[var(&#45;&#45;top-header-text-color)]': -->
      <!--              layout === 'topLeft' || layout === 'top' || layout === 'cutMenu', -->
      <!--          }, -->
      <!--        ]" -->
      <!--        class="ml-10px text-16px font-700" -->
      <!--      > -->
      <!--        {{ title }} -->
      <!--      </div> -->
    </RouterLink>
  </div>
</template>
