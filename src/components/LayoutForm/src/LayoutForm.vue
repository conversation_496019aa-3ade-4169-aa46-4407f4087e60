<script lang="ts" setup>
import type { Props as DescriptionsProps, } from "@/components/Descriptions"
import type { FormInstance, FormProps, } from "element-plus"
import type { Slots, VNodeChild, } from "vue"
import to from "await-to-js"
import { omit, pick, } from "lodash-es"
import { Fragment, } from "vue"

defineOptions({
  name: "LayoutForm",
},)
const props = withDefaults(defineProps<Props>(), {
  span: 6,
  scrollToError: true,
  scrollIntoViewOptions: () => ({
    behavior: "smooth",
    block: "center",
    inline: "center",
  }),
},)

const emits = defineEmits<{
  (e: "search"): void
  (e: "reset"): void
}>()

type Props = Partial<FormProps> & DescriptionsProps & {
  /**
   * 是否是查询表单
   */
  queryForm?: boolean
  /**
   * loading
   */
  loading?: boolean
  /**
   * span
   */
  span?: number
}

const attrs: Record<string, unknown> = useAttrs()

const formRef = ref<FormInstance>()

const { default: defaultSlot, } = useSlots() as Slots

const formProps = computed(() => {
  const { labelPosition, labelWidth, } = props
  const omitKeys: (keyof DescriptionsProps)[] = ["descriptions", "title", "message", "collapse", "border", "labelSuffix", "descriptions", "data",]
  const omitProps = omit(props, omitKeys,)
  return {
    ...attrs,
    ...omitProps,
    labelPosition: labelPosition || "top",
    labelWidth: labelWidth || "auto",
  }
},)

const descriptionsProps = computed(() => {
  const pickKeys: (keyof DescriptionsProps)[] = ["descriptions", "title", "message", "collapse", "border", "labelSuffix", "descriptions", "data",]
  return pick(props, pickKeys,)
},)

const collapse = ref(true,)
const totalSpan = computed(() => {
  // 获取插槽节点数组，并过滤无效节点
  const vnodes = getSlot()?.filter(vnode => !!vnode,)
  // 定义默认 span 值（优先组件 prop，若未设置则默认为 1）
  const defaultSpan = props.span ?? 1

  // 计算累加值（初始值为 0，避免重复累加默认值）
  return vnodes.reduce((sum: number, vnode,) => {
    // 安全获取节点 span 属性（支持 number 或 string 类型）
    const nodeSpan = Number((vnode as VNode).props?.span,) || defaultSpan
    return sum + nodeSpan
  }, 0,) + defaultSpan // 添加全局默认值到总和
},)
const isCollapseNeeded = computed(() => (totalSpan.value > 24 && props?.queryForm),)

const btnOffset = computed(() => {
  const _totalSpan = totalSpan.value
  if (_totalSpan - props.span === 24 || collapse.value) {
    return 0
  }
  const rowCount = Math.ceil(_totalSpan / 24,)

  return rowCount * 24 - _totalSpan
},)
function getSlot() {
  const defaultSlots = defaultSlot?.() || []
  return defaultSlots.flatMap((item: VNode,) => {
    // 检测 Fragment 节点（兼容 Vue 3 的 Symbol(v-fgt)）
    const isFragment = (item?.type === Fragment)

    // 处理 Fragment 的子节点
    if (isFragment && Array.isArray(item.children,)) {
      return item.children.filter((node: VNodeChild,) => {
        const vnode = node as VNode & { props?: Record<string, unknown> }
        return vnode.props && Object.keys(vnode.props,).length > 0
      },)
    }
    // 过滤无效节点（无 props 的非 Fragment 节点）
    return item?.props ? [item,] : []
  },)
}
const visibleFields = computed(() => {
  // 获取处理后插槽内容
  const slots = getSlot() as VNode[]
  // 如果不需要折叠或已展开，则显示所有字段
  if (!isCollapseNeeded.value || !collapse.value) {
    return slots
  }
  // 计算第一行可显示的字段
  const firstRowFields: VNode[] = []
  let sum = 0

  for (const vnode of slots) {
    const span = vnode.props?.span || props.span
    // 如果加上当前字段后超出一行，则停止添加
    if (sum + span > 24 - props.span) {
      break
    }
    firstRowFields.push(vnode,)
    sum += span
  }

  return firstRowFields
},)

async function validate() {
  if (!formRef.value) {
    return false
  }
  const [error, result,] = await to(formRef.value?.validate(),)
  if (error === null && result) {
    return result
  }
  return false
}
defineExpose({
  formRef,
  validate,
},)
</script>

<template>
  <ElForm
    v-if="!props.disabled"
    ref="formRef"
    v-bind="formProps"
  >
    <ElRow
      :gutter="20"
      class="form-row"
      justify="start"
      type="flex"
    >
      <!-- 表单项 -->
      <template v-for="(vnode, index) in visibleFields" :key="index">
        <ElCol :span="vnode.props?.span || props.span" class="form-field">
          <slot :name="`field-${index}`">
            <component :is="vnode" />
          </slot>
        </ElCol>
      </template>

      <!-- 按钮组 -->
      <ElCol
        v-if="queryForm"
        :offset="btnOffset"
        :span="props.span"
        class="btn-group"
      >
        <ElFormItem label="按钮组">
          <ElButton :loading="loading" @click="emits('reset')">
            <Icon class="mr-0.5" icon="ep:refresh-right" />
            重置
          </ElButton>
          <ElButton
            :loading="loading"
            type="primary"
            @click="emits('search')"
          >
            <Icon class="mr-0.5" icon="ep:search" />
            搜索
          </ElButton>
          <ElButton
            v-if="isCollapseNeeded"
            type="primary"
            link
            @click="collapse = !collapse"
          >
            {{ collapse ? "展开" : "收起" }}
            <Icon
              :class="collapse ? '' : 'rotate-180'"
              class="transform transition duration-300"
              icon="ep:arrow-down"
            />
          </ElButton>
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
  <Descriptions v-else v-bind="descriptionsProps" />
</template>

<style scoped>
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-leave-active {
  position: absolute;
}

:deep(.el-form-item--label-top .el-form-item__label) {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-base);
  color:var(--regular-text-color)
}
:deep(.btn-group){
  .el-form-item__label{
    visibility: hidden;
  }
}
</style>
