<script lang="ts" setup>
import { ElCard, ElTooltip, } from "element-plus"

defineProps<Props>()

const { getPrefixCls, } = useDesign()

const prefixCls = getPrefixCls("content-wrap",)
interface Props {
  title?: string
  message?: string
}
</script>

<template>
  <ElCard :class="`content-wrap ${[prefixCls]}`" shadow="never">
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-[16px] font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-[200px]">
              {{ message }}
            </div>
          </template>
          <Icon :size="14" class="ml-[5px]" icon="bi:question-circle-fill" />
        </ElTooltip>
      </div>
    </template>
    <div>
      <slot />
    </div>
  </ElCard>
</template>

<style lang="scss" scoped>
.content-wrap{
  background-color: #fff;
  height:100%;
  overflow-y: auto;
  width:100%;
  border: none;
  border-radius:8px;
  padding:16px;
}
:deep(.el-card__body){
  padding:0;
}
</style>
