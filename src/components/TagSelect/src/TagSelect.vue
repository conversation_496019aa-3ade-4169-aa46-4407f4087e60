<script setup lang="ts">
import { STYLE_LIST, } from "@/components/TagSelect/src/const.ts"
import { ArrowDown, Close, } from "@element-plus/icons-vue"

interface optionData {
  label?: string
  value?: any
}

interface StyleData {
  color: string
  backgroundColor: string
}

export type TagData = optionData & { style?: StyleData }

const props = defineProps<{
  options: GroupData[]
  showClose?: boolean
  defaultData?: TagData
}>()

const emits = defineEmits<{
  (e: "close"): void
  (e: "change", data: TagData): void
}>()

const visible = ref<boolean>(false,)

export interface GroupData {
  group?: number
  groupName: string
  style?: StyleData
  options: optionData[]
}
const currentData = ref<TagData | null>(null,)
const filterStr = ref<string>("",)

watch(() => props.defaultData, (val,) => {
  if (!val) {
    return false
  }
  currentData.value = JSON.parse(JSON.stringify(val,),)
}, {
  immediate: true,
},)

// 样式本地保存
const storage = localStorage.getItem("styleData",)

const formatOptions = computed<GroupData[]>(() => {
  const styleData = storage ? JSON.parse(storage,) : {}

  return (props.options ?? []).map((item, index,) => {
    let style = item.style ?? STYLE_LIST[index % STYLE_LIST.length]
    if (item.group && styleData[item.group]) {
      style = styleData[item.group]
    } else if (item.group) {
      const newData = Object.assign({}, styleData, {
        [item.group]: style,
      },)
      localStorage.setItem("styleData", JSON.stringify(newData,),)
    }
    return {
      ...item,
      options: (item.options ?? []).filter((item,) => {
        return !filterStr.value ? true : item.label?.toLowerCase().includes(filterStr.value.toLowerCase(),)
      },),
      style,
    }
  },)
},)

function onChange(item: optionData, group: GroupData,) {
  const data = {
    ...item,
    style: group.style ?? STYLE_LIST[0],
  }
  emits("change", JSON.parse(JSON.stringify(data,),),)
  currentData.value = data
  visible.value = false
}

function onClose() {
  emits("close",)
}
</script>

<template>
  <span :style="{ backgroundColor: currentData?.style?.backgroundColor ?? STYLE_LIST[0].backgroundColor }" class="tag-select">
    <ElPopover
      v-model:visible="visible"
      :width="400"
      placement="bottom"
      trigger="click"
    >
      <template #reference>
        <div class="select-trigger">
          <span :style="{ color: currentData?.style?.color ?? STYLE_LIST[0].color }" class="tag-select-text">
            {{ currentData?.label ?? '请选择' }}
          </span>
          <ElIcon><ArrowDown /></ElIcon>
        </div>
      </template>
      <div class="tag-select-options">
        <template v-if="formatOptions?.length">
          <div class="tag-select-options-inner">
            <div class="tag-select-filter">
              <ElInput v-model="filterStr" placeholder="请输入" clearable />
            </div>
            <div v-for="group in formatOptions" :key="group.groupName ?? group.group" class="tag-select-group">
              <div class="group-name">
                <span>{{ group.groupName ?? group.group }}</span>
                <div :style="{ backgroundColor: group.style?.backgroundColor }" class="group-style">
                  <div :style="{ backgroundColor: group.style?.color }" class="group-style-inner" />
                </div>
              </div>
              <div class="item-options">
                <div
                  v-for="item in group.options"
                  :key="item.value"
                  class="tag-select-option"
                  @click="onChange(item, group)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="select-empty">
            无展示数据
          </div>
        </template>
      </div>
    </ElPopover>
    <ElIcon v-if="showClose" class="tag-close" @click="onClose"><Close /></ElIcon>
  </span>
</template>

<style scoped lang="scss">
.tag-select {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  color: #ACACAC;
}

.select-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  &:hover {
    cursor: pointer;
  }
}

.tag-close {
  &:hover {
    cursor: pointer;
  }
}

.tag-select-options {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 320px;
  overflow-y: auto;
}

.tag-select-options-inner {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tag-select-filter {
  width: 100%;
  overflow: hidden;
}

.group-name {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ACACAC;
  font-size: 16px;
  font-weight: 700;
}

.group-style {
  width: 36px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  &-inner {
    width: 24px;
    height: 12px;
  }
}

.item-options {
  position: relative;
  font-size: 14px;
  padding-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag-select-option {
  padding: 4px 16px;
  border-radius: 4px;
  &:hover {
    cursor: pointer;
    background-color: rgb(121.3, 187.1, 255);
    color: #fff;
  }
}
</style>
