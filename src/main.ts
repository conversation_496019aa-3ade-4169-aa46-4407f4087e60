import { setupPermission, } from "@/directives"
import { setupAuth, setupI18n, setup<PERSON><PERSON>, setupRouter, setupVxeTable, } from "@/setup"
import { setupMatomo, } from "@/setup/vue-matomo"
import router from "@/setup/vue-router"
import { ElLoading, } from "element-plus"
import { createApp, } from "vue"
import App from "./App.vue"
import "virtual:uno.css"
import "@/styles/index.scss"
import "animate.css"
import "virtual:svg-icons-register"

function setup() {
  const app = createApp(App,)
  setupPinia(app,)
  setupI18n(app,)
  setupVxeTable(app,)
  setupRouter(app,)
  setupMatomo(app, router,)
  setupAuth(app,)
  setupPermission(app,)

  app
    .use(ElLoading,)
    .mount("#app",)
  return app
}

setup()
