import type { UploadProgressEvent, UploadRequestHandler, UploadRequestOptions, } from "element-plus"
import { getOssSign, } from "@/apis/productQualityAnalysis"

import { isNil, } from "lodash-es"

const SCOPE = "ElUpload"
/**
 * 自定义错误类，用于 ElementPlus 相关错误。
 */
class ElementPlusError extends Error {
  /**
   * 创建 ElementPlusError 实例。
   * @param {string} m 错误信息。
   */
  constructor(m: string,) {
    super(m,)
    this.name = "ElementPlusError"
  }
}
/**
 * 上传过程中 AJAX 请求的错误类。
 */
export class UploadAjaxError extends Error {
  name = "UploadAjaxError"
  status: number
  method: string
  url: string
  /**
   * 创建 UploadAjaxError 实例。
   * @param {string} message 错误信息。
   * @param {number} status HTTP 状态码。
   * @param {string} method HTTP 方法。
   * @param {string} url 请求的 URL。
   */
  constructor(message: string, status: number, method: string, url: string,) {
    super(message,)
    this.status = status
    this.method = method
    this.url = url
  }
}
/**
 * 抛出一个 ElementPlusError 错误。
 * @param {string} scope 错误所属的范围或组件。
 * @param {string} m 错误信息。
 * @returns {never}
 */
export function throwError(scope: string, m: string,): never {
  throw new ElementPlusError(`[${scope}] ${m}`,)
}
/**
 * 从 XMLHttpRequest 获取响应体。
 * @param {XMLHttpRequest} xhr XMLHttpRequest 实例。
 * @returns {XMLHttpRequestResponseType} 响应体内容。
 */
function getBody(xhr: XMLHttpRequest,): XMLHttpRequestResponseType {
  const text = xhr.responseText || xhr.response
  if (!text) {
    return text
  }

  try {
    return JSON.parse(text,)
  } catch {
    return text
  }
}
/**
 * 获取上传过程中的错误信息。
 * @param {string} action 执行的动作或 URL。
 * @param {UploadRequestOptions} option 上传请求选项。
 * @param {XMLHttpRequest} xhr XMLHttpRequest 实例。
 * @returns {UploadAjaxError} 上传 AJAX 错误实例。
 */
function getError(
  action: string,
  option: UploadRequestOptions,
  xhr: XMLHttpRequest,
): UploadAjaxError {
  let msg: string
  if (xhr.response) {
    msg = `${xhr.response.error || xhr.response}`
  } else if (xhr.responseText) {
    msg = `${xhr.responseText}`
  } else {
    msg = `fail to ${option.method} ${action} ${xhr.status}`
  }

  return new UploadAjaxError(msg, xhr.status, option.method, action,)
}

/**
 * AJAX 上传处理器。
 * @param {UploadRequestOptions} option 上传请求选项。
 * @returns {XMLHttpRequest} XMLHttpRequest 实例。
 * @description 获取 OSS 签名：从后端获取用于对象存储服务（OSS）的签名信息。
 * 构建 FormData：创建一个 FormData 对象，并添加必要的字段，例如文件、策略、签名等。
 * 设置 XMLHttpRequest：创建一个 XMLHttpRequest 对象，并为上传事件添加事件监听器。
 * 发送请求：打开一个请求，设置必要的请求头，然后发送 FormData。
 * 处理响应：监听请求的 load 和 error 事件，处理上传的结果或错误。
 */
export const ajaxUpload: UploadRequestHandler = (option: UploadRequestOptions,): XMLHttpRequest => {
  if (typeof XMLHttpRequest === "undefined") {
    throwError(SCOPE, "XMLHttpRequest is undefined",)
  }

  const xhr = new XMLHttpRequest()

  // 处理异步获取 OSS 签名的操作
  getOssSign({ fileName: option.file.name, },)
    .then((response,) => {
      if (!response) {
        throw new Error("没有请求到数据",)
      }
      const { data, } = response
      const {
        originName: name,
        objectName: key,
        policy,
        accessid: OSSAccessKeyId,
        callback,
        signature,
        host,
      } = data

      option.action = host!
      const action = option.action

      if (xhr.upload) {
        xhr.upload.addEventListener("progress", (evt,) => {
          const progressEvt = evt as UploadProgressEvent
          progressEvt.percent = evt.total > 0 ? (evt.loaded / evt.total) * 100 : 0
          option.onProgress(progressEvt,)
        },)
      }

      const formData = new FormData()
      const form = Object.assign(
        { name: encodeURIComponent(name!,), key, policy, OSSAccessKeyId, callback, signature, },
        { file: option.file, success_action_status: 200, },
      )
      Object.keys(form,).forEach(key => formData.set(key, form[key],),)

      xhr.addEventListener("error", () => {
        option.onError(getError(action, option, xhr,),)
      },)

      xhr.addEventListener("load", () => {
        if (xhr.status !== 200) {
          return option.onError(getError(action, option, xhr,),)
        }
        option.onSuccess(getBody(xhr,),)
      },)

      xhr.open(option.method, action, true,)

      if (option.withCredentials && "withCredentials" in xhr) {
        xhr.withCredentials = true
      }

      const headers = option.headers || {}
      if (headers instanceof Headers) {
        headers.forEach((value, key,) => xhr.setRequestHeader(key, value,),)
      } else {
        for (const [key, value,] of Object.entries(headers,)) {
          if (isNil(value,)) {
            continue
          }
          xhr.setRequestHeader(key, String(value,),)
        }
      }
      xhr.send(formData,)
    },)
    .catch(() => {
      option.onError(getError("get", option, xhr,),)
    },)

  return xhr
}
