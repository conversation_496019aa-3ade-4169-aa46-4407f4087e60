declare global {
  type ItemName<T,> = T & {
    [key in `${keyof T}ItemName`]?: string
  }
  interface RawResponse {
    rawData: true
  }

  interface NewBasicResponseData {
    /** 返回标记：成功标记=0001，其余都是失败 */
    responseCode: string
    /** 响应描述 */
    responseDesc: string
    /** 表示请求是否成功 */
    success: boolean
  }

  type NewResponseData<T,> = NewBasicResponseData & {
    data: T
  }

  interface NewBasicPage {
    currPage: number
    pageSize: number
    totalCount: number
    totalPage?: number
    total?: number
  }

  /**
   * 分页请求参数的接口。
   * 用于指定分页请求的当前页和页大小。
   */
  interface PageParams {
    /** 当前页码 */
    current: number
    /** 每页显示的记录数 */
    size: number
  }
  type NewListResponseData<T,> = {
    /** 数据列表 */
    records: T[]
  } & NewBasicPage
  type NewPageResponseData<T,> = NewBasicResponseData & {
    data: NewListResponseData<T>
  }
}

export {}
