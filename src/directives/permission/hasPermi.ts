import type { App, Directive, DirectiveBinding, } from "vue"
import { useI18n, } from "@/hooks/useI18n"
import { useRouteStore, } from "@/store/Route"
import { intersection, isArray, } from "lodash-es"

const { t, } = useI18n()
// 全部权限
export function hasPermission(value: string | string[],): boolean {
  const permissionStore = useRouteStore()
  const skipAuth = permissionStore.getSkipAuth
  if (skipAuth) {
    return true
  }
  const permissions = permissionStore.getResList?.map(item => item.code,) || []

  if (!value) {
    throw new Error(t("permission.hasPermission",),)
  }
  if (!isArray(value,)) {
    return permissions?.includes(value as string,)
  }

  return (intersection(value, permissions,) as string[]).length > 0
}
function hasPermi(el: Element, binding: DirectiveBinding,) {
  const value = binding.value
  const flag = hasPermission(value,)
  if (!flag) {
    el.parentNode?.removeChild(el,)
  }
}
function mounted(el: Element, binding: DirectiveBinding<any>,) {
  hasPermi(el, binding,)
}
const permiDirective: Directive = {
  mounted,
}

export function setupPermissionDirective(app: App<Element>,) {
  app.directive("hasPermi", permiDirective,)
}

export default permiDirective
