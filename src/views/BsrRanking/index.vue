<script lang="ts" setup>
import type {
  BsrRankingListApi,
} from "@/views/BsrRanking/api"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { virtualScrollProps, } from "@/setup"
import { trackDialogEvent, trackEvent, } from "@/utils/monitor.ts"
import {
  getBsrRankingList,
} from "@/views/BsrRanking/api"
import BsrRankingDetailDialog from "@/views/BsrRanking/BsrRankingDetailDialog.vue"
import { FormConfig, StationEnums, } from "@/views/BsrRanking/help"

defineOptions({
  name: "BsrRankingList",
},)
type FormModel = BsrRankingListApi.Request & { productCategoryID?: [] }
type RowType = BsrRankingListApi.Response
const currentRow = ref<RowType>()
const dialogVisible = ref(false,)
const formData = reactive<FormModel>({
  amazonCategoryIds: [],
  asin: "",
  brand: "",
  current: undefined,
  ids: [],
  productCategoryName: "",
  rankVersionList: [],
  rankVersionTimeList: [],
  size: undefined,
  sortInfos: [],
  station: StationEnums.US,
  productCategoryID: [],
},)
const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getBsrRankingList,
  formData,
  formatParams: (formData,) => {
    trackEvent("search", "亚马逊榜单搜索",)
    const lastElements = formData?.amazonCategoryIds?.map(subArr => subArr[subArr?.length - 1],) || []
    return {
      ...formData,
      productCategoryName: formData?.productCategoryID?.join("/",),
      amazonCategoryIds: lastElements,
    }
  },
},)
function openDialog(row: RowType,) {
  trackDialogEvent("历史销量与单价",)
  currentRow.value = row
  dialogVisible.value = true
}

const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()

// 这个页面0为true,1为false
function format01(value: number | undefined, falseLabel = "否",) {
  if (value === 0) {
    return "是"
  }
  return falseLabel
}
function formatMoney(money: number | undefined, unit: string | undefined,) {
  return `${money} ${unit}`
}
function handleCommand(command: string | number | object,) {
  const checked: RowType[] | undefined = tableRef.value?.getCheckboxRecords()
  let reqParamObj: any
  const amazonCategoryIds = formData?.amazonCategoryIds?.map(subArr => subArr[subArr?.length - 1],) || []
  if (Number(command,) === 1) {
    reqParamObj
    = Array.isArray(checked,) && checked.length > 0
        ? {
            asinList: checked.map((item,) => {
              return item.asin
            },),
            station: formData.station,
          }
        : {
            ...formData,
            amazonCategoryIds,
            productCategoryName: formData?.productCategoryID?.join("/",),
          }
  } else {
    reqParamObj
        = Array.isArray(checked,) && checked.length > 0
        ? {
            ids: checked.map((item,) => {
              return item.id
            },),
          }
        : {
            ...formData,
            amazonCategoryIds,
            productCategoryName: formData?.productCategoryID?.join("/",),
          }
  }
  switch (Number(command,)) {
    case 1:
      if (!checked || checked?.length <= 0) {
        ElMessage.error("必须勾选榜单的ASIN数据",)
        return
      }
      if (checked?.length > 100) {
        ElMessage.error("最多只能勾选100条数据",)
        return
      }
      trackEvent("download", "导出销量与单价",)
      exportFn({
        exportType: "asinSalesHistory-export",
        reqParam: JSON.stringify(reqParamObj,),
      },)
      break
    case 2:
      if (!formData.station) {
        ElMessage.error("请选择站点",)
        return
      }
      trackEvent("download", "导出榜单数据（不含图）",)
      exportFn({
        exportType: "bsrRank-export-noImg",
        reqParam: JSON.stringify(reqParamObj,),
      },)
      break
    case 3:
      if (!formData.station) {
        ElMessage.error("请先选择站点",)
        return
      }
      if (formData.amazonCategoryIds && formData.amazonCategoryIds?.length <= 0 && formData.productCategoryID && formData.productCategoryID?.length <= 0) {
        ElMessage.error("请先选择亚马逊类目或者MMT类目",)
        return
      }
      trackEvent("download", "导出榜单数据（含图片）",)
      exportFn({
        exportType: "bsrRank-export-plm",
        reqParam: JSON.stringify(reqParamObj,),
      },)
      break
  }
}
// 修改formConfigComputed计算属性
const formConfigComputed = computed(() => {
  return FormConfig.map((item,) => {
    const newItem = { ...item, }
    switch (newItem.field) {
      case "amazonCategoryIds":
        newItem.params = {
          ...newItem.params,
          station: formData.station,
        }
        break
    }
    return newItem
  },)
},)
watch(formConfigComputed, (newConfig,) => {
  console.log("表单配置变更:", newConfig.map(c => ({
    field: c.field,
    params: c.params,
  }),),)
}, { immediate: true, },)
</script>

<template>
  <ContentWrap>
    <div class="h-[calc(100vh_-_150px)] overflow-x-hidden overflow-y-auto">
      <LayoutForm
        v-if="hasPermission('bsrRanking:search')"
        ref="formRef"
        :descriptions="FormConfig"
        :loading="loading"
        :model="formData"
        :span="6"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem
          v-for="({ label, layout, field, props, rules, ...other }, index) in formConfigComputed"
          :key="index"
          :label="label"
          :rules="rules"
        >
          <component
            :is="layout as Component"
            v-model="formData[field as keyof BsrRankingListApi.Request]"
            v-bind="{ ...other,
                      key: JSON.stringify(other.params),
                      ...props, // 级联处理
                      params: other.params,
            }"
          />
        </ElFormItem>
      </LayoutForm>
      <div class="flex" style="margin-bottom: 10px;">
        <ElDropdown v-if="hasPermission('bsrRanking:export')" @command="handleCommand">
          <ElButton
            v-hasPermi="['bsrRanking:export']"
            :loading="exportLoading"
            class="w-16"
            type="primary"
          >
            <Icon :size="20" icon="ep:download" />
            导出
          </ElButton>
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem :key="1" command="1">
                导出销量与单价
              </ElDropdownItem>
              <ElDropdownItem :key="2" command="2">
                导出榜单数据（不含图）
              </ElDropdownItem>
              <ElDropdownItem :key="3" command="3">
                导出榜单数据（含图片）
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
      <VxeTable
        ref="tableRef"
        :cell-config="{ height: 110 }"
        :data="tableData"
        :loading="loading"
        :max-height="maxHeight"
        :min-height="280"
        v-bind="{ ...virtualScrollProps }"
        show-overflow="tooltip"
      >
        <VxeColumn type="checkbox" width="40" />
        <VxeColumn field="station" title="站点" width="120" />
        <!-- rankVersionInfo展示, rankVersion排序 -->
        <VxeColumn
          field="rankVersion"
          title="榜单版本"
          width="120"
          sortable
        >
          <template #default="{ row }: { row: RowType }">
            {{ row.rankVersionInfo }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="ranking"
          title="排名"
          width="120"
          sortable
        />
        <VxeColumn
          :cell-render="{ name: 'Image' }"
          field="primaryImageUrl"
          title="主图"
          width="100"
        />
        <VxeColumn field="productName" title="产品名称" width="100" />
        <VxeColumn
          field="asin"
          title="ASIN"
          width="100"
          sortable
        />
        <VxeColumn field="productCategory" title="产品类型" width="100" />
        <VxeColumn
          field="parentAsin"
          title="ParentAsin"
          width="125"
          sortable
        />
        <VxeColumn field="amazonCategoryName" title="所在类目" width="100" />
        <VxeColumn field="productCategoryName" title="MMT类目" width="100" />
        <VxeColumn field="targetAudience" title="人群" width="100" />
        <VxeColumn field="targetSex" title="性别" width="100" />
        <VxeColumn field="categoryStyle" title="风格" width="100" />
        <VxeColumn field="mainCategory" title="大类" width="100" />
        <VxeColumn field="classificationName" title="类目名称" width="100" />
        <VxeColumn field="applicableSeason" title="季节" width="100" />
        <VxeColumn field="amazonUrl" title="URL" width="100">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            <ElLink :href="row.amazonUrl" target="_blank" type="primary">
              {{ row.amazonUrl }}
            </ElLink>
          </template>
        </VxeColumn>
        <VxeColumn
          field="estimatedMonthlyListingSales"
          title="预计Listing月销量"
          width="150"
          sortable
        />
        <VxeColumn
          field="estimatedMonthlySalesRevenue"
          title="预计月销售额"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            <span v-if="row.estimatedMonthlyListingSales">
              {{ row.estimatedMonthlyListingSales }}
            </span>
            <span v-if="row.estimatedMonthlyListingSales && row.monetaryUnit">
              {{ row.monetaryUnit }}
            </span>
          </template>
        </VxeColumn>
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn
          field="listingTime"
          title="上架时间"
          width="150"
          sortable
        />
        <VxeColumn
          field="listingDays"
          title="上架天数"
          width="100"
          sortable
        />
        <VxeColumn
          field="recentSevenDayStockTensionFlag"
          title="近7日是否出现库存紧张"
          width="175"
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.recentSevenDayStockTensionFlag) }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="recentThirtyDayStockTensionFlag"
          title="近30日是否出现库存紧张"
          width="175"
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.recentThirtyDayStockTensionFlag) }}
          </template>
        </VxeColumn>
        <VxeColumn field="recentSevenDayStockoutFlag" title="近7日是否出现断货" width="150">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.recentSevenDayStockoutFlag) }}
          </template>
        </VxeColumn>
        <VxeColumn field="recentThirtyDayStockoutFlag" title="近30日是否出现断货" width="150">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.recentThirtyDayStockoutFlag) }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="reviesNum"
          title="评价数量"
          width="100"
          sortable
        />
        <VxeColumn
          field="ratingStar"
          title="评分星级"
          width="100"
          sortable
        />
        <VxeColumn field="bbxSellerAttribute" title="BBX卖家属性" width="100" />
        <VxeColumn field="storeName" title="店铺" width="100" />
        <VxeColumn field="nationalRegion" title="国籍/地区" width="100" />
        <VxeColumn
          field="currentSalesPrice"
          title="目前销售价"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ formatMoney(row.crossedPrice, row.monetaryUnit) }}
          </template>
        </VxeColumn>
        <VxeColumn field="coupon" title="Coupon" width="100" />
        <VxeColumn
          field="preferentialPrice"
          title="优惠"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ formatMoney(row.preferentialPrice, row.monetaryUnit) }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="actualPrice"
          title="实际价格"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ formatMoney(row.actualPrice, row.monetaryUnit) }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="crossedPrice"
          title="划线价"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ formatMoney(row.crossedPrice, row.monetaryUnit) }}
          </template>
        </VxeColumn>
        <VxeColumn
          field="grossProfit"
          title="单个产品毛利"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            <span v-if="row.grossProfit">
              {{ row.grossProfit }}
            </span>
            <span v-if="row.grossProfit && row.monetaryUnit">
              {{ row.monetaryUnit }}
            </span>
          </template>
        </VxeColumn>
        <VxeColumn
          field="grossProfitRate"
          title="单个产品毛利率(%)"
          width="150"
          sortable
        />
        <VxeColumn
          field="counterQuantity"
          title="单个产品跟卖数量"
          width="125"
          sortable
        />
        <VxeColumn
          field="variantsQuantity"
          title="单个产品变体数量"
          width="125"
          sortable
        />
        <VxeColumn
          field="marketShare"
          title="月度同品牌销量占比(市场份额%)"
          width="225"
          sortable
        />
        <VxeColumn
          field="salesRatio"
          title="月度流量圈销量占比(%)"
          width="100"
          sortable
        />
        <VxeColumn
          field="fbaCost"
          title="FBA费用"
          width="100"
          sortable
        >
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            <span v-if="row.fbaCost">
              {{ row.fbaCost }}
            </span>
            <span v-if="row.fbaCost && row.monetaryUnit">
              {{ row.monetaryUnit }}
            </span>
          </template>
        </VxeColumn>
        <VxeColumn
          field="categoryRanking"
          title="大类排名"
          width="100"
          sortable
        />
        <VxeColumn field="averageCategoryRanking" title="平均大类排名" width="100" />
        <VxeColumn field="categoryRankingAndName" title="小类排名及名称" width="150" />
        <VxeColumn field="rankingChange" title="排名变化" width="100" />
        <VxeColumn field="rankingChangeRatio" title="排名变化率" width="100" />
        <VxeColumn field="primaryImageVideo" title="主图视频" width="100">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.primaryImageVideo, '无') }}
          </template>
        </VxeColumn>
        <VxeColumn field="aAdd" title="A+" width="100">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.aAdd, '无') }}
          </template>
        </VxeColumn>
        <VxeColumn field="climatePledgeFriendlyFlag" title="是否气候承诺友好标" width="150">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.climatePledgeFriendlyFlag, '无') }}
          </template>
        </VxeColumn>
        <VxeColumn field="doBrandFlagshipStoreFlag" title="是否做品牌旗舰店" width="125">
          <template #default="{ row }: { row: BsrRankingListApi.Response }">
            {{ format01(row.doBrandFlagshipStoreFlag) }}
          </template>
        </VxeColumn>
        <VxeColumn field="logisticsMode" title="物流方式" width="100" />
        <VxeColumn field="weight" title="重量(lb)" width="100" />
        <VxeColumn field="volume" title="体积（in³）" width="100" />
        <VxeColumn field="sizeStandard" title="尺寸标准" width="100" />
        <VxeColumn field="sizeInfo" title="尺寸（in）" width="150" />
        <VxeColumn field="fivePointDesc" title="五点描述" width="400">
          <template #default="{ row }: { row: RowType }">
            <div v-html="row.fivePointDesc" />
          </template>
        </VxeColumn>

        <VxeColumn fixed="right" title="操作" width="180">
          <template #default="{ row }: { row: RowType }">
            <ElButton
              v-hasPermi="['bsrRanking:detail']"
              type="primary"
              link
              @click="openDialog(row)"
            >
              历史销量与单价
            </ElButton>
          </template>
        </VxeColumn>
      </VxeTable>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.currPage"
        v-model:page-size="pager.pageSize"
        :total="pager.totalCount"
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @click="handlePagerChange"
      />
    </div>
    <BsrRankingDetailDialog v-model="dialogVisible" :current-row="currentRow" />
  </ContentWrap>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  align-items: center;
}
</style>
