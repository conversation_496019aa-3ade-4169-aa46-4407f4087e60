import { ApiSelect, } from "@/components/ApiSelect"
import { DictSelect, } from "@/components/DictSelect"
import { ElCascader, } from "@/components/ElCascader"
import { getBsrCategoryList, getBsrVersionList, queryCategoryTree, } from "@/views/BsrRanking/api"
import { ElInput, } from "element-plus"

export const FormConfig = [
  {
    label: "站点",
    layout: DictSelect,
    field: "station",
    props: {
      filterable: true,
      dictCode: "station",
    },
    apiConfig: {
      config: {
        label: "dictEnName",
        value: "dictValue",
      },
    },
    rules: [
      {
        required: true,
        message: "请选择站点",
        trigger: "change",
      },
    ],
    apiKey: "station",
  },
  {
    "label": "榜单版本",
    "layout": ApiSelect,
    "field": "rankVersionList",
    "api-config": {
      api: getBsrVersionList,
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "multiple": true,
    },
  },
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "productCategoryID",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      clearable: true,
      filterable: true,
    },
  },
  {
    "label": "亚马逊类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "amazonCategoryIds",
    "params": {
      station: "",
    },
    "api-config": {
      api: getBsrCategoryList,
      config: {
        label: "selectorEnValue",
        value: "selectorKey",
        children: "childList",
      },
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "props": {
        multiple: true,
      },
    },
  },
  {
    label: "品牌",
    field: "brand",
    layout: ElInput,
    props: {
      clearable: true,
      placeholder: "请输入品牌",
    },
  },
  {
    label: "ASIN",
    field: "asin",
    layout: ElInput,
    props: {
      clearable: true,
      placeholder: "请输入ASIN",
    },
  },
  {
    label: "人群",
    layout: DictSelect,
    field: "targetAudiences",
    props: {
      "multiple": true,
      "dictCode": "PRODUCT_PEOPLE",
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
  },
  {
    label: "性别",
    layout: DictSelect,
    field: "targetSexs",
    props: {
      "multiple": true,
      "dictCode": "PLATFORM_CATEGORY_SEX",
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
  },
  {
    label: "风格",
    layout: DictSelect,
    field: "categoryStyles",
    props: {
      "clearable": true,
      "multiple": true,
      "dictCode": "PLATFORM_PRODUCT_STYLE",
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "大类",
    layout: DictSelect,
    field: "mainCategorys",
    props: {
      "clearable": true,
      "dictCode": "PLATFORM_MAIN_CATEGORY",
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "季节",
    layout: DictSelect,
    span: 12,
    field: "applicableSeasons",
    props: {
      "clearable": true,
      "dictCode": "PLATFORM_CATEGORY_SEASON",
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "类目名称",
    layout: DictSelect,
    field: "classificationNames",
    props: {
      "clearable": true,
      "dictCode": "PRODUCT_STYLE",
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
]
/**
 * 站点枚举
 */
export enum StationEnums {
  /**
   * us
   */
  US = "US",
}
