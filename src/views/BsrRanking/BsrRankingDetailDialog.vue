<script lang="ts" setup>
import type { BsrRankingHistoryApi, BsrRankingListApi, } from "@/views/BsrRanking/api"

import { getBsrHistory, } from "@/views/BsrRanking/api"

defineOptions({
  name: "BsrRankingDetailDialog",
},)

const props = defineProps<{
  modelValue: boolean
  currentRow: BsrRankingListApi.Response | undefined
}>()

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)

const queryLoading = ref(false,)
type TableRow = BsrRankingHistoryApi.Response
const tableData = ref<TableRow[] | undefined>()

const transformedData = ref<Record<string, string>[]>([],)
const dynamicColumns = ref<string[]>([],)
async function queryBsrHistory() {
  const row = props.currentRow
  if (!row) {
    return
  }
  queryLoading.value = true

  const response = await getBsrHistory({
    asin: row.asin,
    station: row.station,
  },)
  queryLoading.value = false
  if (response?.success) {
    tableData.value = response?.data
  }
}

watch(
  visible,
  async() => {
    if (visible.value) {
      await queryBsrHistory()
    }
  },
  { immediate: true, },
)

function handleClose() {
  visible.value = false
  tableData.value = []
}
const isEmpty = computed(() => {
  if (!Array.isArray(tableData.value,)) {
    return true
  }
  return tableData.value.length === 0
},)

const mapTranslate = {
  monthlyListingSales: "Listing月销量",
  monthlyListingSalesVolume: "月销售额",
  monthlyAverageUnitPrice: "平均单价",
}

watch(
  tableData,
  (newVal: TableRow[],) => {
    transformedData.value = transformTableData(newVal,)
    dynamicColumns.value = newVal.map(item => item.rankVersion!,)
  },
  { deep: true, },
)

function transformTableData(data: TableRow[],) {
  const objKeys = Object.keys(mapTranslate,)
  return objKeys.map((key,) => {
    const row = { rankVersion: mapTranslate[key], }
    data.forEach((item,) => {
      if (item.rankVersion) {
        if (key === "monthlyAverageUnitPrice") {
          row[item.rankVersion] = `${item[key]} ${item.monetaryUnit || ""}`
          return
        }
        row[item.rankVersion] = item[key]
      }
    },)
    return row
  },)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="handleClose"
    :parent-scroll="false"
    title=""
  >
    <template #title>
      <div class="w-full flex items-center">
        <div class="flex items-center gap-4">
          <ElImage

            :preview-src-list="[currentRow?.primaryImageUrl!]"
            :src="currentRow?.primaryImageUrl"
            class="h-[35px] w-[40px]"
            hide-on-click-modal
            lazy
          />
          <span class="text-gray-500"> 查看历史销量与单价 </span>
        </div>
        <div class="ml-32 mr-auto">
          ASIN：{{ currentRow?.asin }}
        </div>
      </div>
    </template>
    <template v-if="isEmpty">
      <ElTable />
    </template>
    <template v-else>
      <ElTable :data="transformedData" class="w-full" border>
        <ElTableColumn label="历史月份" prop="rankVersion" width="180" />
        <ElTableColumn
          v-for="(column, index) in dynamicColumns"
          :key="index"
          :label="column"
          :prop="column"
        />
      </ElTable>
    </template>
    <template #footer>
      <ElButton @click="handleClose">
        关闭
      </ElButton>
    </template>
  </ElDialog>
</template>

<style lang="scss" scoped>
:deep(tr .el-table__cell:first-of-type) {
  background: #ccc;
}

:deep(.el-table__header thead) {
  color: var(--el-text-color-regular);

  .el-table__cell {
    font-weight: 500;
  }
}
</style>
