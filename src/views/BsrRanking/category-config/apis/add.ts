import { postJSON, } from "@/utils/fetch.ts"

export namespace AddCategoryAPI {
  export interface Params {
    /**
     * 适用季节（字典：PLATFORM_CATEGORY_SEASON，取值字典值）
     */
    applicableSeason: string
    /**
     * 平台类目
     */
    categoryAllName: string
    /**
     * 风格（字典：PRODUCT_STYLE）取值字典英文
     */
    categoryStyleList: string[]
    /**
     * 类目名称()
     */
    classificationNameList: string[]
    /**
     * 大类（字典：PLATFORM_MAIN_CATEGORY，取字典值，展示字典中文）
     */
    mainCategoryList: string[]
    /**
     * 平台类型: amazon-亚马逊
     */
    platformType: string
    /**
     * URL对应链接
     */
    platformUrl: string
    /**
     * MMT类目名称
     */
    productAllCategoryName?: string[]
    productAllCategoryNames?: string[]
    /**
     * 站点
     */
    station: string
    /**
     * 启用状态: start-启用, ban-禁用
     */
    status?: string
    /**
     * 人群(字典：PRODUCT_PEOPLE)取值字典值
     */
    targetAudience: string
    /**
     * 性别（字典: PLATFORM_CATEGORY_SEX, 取值字典值）
     */
    targetSexList: string[]
  }
  export type Request = Partial<Params>
  export type Response = NewBasicResponseData
}

export function addCategory(data: AddCategoryAPI.Request,) {
  return postJSON<NewBasicResponseData>({
    url: "/platform/category/insertPlatformCategory",
    data,
  },)
}
