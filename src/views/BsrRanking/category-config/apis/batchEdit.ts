import type { CategoryPageAPI, } from "@/views/BsrRanking/category-config/apis/list"
import { postJSON, } from "@/utils/fetch.ts"

export namespace CategoryListByIdAPI {
  export type Request = CategoryPageAPI.Params
  export type Response = NewResponseData<CategoryPageAPI.List>
}

export function getCategoryListById(data: CategoryListByIdAPI.Request,) {
  return postJSON<NewResponseData<any>>({
    url: "/platform/category/queryByIds",
    data,
  },)
}
export namespace CategoryBatchEditAPI {
  export type Request = CategoryPageAPI.List
  export type Response = NewBasicResponseData
}

export function batchEditCategory(data: CategoryBatchEditAPI.Request,) {
  return postJSON<NewBasicResponseData>({
    url: "/platform/category/updatePlatformCategorys",
    data,
  },)
}

export function changeStatus(data,) {
  return postJSON<NewBasicResponseData>({
    url: "/platform/category/startOrBan",
    data,
  },)
}
