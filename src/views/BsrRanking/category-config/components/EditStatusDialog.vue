<script lang="ts" setup>
import type { CategoryPageAPI, } from "../apis/list"
import { virtualScrollProps, } from "@/setup"
import { StatusEnum, } from "@/views/BsrRanking/category-config/help"
import { ElMessage, } from "element-plus"
import { changeStatus, } from "../apis/batchEdit"

defineOptions({
  name: "StatusDialog",
},)

const props = defineProps<{
  modelValue: boolean
  selectedRows: CategoryPageAPI.List
}>()

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void
  (e: "refresh"): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)

type TableRow = Partial<CategoryPageAPI.Row & { updateStatus: string }>

const tableData = ref<TableRow[]>([],)

watch(visible, (val,) => {
  if (val) {
    tableData.value = props.selectedRows.slice()
  }
},)

function handleClose() {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false,)
async function handleSubmit() {
  const hasError = tableData.value.some((item,) => {
    return item.newStatus === undefined || item.newStatus === ""
  },)
  if (hasError) {
    ElMessage.warning("请选择状态",)
    return
  }
  submitLoading.value = true
  const response = await changeStatus({
    updateStatusReqList: tableData.value.map(e => ({
      id: e.id!,
      oldStatus: e.status!,
      newStatus: e.newStatus!,
    }),),
  },)
  submitLoading.value = false
  if (response?.success) {
    ElMessage.success("操作成功",)
    handleClose()
    emit("refresh",)
  }
}
</script>

<template>
  <ElDialog v-model="visible" :before-close="handleClose" title="操作确认">
    <VxeTable :data="tableData" :max-height="800" v-bind="virtualScrollProps">
      <VxeColumn field="station" title="站点" />
      <VxeColumn field="categoryAllName" title="平台类目" />
      <VxeColumn field="productAllCategoryName" title="MMT类目" />
      <VxeColumn field="statusName" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }: { row: CategoryPageAPI.Row }">
          <ElSelect v-model="(row as TableRow).newStatus">
            <ElOption v-if="row.status === StatusEnum.BAN" :value="StatusEnum.START" label="启用" />
            <ElOption v-if="row.status === StatusEnum.START" :value="StatusEnum.BAN" label="禁用" />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">
        确认
      </ElButton>
      <ElButton :loading="submitLoading" @click="handleClose">
        取消
      </ElButton>
    </template>
  </ElDialog>
</template>

<style lang="less" scoped></style>
