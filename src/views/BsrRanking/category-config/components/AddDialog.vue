<script lang="ts" setup>
import type { ApiSelect, } from "@/components/ApiSelect"
import type { AddCategoryAPI, } from "@/views/BsrRanking/category-config/apis/add"
import type { FormInstance, FormRules, } from "element-plus"
import { addCategory, } from "@/views/BsrRanking/category-config/apis/add"
import { editFormConfig, StatusEnum, } from "@/views/BsrRanking/category-config/help.ts"
import { noop, omit, } from "lodash-es"

defineOptions({
  name: "AddDialog",
},)
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void
  (e: "refresh"): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)

const title = computed(() => {
  return "新增平台类目"
},)
type ElCascaderInstance = InstanceType<typeof ApiSelect>
const productCategoryRef = ref<ElCascaderInstance>()
const formRef = ref<FormInstance>()
const formData = ref<AddCategoryAPI.Request & { productCategoryName?: string[][] }>({
  platformType: "amazon",
  status: StatusEnum.START,
},)
const rules = computed<FormRules<typeof formData>>(() => ({
  station: [{ required: true, message: "请选择站点", trigger: "change", },],
  categoryAllName: [{ required: true, message: "请选择平台类目", trigger: "change", },],
  targetAudience: [{ required: true, message: "请选择人群", trigger: "change", },],
  categoryStyleList: [{ required: true, message: "请选择风格", trigger: "change", },],
  targetSexList: [{ required: true, message: "请选择性别", trigger: "change", },],
  mainCategoryList: [{ required: true, message: "请选择大类", trigger: "change", },],
  classificationNameList: [{ required: true, message: "请选择类目名称", trigger: "change", },],
  applicableSeason: [{ required: true, message: "请选择季节", trigger: "change", },],
  status: [{ required: true, message: "请选择状态", trigger: "change", },],
  platformUrl: [{ required: true, message: "请输入对应链接", trigger: "change", },],
}),)

function handleClose() {
  formData.value = {
    platformType: "amazon",
    status: StatusEnum.START,
  }
  visible.value = false
}
const submitLoading = ref(false,)
async function handleSubmit() {
  const valid = await formRef.value?.validate().catch(noop,)
  if (!valid) {
    return
  }
  submitLoading.value = true
  const nodes = productCategoryRef.value?.[0].componentRef?.cascaderRef?.getCheckedNodes(true,)
  console.log(formData.value, productCategoryRef.value,)
  const response = await addCategory({
    ...omit(formData.value, ["productCategoryName",],),
    productAllCategoryNames: nodes?.map(e => e.pathLabels.join("/",),),
  },)
  submitLoading.value = false
  if (response?.success) {
    ElMessage.success("操作成功",)
    emit("refresh",)
    handleClose()
  }
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    width="90%"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      class="mx-4"
      label-width="auto"
      scroll-to-error
    >
      <ElRow :gutter="20">
        <ElCol
          v-for="({ label, layout, field, props, ...other }, index) in editFormConfig"
          :key="index"
          :span="12"
        >
          <ElFormItem :label="label" :prop="field">
            <component
              :is="layout as Component"
              v-if="field === 'productCategoryName'"
              ref="productCategoryRef"
              v-model="formData[field]"
              v-bind="{ ...other,
                        ...props, // 级联处理

              }"
            />
            <component
              :is="layout as Component"
              v-else
              v-model="formData[field]"
              v-bind="{ ...other,
                        ...props, // 级联处理

              }"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">
        关闭
      </ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">
        保存
      </ElButton>
    </template>
  </ElDialog>
</template>

<style lang="less" scoped></style>
