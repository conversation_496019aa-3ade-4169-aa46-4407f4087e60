<script lang="ts" setup>
import type { ApiSelect, } from "@/components/ApiSelect"
import type { DictSelect, } from "@/components/DictSelect"
import type { WMSCategoryListAPI, } from "@/views/BsrRanking/api"
import type { ElSelect, } from "element-plus"
import type { VxeTableInstance, VxeTablePropTypes, } from "vxe-table"
import type { CategoryPageAPI, } from "../apis/list"
import { ElCascader, } from "@/components/ElCascader"
import { virtualScrollProps, } from "@/setup"
import { omit, } from "lodash-es"
import { batchEditCategory, getCategoryListById, } from "../apis/batchEdit"

defineOptions({
  name: "BatchEditDialog",
},)

const props = defineProps<{
  modelValue: boolean
  wmsCategoryList: WMSCategoryListAPI.Data[]
  selectedRows?: CategoryPageAPI.List
}>()

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void
  (e: "refresh"): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)

const title = computed(() => {
  return "批量编辑平台类目"
},)

type Row = CategoryPageAPI.Row & {
  productCategoryName?: string[][]
} & {
  [key in `${keyof CategoryPageAPI.Row}ItemName`]: string
}
type ElCascaderInstance = InstanceType<typeof ElCascader>
type SelectPlusInstance = InstanceType<typeof ApiSelect>
type DictPlusInstance = InstanceType<typeof DictSelect>
const tableRef = ref<VxeTableInstance<Row>>()
const tableData = ref<Row[]>([],)
const queryLoading = ref(false,)
const cascaderRefs = ref<ElCascaderInstance[]>([],)
const selectRefs = ref<{ [key in keyof Row]?: SelectPlusInstance[] }>({
  mainCategoryList: [],
  classificationNameList: [],
  applicableSeason: [],
},)

function findCategoryCodes(categoryData: WMSCategoryListAPI.Data[], categoryNames: string[],) {
  let currentCategories = categoryData
  const categoryCodes: string[] = []

  for (const name of categoryNames) {
    const foundCategory = currentCategories.find(cat => cat.categoryName === name,)
    if (!foundCategory) {
      return null // 未找到匹配的类别
    }
    categoryCodes.push(foundCategory.categoryCode!,)
    currentCategories = foundCategory.sonCategory || []
  }

  return categoryCodes
}

watch(visible, async(val,) => {
  if (val) {
    queryLoading.value = true
    const response = await getCategoryListById({
      ids: props.selectedRows?.map(e => e.id!,),
    },)
    queryLoading.value = false
    if (response?.success) {
      tableData.value = response?.data.map((row,) => {
        const productCategoryName = row.productAllCategoryNames?.map((category,) => {
          if (category) {
            // 找到对应每一层的categoryCode
            return findCategoryCodes(props.wmsCategoryList, category.split("/",),)
          }
        },)
        return {
          ...row,
          productCategoryName,
          mainCategoryListItemName: row.mainCategory,
          classificationNameListItemName: row.classificationName,
          applicableSeasonItemName: row.applicableSeasonName,
        } as Row
      },)
    }
  }
},)

const rules = reactive<VxeTablePropTypes.EditRules<Row>>({
  station: [{ required: true, content: "站点不能为空", },],
  categoryAllName: [{ required: true, content: "平台类目不能为空", },],
  platformUrl: [{ required: true, content: "对应链接不能为空", },],
  targetAudience: [{ required: true, content: "人群不能为空", },],
  targetSexList: [{ required: true, content: "性别不能为空", },],
  categoryStyleList: [{ required: true, content: "风格不能为空", },],
  mainCategoryListItemName: [{ required: true, content: "大类不能为空", },],
  classificationNameListItemName: [{ required: true, content: "类目名称不能为空", },],
  applicableSeasonItemName: [{ required: true, content: "季节不能为空", },],
},)

async function handleProductCategoryNameChange(row: Row, rowIndex: number,) {
  const cascaderRef = cascaderRefs.value[rowIndex]?.cascaderRef
  const nodes = cascaderRef?.getCheckedNodes(true,)
  row.productAllCategoryNames = nodes?.map(e => e.pathLabels.join("/",),)
}

function handleGetSelectLabel(field: keyof Row, row: Row, rowIndex: number,) {
  const selectRef = selectRefs.value[field]?.[rowIndex]?.componentRef as InstanceType<typeof ElSelect>
  nextTick(() => {
    nextTick(() => {
      row[`${field}ItemName`] = Array.isArray(selectRef?.selectedLabel,) ? selectRef?.selectedLabel.join(",",) : selectRef?.selectedLabel
    },)
  },)
}

function handleClose() {
  tableData.value = []
  visible.value = false
}
const submitLoading = ref(false,)
async function handleSubmit() {
  const valid = await tableRef.value?.validate(true,)
  if (valid) {
    const errorMessage = Object.values(valid,)
      .map(e => e[0].rules[0].content,)
      .join("\n",)
    ElMessage.error(errorMessage,)
    return
  }
  submitLoading.value = true
  const response = await batchEditCategory(
    tableData.value.map((row,) => {
      return omit(row, ["productCategoryName",],)
    },) as CategoryPageAPI.List,
  )
  submitLoading.value = false
  if (response?.success) {
    ElMessage.success("操作成功",)
    emit("refresh",)
    handleClose()
  }
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    width="90%"
  >
    <VxeTable
      ref="tableRef"
      :cell-config="{ height: 110 }"
      :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell', autoPos: false, autoFocus: false }"
      :edit-rules="rules"
      :loading="queryLoading"
      :max-height="800"
      :valid-config="{ showMessage: false, autoPos: false }"
      v-bind="{ ...virtualScrollProps }"
    >
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="station"
        title="站点"
      >
        <template #edit="{ row }: { row: Row }">
          <DictSelect
            v-model="row.station"
            :api-config="{
              config: {
                label: 'dictEnName',
                value: 'dictValue',
              },
            }"
            class="!w-full"
            dict-code="station"
            popper-class="vxe-table--ignore-clear"
            filterable
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="categoryAllName"
        title="平台类目"
        width="300"
      >
        <template #edit="{ row }: { row: Row }">
          <ElInput
            v-model="row.categoryAllName"
            :autosize="{
              minRows: 2,
              maxRows: 4,
            }"
            maxlength="200"
            placeholder="请输入完整的亚马逊类目"
            type="textarea"
            clearable
            show-word-limit
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="productCategoryName"
        title="MMT类目"
        width="300"
      >
        <template #default="{ row }: { row: Row }">
          <span>{{ row.productAllCategoryNames?.join('、') }}</span>
        </template>
        <template #edit="{ row, rowIndex }: { row: Row, rowIndex: number }">
          <ElCascader
            :ref="(el: ElCascaderInstance) => (cascaderRefs[rowIndex] = el)"
            v-model="row.productCategoryName"
            :options="wmsCategoryList"
            :persistent="false"
            :props="{
              children: 'sonCategory',
              label: 'categoryName',
              value: 'categoryCode',
              emitPath: true,
              multiple: true,
            }"
            class="overflow-auto !w-full"
            placeholder="请选择"
            popper-class="vxe-table--ignore-clear"
            separator="/"
            clearable
            filterable
            @change="handleProductCategoryNameChange(row, rowIndex)"
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="platformUrl"
        title="对应链接"
        width="300"
      >
        <template #edit="{ row }: { row: Row }">
          <ElInput
            v-model="row.platformUrl"
            :autosize="{
              minRows: 2,
              maxRows: 4,
            }"
            maxlength="500"
            type="textarea"
            show-word-limit
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="targetAudience"
        title="人群"
      >
        <template #edit="{ row }: { row: Row }">
          <DictSelect
            v-model="row.targetAudience"
            :parent-scroll="false"
            class="!w-full"
            dict-code="PLATFORM_PRODUCT_PEOPLE"
            popper-class="vxe-table--ignore-clear"
            filterable
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="targetSexList"
        title="性别"
      >
        <template #edit="{ row }: { row: Row }">
          <DictSelect
            v-model="row.targetSexList"
            :parent-scroll="false"
            class="!w-full"
            dict-code="PLATFORM_CATEGORY_SEX"
            popper-class="vxe-table--ignore-clear"
            filterable
            multiple
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="categoryStyleList"
        title="风格"
      >
        <template #edit="{ row }: { row: Row }">
          <DictSelect
            v-model="row.categoryStyleList"
            :parent-scroll="false"
            class="!w-full"
            dict-code="PLATFORM_PRODUCT_STYLE"
            popper-class="vxe-table--ignore-clear"
            filterable
            multiple
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="mainCategoryListItemName"
        title="大类"
      >
        <template #edit="{ row, rowIndex }: { row: Row, rowIndex: number }">
          <DictSelect
            :ref="(el:DictPlusInstance) => selectRefs.mainCategoryList![rowIndex] = el"
            v-model="row.mainCategoryList"
            :parent-scroll="false"
            class="!w-full"
            dict-code="PLATFORM_MAIN_CATEGORY"
            popper-class="vxe-table--ignore-clear"
            filterable
            multiple
            @change="handleGetSelectLabel('mainCategoryList', row, rowIndex)"
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="classificationNameListItemName"
        title="类目名称"
      >
        <template #edit="{ row, rowIndex }: { row: Row, rowIndex: number }">
          <DictSelect
            :ref="(el:DictPlusInstance) => selectRefs.classificationNameList![rowIndex] = el"
            v-model="row.classificationNameList"
            :parent-scroll="false"
            class="!w-full"
            dict-code="CLASSIFICATION_NAME"
            popper-class="vxe-table--ignore-clear"
            filterable
            multiple
            @change="handleGetSelectLabel('classificationNameList', row, rowIndex)"
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        :min-width="150"
        field="applicableSeasonItemName"
        title="季节"
      >
        <template #edit="{ row, rowIndex }: { row: Row, rowIndex: number }">
          <DictSelect
            :ref="(el:DictPlusInstance) => selectRefs.applicableSeason![rowIndex] = el"
            v-model="row.applicableSeason"
            :parent-scroll="false"
            class="!w-full"
            dict-code="PLATFORM_CATEGORY_SEASON"
            popper-class="vxe-table--ignore-clear"
            clearable
            filterable
            @change="handleGetSelectLabel('applicableSeason', row, rowIndex)"
          />
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="queryLoading || submitLoading" @click="handleClose">
        关闭
      </ElButton>
      <ElButton :loading="queryLoading || submitLoading" type="primary" @click="handleSubmit">
        保存
      </ElButton>
    </template>
  </ElDialog>
</template>

<style lang="less" scoped></style>
