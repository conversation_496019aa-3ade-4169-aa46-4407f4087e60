<script lang="ts" setup>
import type { BsrRankingListApi, WMSCategoryListAPI, } from "@/views/BsrRanking/api"
import type { CategoryPageAPI, } from "./apis/list"
import { Icon, } from "@/components/Icon"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { virtualScrollProps, } from "@/setup"

import { trackDialogEvent, trackEvent, } from "@/utils/monitor"
import { queryCategoryTree, } from "@/views/BsrRanking/api"
import { FormConfig, } from "@/views/BsrRanking/category-config/help"
import { getCategoryListByPage, } from "./apis/list"
import AddDialog from "./components/AddDialog.vue"
import BatchEditDialog from "./components/BatchEditDialog.vue"
import EditStatusDialog from "./components/EditStatusDialog.vue"

defineOptions({
  name: "CategoryConfig",
},)
type FormModel = CategoryPageAPI.Params & {
  productCategoryName?: string[]
}
const defaultFormData: FormModel = {}
const wmsCategoryList = ref<WMSCategoryListAPI.Data[]>([],)
const formData = reactive<FormModel>({
  ...defaultFormData,
},)
const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getCategoryListByPage,
  formData,
  formatParams: (formData,) => {
    const ids = formData?.ids?.map(subArr => subArr[subArr?.length - 1],) || []
    return {
      ...formData,
      productAllCategoryName: formData?.productCategoryName?.join("/",),
      ids,
    }
  },
},)
function useOperation() {
  const selectedRows = ref<CategoryPageAPI.List>([],)
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as CategoryPageAPI.List
  }

  const addDialogVisible = ref(false,)
  const editDialogVisible = ref(false,)
  const statusDialogVisible = ref(false,)

  const handleCreate = () => {
    trackDialogEvent("add", "新增类目",)
    addDialogVisible.value = true
  }

  const handleEdit = () => {
    trackDialogEvent("edit", "编辑类目",)
    if (selectedRows.value.length === 0) {
      ElMessage.warning("请至少选择1行数据",)
      return
    }
    editDialogVisible.value = true
  }

  const handleChangeStatus = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning("请至少选择1行数据",)
      return
    }
    statusDialogVisible.value = true
  }

  return {
    selectedRows,
    handleCreate,
    addDialogVisible,
    editDialogVisible,
    handleEdit,
    statusDialogVisible,
    handleChangeStatus,
    handleSelectChange,
  }
}

const {
  selectedRows,
  editDialogVisible,
  addDialogVisible,
  handleSelectChange,
  handleCreate,
  handleEdit,
  statusDialogVisible,
  handleChangeStatus,
} = useOperation()

const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()
function handleExport() {
  let reqParam: string
  if (selectedRows.value?.length > 0) {
    const ids = selectedRows.value.map(item => item.id,)
    reqParam = JSON.stringify({ ids, ...pager, },)
  } else {
    const ids = formData?.ids?.map(subArr => subArr[subArr?.length - 1],) || []
    reqParam = JSON.stringify({
      ...formData,
      ids,
      productAllCategoryName: formData?.productCategoryName?.join("/",),
    },)
  }
  trackEvent("export", "导出",)
  exportFn({
    exportType: "platformCategory-export-plm",
    reqParam,
  },)
}// 修改formConfigComputed计算属性
const formConfigComputed = computed(() => {
  return FormConfig.map((item,) => {
    const newItem = { ...item, }
    switch (newItem.field) {
      case "ids":
        newItem.params = {
          ...newItem.params,
          station: formData.station,
        }
        break
    }
    return newItem
  },)
},)
watch(formConfigComputed, (newConfig,) => {
  console.log("表单配置变更:", newConfig.map(c => ({
    field: c.field,
    params: c.params,
  }),),)
}, { immediate: true, },)
async function queryCateogry() {
  const response = await queryCategoryTree()
  if (response?.success) {
    wmsCategoryList.value = response?.data
  }
}
onMounted(queryCateogry,)
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <LayoutForm
          v-if="hasPermission('categoryConfig:search')"
          ref="formRef"
          :descriptions="FormConfig"
          :loading="loading"
          :model="formData"
          :span="6"
          query-form
          @reset="handleReset"
          @search="handleSearch"
        >
          <ElFormItem
            v-for="({ label, layout, field, props, ...other }, index) in formConfigComputed"
            :key="index"
            :label="label"
          >
            <component
              :is="layout as Component"
              v-model="formData[field as keyof BsrRankingListApi.Request]"
              v-bind="{ ...other,
                        key: JSON.stringify(other.params),
                        ...props, // 级联处理
                        params: other.params,
              }"
            />
          </ElFormItem>
        </LayoutForm>
        <div class="mb-[10px] min-h-8">
          <ElButton
            v-hasPermi="['categoryConfig:add']"
            type="primary"
            @click="handleCreate"
          >
            <Icon :size="20" icon="ep:plus" />
            新增类目
          </ElButton>
          <ElButton
            v-hasPermi="['categoryConfig:edit']"
            type="primary"
            @click="handleEdit"
          >
            <Icon :size="20" icon="ep:edit" />
            批量修改
          </ElButton>
          <ElButton
            v-hasPermi="['categoryConfig:changeStatus']"
            type="primary"
            @click="handleChangeStatus"
          >
            <Icon :size="20" icon="ep:share" />
            启用/禁用
          </ElButton>
          <ElButton
            v-hasPermi="['categoryConfig:export']"
            :loading="exportLoading"
            type="primary"
            @click="handleExport"
          >
            <Icon :size="20" icon="ep:download" />
            导出
          </ElButton>
        </div>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="loading"
          :max-height="maxHeight - 100"
          :min-height="100"
          :sort-config="{
            multiple: true,
          }"
          show-overflow="tooltip"
          v-bind="virtualScrollProps"
          @checkbox-all="handleSelectChange"
          @checkbox-change="handleSelectChange"
        >
          <VxeColumn type="checkbox" width="40" />
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn
            field="station"
            title="站点"
            width="120"
            sortable
          />
          <VxeColumn field="categoryAllName" title="平台类目" width="120" />
          <VxeColumn field="productAllCategoryName" title="MMT类目" width="120" />
          <VxeColumn field="platformUrl" title="对应链接" width="100">
            <template #default="{ row }: { row: CategoryPageAPI.Row }">
              <ElLink :href="row.platformUrl" target="_blank" type="primary">
                {{ row.platformUrl }}
              </ElLink>
            </template>
          </VxeColumn>
          <VxeColumn
            field="targetAudience"
            title="人群"
            width="100"
            sortable
          />
          <VxeColumn field="targetSex" title="性别" width="100" />
          <VxeColumn
            field="categoryStyle"
            title="风格"
            width="100"
            sortable
          />
          <VxeColumn
            field="mainCategory"
            title="大类"
            width="125"
            sortable
          />
          <VxeColumn
            field="classificationName"
            title="类目名称"
            width="100"
            sortable
          />
          <VxeColumn field="applicableSeasonName" title="季节" width="100" />
          <VxeColumn field="statusName" title="状态" width="100" />
          <VxeColumn
            field="effectiveDate"
            title="生效日期"
            width="150"
            sortable
          />
          <VxeColumn field="modifyByName" title="操作人" width="100" />
          <VxeColumn field="modifyTime" title="操作时间" width="160" />
        </VxeTable>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.currPage"
        v-model:page-size="pager.pageSize"
        :total="pager.totalCount"
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @click="handlePagerChange"
      />
      <AddDialog
        v-model="addDialogVisible"
        @refresh="handleSearch"
      />
      <BatchEditDialog
        v-model="editDialogVisible"
        :selected-rows="selectedRows"
        :wms-category-list="wmsCategoryList"
        @refresh="handleSearch"
      />
      <EditStatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        :wms-category-list="wmsCategoryList"
        @refresh="handleSearch"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
