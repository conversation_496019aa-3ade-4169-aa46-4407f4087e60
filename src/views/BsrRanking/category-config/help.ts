import { ApiSelect, } from "@/components/ApiSelect"
import { DictSelect, } from "@/components/DictSelect"
import { ElCascader, } from "@/components/ElCascader"
import { getBsrCategoryList, } from "@/views/BsrRanking/api"
import { queryCategoryTree, } from "@/views/BsrRanking/api/index"
import { ElInput, } from "element-plus"

export enum StatusEnum {
  /**
   * 启用
   */
  START = "start",
  /**
   * 禁用
   */
  BAN = "ban",
}

export const FormConfig = [
  {
    label: "站点",
    layout: DictSelect,
    field: "station",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "station",
      apiConfig: {
        config: {
          label: "dictEnName",
          value: "dictValue",
        },
      },
    },
    apiKey: "station",
  },
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "productCategoryName",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      clearable: true,
      filterable: true,
    },
  },
  {
    "label": "平台类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "ids",
    "params": {
      station: "",
    },
    "api-config": {
      api: getBsrCategoryList,
      config: {
        label: "selectorEnValue",
        value: "selectorKey",
        children: "childList",
      },
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "props": {
        multiple: true,
      },
    },
  },
  {
    label: "人群",
    layout: DictSelect,
    field: "targetAudiences",
    props: {
      "multiple": true,
      "dictCode": "PLATFORM_PRODUCT_PEOPLE",
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
  },
  {
    label: "风格",
    layout: DictSelect,
    field: "categoryStyles",
    props: {
      "clearable": true,
      "multiple": true,
      "dictCode": "PLATFORM_PRODUCT_STYLE",
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "大类",
    layout: DictSelect,
    field: "mainCategorys",
    props: {
      "clearable": true,
      "dictCode": "PLATFORM_MAIN_CATEGORY",
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "类目名称",
    layout: DictSelect,
    field: "classificationNames",
    props: {
      "clearable": true,
      "dictCode": "CLASSIFICATION_NAME",
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
]
export const editFormConfig = [
  {
    label: "站点",
    layout: DictSelect,
    field: "station",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "station",
      apiConfig: {
        config: {
          label: "dictEnName",
          value: "dictValue",
        },
      },
    },
    rules: [
      {
        required: true,
        message: "请选择站点",
        trigger: "change",
      },
    ],
    apiKey: "station",
  },
  {
    label: "平台类目",
    field: "categoryAllName",
    layout: ElInput,
    props: {
      maxlength: "200",
      clearable: true,
      showWordLimit: true,
      placeholder: "请输入完整的亚马逊类目",
    },
  },
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "productCategoryName",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "multiple": true,
      "props": {
        multiple: true,
      },
    },
  },
  {
    label: "人群",
    layout: DictSelect,
    field: "targetAudience",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "PRODUCT_PEOPLE",
    },
    rules: [
      {
        required: true,
        message: "请选择人群",
        trigger: "change",
      },
    ],
  },
  {
    label: "风格",
    layout: DictSelect,
    field: "categoryStyleList",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "PLATFORM_PRODUCT_STYLE",
      multiple: true,
    },
    rules: [
      {
        required: true,
        message: "请选择风格",
        trigger: "change",
      },
    ],
  },
  {
    label: "性别",
    layout: DictSelect,
    field: "targetSexList",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "PLATFORM_CATEGORY_SEX",
      multiple: true,
    },
    rules: [
      {
        required: true,
        message: "请选择性别",
        trigger: "change",
      },
    ],
  },
  {
    label: "大类",
    layout: DictSelect,
    field: "mainCategoryList",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "PLATFORM_MAIN_CATEGORY",
      multiple: true,
    },
    rules: [
      {
        required: true,
        message: "请选择大类",
        trigger: "change",
      },
    ],
  },
  {
    label: "类目名称",
    layout: DictSelect,
    field: "classificationNameList",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "CLASSIFICATION_NAME",
      multiple: true,
    },
    rules: [
      {
        required: true,
        message: "请选择类目名称",
        trigger: "change",
      },
    ],
  },
  {
    label: "季节",
    layout: DictSelect,
    field: "applicableSeason",
    props: {
      clearable: true,
      filterable: true,
      dictCode: "PLATFORM_CATEGORY_SEASON",
    },
    rules: [
      {
        required: true,
        message: "请选择季节",
        trigger: "change",
      },
    ],
  },
  {
    "label": "状态",
    "layout": ApiSelect,
    "component": ElRadioGroup,
    "childComponent": ElRadio,
    "field": "status",
    "props": {
      clearable: true,
      filterable: true,
    },
    "api-config": {
      api: () => {
        return {
          code: 0,
          data: [
            { label: "启用", value: StatusEnum.START, },
            { label: "禁用", value: StatusEnum.BAN, },
          ],
        }
      },
      config: {
        label: "label",
        value: "value",
      },

    },
    "rules": [
      {
        required: true,
        message: "请选择状态",
        trigger: "change",
      },
    ],
  },
  {
    label: "对应链接",
    layout: ElInput,
    field: "platformUrl",
    props: {
      type: "textarea",
      clearable: true,
      placeholder: "请输入对应链接",
      maxlength: 500,
      showWordLimit: true,
      autosize: {
        minRows: 2,
        maxRows: 4,
      },
    },
  },
]
