// 标签管理列表配置
export const TableColumn = [
  {
    type: "checkbox",
    fixed: "left",
    width: 40,
  },
  {
    type: "seq",
    title: "序号",
    width: 54,
    fixed: "left",
  },
  {
    title: "标签类型",
    field: "tagTypeName",
    width: 100,
    sortable: true,
  },
  {
    title: "一级标签",
    field: "firstLevelTagName",
    width: 200,
    sortable: true,
  },
  {
    title: "二级标签",
    field: "secondLevelTagName",
    width: 200,
    sortable: true,
  },
  {
    title: "二级标签英文名称",
    field: "secondLevelTagEn",
    minWidth: 250,
  },
  {
    title: "标签定义描述",
    field: "tagDescription",
    minWidth: 250,
    slots: {
      default: "tagDescription",
    },
  },
  {
    title: "正例描述",
    field: "positiveExample",
    width: 200,
  },
  {
    title: "反例描述",
    field: "negativeExample",
    width: 200,
  },
  {
    title: "生效日期",
    field: "effectiveDate",
    sortable: true,
    minWidth: 120,
  },
  {
    title: "操作人",
    field: "modifyByName",
    minWidth: 120,
  },
  {
    title: "操作时间",
    field: "modifyTime",
    minWidth: 150,
  },
  {
    title: "打标状态",
    field: "labelingStatusName",
    minWidth: 100,
    fixed: "right",
    slots: {
      default: "labelingStatusName",
    },
  },
  {
    title: "正确率",
    field: "accuracy",
    minWidth: 100,
    fixed: "right",
    titleSuffix: { content: "模型预测正确的样本数 / 总样本数", },
  },
  {
    title: "F1-Score",
    field: "f1Score",
    minWidth: 120,
    fixed: "right",
    titleSuffix: { content: "F1-score 是精确率和召回率的调和平均值，在类别不平衡时尤其重要", },
  },
  {
    title: "样本量",
    field: "sampleCount",
    minWidth: 100,
    fixed: "right",
    slots: {
      default: "sampleCount",
    },
    titleSuffix: { content: "计算训练标签正确率的评论样本数据量", },
  },
  {
    title: "状态",
    field: "statusName",
    minWidth: 100,
    fixed: "right",
  },
  {
    title: "操作项",
    field: "operation",
    width: 120,
    fixed: "right",
    slots: {
      default: "operation",
    },
  },
]
