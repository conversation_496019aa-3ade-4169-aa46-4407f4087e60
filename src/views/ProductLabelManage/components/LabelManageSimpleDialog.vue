<script setup lang="ts">
import type { LabelManageSimpleList, } from "@/apis/productQualityAnalysis/types.ts"
import { getSimpleListById, saveSimpleList, } from "@/apis/productQualityAnalysis"
import MarkLabelDialog from "@/views/ProductLabelManage/components/MarkLabelDialog.vue"

const props = defineProps<{
  id: number
}>()

const emits = defineEmits<{
  (e: "refresh"): void
}>()

const tableRef = useTemplateRef("tableRef",)
const tableData = ref<LabelManageSimpleList.List>([],)
const visible = defineModel<boolean>("visible",)

const isQuerying = ref<boolean>(false,)
watch(() => visible.value, (val,) => {
  if (!val) {
    return false
  }
  isQuerying.value = true
  getSimpleListById(props.id,).then((res,) => {
    tableData.value = res?.data ?? []
  },).finally(() => {
    isQuerying.value = false
  },)
},)

const isLoading = ref<boolean>(false,)

function onClose(done: () => void,) {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  done()
}
function onCancel() {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  visible.value = false
}

function onRefresh() {
  visible.value = false
  emits("refresh",)
}

// 保存和打标
const markLabelVisible = ref<boolean>(false,)

function onConfirm() {
  if (!tableData.value?.length) {
    ElMessage.warning("没有查询到需要保存的数据",)
    return false
  }
  isLoading.value = true
  saveSimpleList(tableData.value,).then((res,) => {
    console.error(res,)
    if (res?.success) {
      markLabelVisible.value = true
    }
  },).finally(() => {
    isLoading.value = false
  },)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="样本数据详情"
    top="15vh"
    width="1100px"
    append-to-body
  >
    <div class="table-wrapper">
      <VxeTable
        ref="tableRef"
        :border="true"
        :data="tableData"
        :show-overflow="true"
        max-height="500px"
      >
        <VxeColumn type="seq" width="55" />
        <VxeColumn
          field="site"
          min-width="100"
          title="站点"
        />
        <VxeColumn
          field="commentId"
          min-width="120"
          title="评论ID"
        />
        <VxeColumn
          field="commentDate"
          min-width="160"
          title="评论日期"
        />
        <VxeColumn
          field="rating"
          min-width="80"
          title="评分"
        />
        <VxeColumn
          field="commentTitle"
          min-width="100"
          title="评论标题"
        />
        <VxeColumn
          field="commentText"
          min-width="200"
          title="评论原文"
        />
        <VxeColumn
          field="suggestionTag"
          min-width="120"
          title="建议标签值"
        />
        <VxeColumn
          field="tagCategory"
          min-width="120"
          title="标签分类"
        />
        <VxeColumn
          field="aiExampleTypeName"
          min-width="100"
          title="AI 正 / 反例"
        />
        <VxeColumn
          field="exampleType"
          min-width="100"
          title="正 / 反例"
        >
          <template #default="{ row }">
            <ElSelect v-model="row.exampleType">
              <ElOption :value="0" label="正例" />
              <ElOption :value="1" label="反例" />
            </ElSelect>
          </template>
        </VxeColumn>
      </VxeTable>
    </div>
    <template #footer>
      <div class="footer-actions">
        <ElButton :disabled="isLoading" @click="onCancel">
          关闭
        </ElButton>
        <ElButton
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onConfirm"
        >
          保存
        </ElButton>
      </div>
    </template>

    <MarkLabelDialog
      v-model:visible="markLabelVisible"
      :ids="[id]"
      text="内容已保存，是否针对修改的内容，重新打标，更新正确率？"
      @refresh="onRefresh"
    />
  </ElDialog>
</template>

<style scoped lang="scss">
.header-actions {
  padding-bottom: 10px;
}
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
