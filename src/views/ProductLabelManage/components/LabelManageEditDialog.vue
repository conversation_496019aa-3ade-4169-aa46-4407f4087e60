<script setup lang="ts">
import type { LabelManageDataEdit, LabelManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeTablePropTypes, } from "vxe-table"
import { addLabelManageData, editLabelManageData, } from "@/apis/productQualityAnalysis"
import { ApiSelect, } from "@/components/ApiSelect"
import { DictSelect, } from "@/components/DictSelect"
import { LabelStatusEnum, } from "@/enums"
import { queryTagStructureBox, } from "@/views/Label/apis/Info.ts"
import MarkLabelDialog from "@/views/ProductLabelManage/components/MarkLabelDialog.vue"

const props = defineProps<{
  rows: LabelManagePageList.List
}>()

const emits = defineEmits<{
  (e: "refresh"): void
}>()

const tableRef = useTemplateRef("tableRef",)

const visible = defineModel<boolean>("visible",)

// 是否为批量编辑
const isEdit = computed<boolean>(() => {
  return props.rows?.length > 0
},)

// 标题
const title = computed<string>(() => {
  return !isEdit.value ? "批量新增" : "批量修改"
},)

// 标签数据
const apiConfig = reactive({
  api: queryTagStructureBox,
  config: {
    label: "name",
    value: "id",
    children: "child",
  },
},)

// 表格数据处理
const defaultData: LabelManageDataEdit.Row = {
  tagType: "",
  firstLevelTagId: undefined,
  firstLevelTagName: "",
  secondLevelTagId: undefined,
  secondLevelTagName: "",
  secondLevelTagEn: "",
  tagDescription: "",
  positiveExample: "",
  negativeExample: "",
}
const tableData = ref<LabelManageDataEdit.Params>([],)
function addData(num: number,): void {
  for (let i = 0; i < num; i++) {
    tableData.value.unshift({ ...defaultData, },)
  }
}
function onAdd() {
  if (tableData.value.length > 19) {
    ElMessage.warning("一次最多只能添加20行数据",)
    return false
  }
  addData(1,)
}
function onDelete() {
  const selectedData: LabelManageDataEdit.Params | undefined = tableRef.value?.getCheckboxRecords()
  if (!selectedData?.length) {
    ElMessage.warning("请先选择要删除的数据",)
    return false
  }
  ElMessageBox.confirm("确认要删除选择的数据？", "确认", { type: "warning", },)
    .then(() => {
      const arr: string[] = selectedData.map((item,) => {
        return item._X_ROW_KEY as string
      },)
      tableData.value = tableData.value.filter((row: LabelManageDataEdit.Row,) => {
        if (!row._X_ROW_KEY) {
          return true
        }
        return !arr.includes(row._X_ROW_KEY,)
      },)
    },)
    .catch(() => {},)
}
function onOptionChange(row: LabelManageDataEdit.Row, key: string, val: any,) {
  row[key] = val
}
watch(() => visible.value, (val,) => {
  tableData.value = []
  if (!val) {
    return false
  }
  if (!props.rows?.length) {
    addData(5,)
    return false
  }
  props.rows.forEach((row,) => {
    tableData.value.push({
      id: row.id,
      tagType: row.tagType,
      tagTypeName: row.tagTypeName,
      firstLevelTagId: row.firstLevelTagId,
      firstLevelTagName: row.firstLevelTagName ?? "",
      secondLevelTagId: row.secondLevelTagId,
      secondLevelTagName: row.secondLevelTagName ?? "",
      secondLevelTagEn: row.secondLevelTagEn ?? "",
      tagDescription: row.tagDescription ?? "",
      positiveExample: row.positiveExample ?? "",
      negativeExample: row.negativeExample ?? "",
      status: row.status ?? LabelStatusEnum.START,
    },)
  },)
},)
// 校验
const validRules = ref<VxeTablePropTypes.EditRules<LabelManageDataEdit.Row>>({
  tagType: [
    { required: true, message: "必须填写", },
  ],
  firstLevelTagId: [
    { required: true, message: "必须填写", },
  ],
  secondLevelTagId: [
    { required: true, message: "必须填写", },
  ],
  secondLevelTagEn: [
    { required: true, message: "必须填写", },
  ],
},)

async function fullValid() {
  const $table = tableRef.value
  if (!tableData.value?.length) {
    ElMessage.warning("至少需要一条数据",)
    return false
  }
  if ($table) {
    const errMap = await $table.validate(true,)
    return !errMap
  }
  return false
}

// 退出
const isLoading = ref<boolean>(false,)
// 重新打标
const markLabelVisible = ref<boolean>(false,)
const ids = ref<number[]>([],)
function onClose(done: () => void,) {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  done()
}
function onCancel() {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  visible.value = false
}
async function onConfirm() {
  const checkResult = await fullValid()
  if (!checkResult) {
    return false
  }
  isLoading.value = true
  const method = isEdit.value ? editLabelManageData : addLabelManageData
  method(tableData.value,).then((res,) => {
    if (res?.success) {
      if (isEdit.value) {
        markLabelVisible.value = true
        ids.value = tableData.value.map(item => item.id as number,)
      } else {
        ElMessage.success("新增成功",)
        onRefresh()
      }
    }
  },).finally(() => {
    isLoading.value = false
  },)
}

function onRefresh() {
  visible.value = false
  emits("refresh",)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :title="title"
    top="15vh"
    width="1100px"
    append-to-body
  >
    <div v-if="!isEdit" class="header-actions">
      <ElButton :disabled="isLoading" type="primary" @click="onAdd">
        添加
      </ElButton>
      <ElButton :disabled="isLoading" @click="onDelete">
        删除
      </ElButton>
    </div>
    <div class="table-wrapper">
      <VxeTable
        ref="tableRef"
        :border="true"
        :cell-config="{ height: 80 }"
        :data="tableData"
        :edit-config="{ mode: 'row', trigger: 'click', autoClear: false, showStatus: true }"
        :edit-rules="validRules"
        :show-overflow="true"
        max-height="500px"
      >
        <VxeColumn v-if="!isEdit" type="checkbox" width="50" />
        <VxeColumn type="seq" width="55" />
        <VxeColumn
          :edit-render="isEdit ? undefined : {}"
          field="tagType"
          min-width="200"
          title="标签类型"
        >
          <template v-if="!isEdit" #edit="{ row }">
            <DictSelect
              v-model="row.tagType"
              dict-code="PLM_TAG_TYPE"
              clearable
              filterable
              @option-change="onOptionChange(row, 'tagTypeName', $event.label)"
            />
          </template>
          <template #default="{ row }">
            <span>{{ row.tagTypeName }}</span>
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="isEdit ? undefined : {}"
          field="firstLevelTagId"
          min-width="200"
          title="一级标签"
        >
          <template v-if="!isEdit" #edit="{ row }">
            <ApiSelect
              v-model="row.firstLevelTagId"
              :api-config="apiConfig"
              :params="{ tagType: 'review', tagLevel: 1, parentIdList: [0] }"
              clearable
              filterable
              immediate
              @option-change="onOptionChange(row, 'firstLevelTagName', $event.label)"
            />
          </template>
          <template #default="{ row }">
            <span>{{ row.firstLevelTagName }}</span>
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{}"
          field="secondLevelTagId"
          min-width="200"
          title="二级标签"
        >
          <template #edit="{ row }">
            <ApiSelect
              v-model="row.secondLevelTagId"
              :api-config="apiConfig"
              :params="{ tagType: 'review', tagLevel: 2, parentIdList: [row.firstLevelTagId] }"
              clearable
              filterable
              immediate
              @option-change="onOptionChange(row, 'secondLevelTagName', $event.label)"
            />
          </template>
          <template #default="{ row }">
            <span>{{ row.secondLevelTagName }}</span>
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="secondLevelTagEn"
          min-width="200"
          title="二级标签英文名称"
        >
          <template #edit="{ row }">
            <VxeTextarea v-model="row.secondLevelTagEn" max-length="200" clearable />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="tagDescription"
          min-width="200"
          title="标签定义描述"
        >
          <template #edit="{ row }">
            <VxeTextarea
              v-model="row.tagDescription"
              max-length="2000"
              clearable
              show-word-count
            />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="positiveExample"
          min-width="200"
          title="正例描述"
        >
          <template #edit="{ row }">
            <VxeTextarea
              v-model="row.positiveExample"
              max-length="2000"
              clearable
              show-word-count
            />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="negativeExample"
          min-width="200"
          title="反例描述"
        >
          <template #edit="{ row }">
            <VxeTextarea
              v-model="row.negativeExample"
              max-length="2000"
              clearable
              show-word-count
            />
          </template>
        </VxeColumn>
        <VxeColumn
          v-if="isEdit"
          field="status"
          min-width="80"
          title="状态"
        >
          <template #default="{ row }">
            <ElSwitch v-model="row.status" :active-value="LabelStatusEnum.START" :inactive-value="LabelStatusEnum.BAN" />
          </template>
        </VxeColumn>
      </VxeTable>
    </div>
    <template #footer>
      <div class="footer-actions">
        <ElButton :disabled="isLoading" @click="onCancel">
          关闭
        </ElButton>
        <ElButton
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onConfirm"
        >
          保存
        </ElButton>
      </div>
    </template>

    <MarkLabelDialog
      v-model:visible="markLabelVisible"
      :ids="ids"
      text="内容已保存，是否针对修改的内容，重新打标，更新正确率？"
      @refresh="onRefresh"
    />
  </ElDialog>
</template>

<style scoped lang="scss">
.header-actions {
  padding-bottom: 10px;
}
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
