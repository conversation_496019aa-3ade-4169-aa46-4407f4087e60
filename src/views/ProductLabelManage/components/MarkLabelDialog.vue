<script setup lang="ts">
import { markLabelById, } from "@/apis/productQualityAnalysis"
import { trackEvent, } from "@/utils/monitor.ts"
import { WarningFilled, } from "@element-plus/icons-vue"

const props = defineProps<{
  ids: number[]
  text: string
  // 点击关闭时是否往上抛出刷新事件
  noCancelRefresh?: boolean
}>()

const emits = defineEmits<{
  (e: "refresh"): void
}>()

// 重新打标
const markLabelVisible = defineModel<boolean>("visible",)

const isLoading = ref<boolean>(false,)

function onCancel() {
  if (isLoading.value) {
    return false
  }
  markLabelVisible.value = false
  if (!props.noCancelRefresh) {
    emits("refresh",)
  }
}
function onConfirm() {
  if (!props.ids?.length) {
    ElMessage.warning("没有检测到需要打标的数据",)
    return false
  }
  trackEvent("Action", "标签管理页面执行打标",)
  isLoading.value = true
  markLabelById(props.ids,)
    .then((res,) => {
      console.error(res,)
      if (res?.success) {
        ElMessage.success("打标成功",)
        markLabelVisible.value = false
        emits("refresh",)
        return false
      }
    },)
    .finally(() => {
      isLoading.value = false
    },)
}
</script>

<template>
  <ElDialog
    v-model="markLabelVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    title="样本打标确认"
    top="15vh"
    width="704px"
    append-to-body
  >
    <template #header>
      <span style="font-size: 18px; font-weight: 700">样本打标确认</span>
    </template>
    <div class="mark-label">
      <div class="warning-icon">
        <div class="warning-icon-inner">
          <ElIcon color="#FF9900" size="32px">
            <WarningFilled />
          </ElIcon>
        </div>
      </div>
      <div class="mark-label-base">
        样本打标确认中
      </div>
      <div class="mark-label-text">
        {{ text ?? '' }}
      </div>
    </div>
    <template #footer>
      <div class="footer-actions">
        <ElButton :disabled="isLoading" @click="onCancel">
          关闭
        </ElButton>
        <ElButton
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onConfirm"
        >
          保存
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
.mark-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.warning-icon {
  width: 120px;
  height: 120px;
  background-color: #FF9900;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  .warning-icon-inner {
    width: 72px;
    height: 72px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.mark-label-base {
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  color: #646464;
}

.mark-label-text {
  width: 440px;
  text-align: center;
  font-size: 16px;
  line-height: 24px;
}
</style>
