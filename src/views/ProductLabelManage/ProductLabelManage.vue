<script setup lang="ts">
import type { LabelManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeGridProps, VxeTablePropTypes, } from "vxe-table"
import { getLabelManageList, queryTagStructureBoxNew, tagManageStartOrBan, } from "@/apis/productQualityAnalysis"
import { ApiSelect, } from "@/components/ApiSelect"
import { ContentWrap, } from "@/components/ContentWrap"
import { DictSelect, } from "@/components/DictSelect"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { LabelStatusEnum, } from "@/enums"
import { useHandleExport, } from "@/hooks/useExport.tsx"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { trackDialogEvent, trackEvent, } from "@/utils/monitor.ts"
import LabelManageEditDialog from "@/views/ProductLabelManage/components/LabelManageEditDialog.vue"
import LabelManageSimpleDialog from "@/views/ProductLabelManage/components/LabelManageSimpleDialog.vue"
import MarkLabelDialog from "@/views/ProductLabelManage/components/MarkLabelDialog.vue"
import { EditPen, Plus, } from "@element-plus/icons-vue"
import { ref, } from "vue"
import { TableColumn, } from "./const.ts"

// 查询初始化
const formData = reactive<LabelManagePageList.Params>({
  tagTypeList: [],
  firstLevelTagIdList: [],
  secondLevelTagIdList: [],
  accuracy: undefined,
  relationOperator: undefined,
  statusList: [],
},)
const rules = computed(() => {
  return {
    relationOperator: [{ required: !!formData.accuracy || formData.accuracy === 0, message: "数学符号必选", },],
  }
},)
// const tagTypeOptions = ref<DictAPI.DictValue[]>([],)
// getDictList().then((res,) => {
//   const data = res?.data ?? []
//   data.forEach((item,) => {
//     if (item.dictItem === "PLM_TAG_TYPE") {
//       tagTypeOptions.value = item.dictValueList ?? []
//     }
//   },)
// },)

const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getLabelManageList,
  formData,
  resetWithSearch: true,
  formatParams: (formData,) => {
    const obj = {
      ...formData,
    }
    if (!obj.accuracy && obj.accuracy !== 0) {
      delete obj.relationOperator
    }
    return obj
  },
},)

// 标签数据
const apiConfig = reactive({
  api: queryTagStructureBoxNew,
  config: {
    label: "name",
    value: "id",
    children: "child",
  },
},)

// 表格配置
const tableOptions = computed(() => ({
  columns: TableColumn,
  maxHeight: maxHeight.value - 50,
  minHeight: 280,
  border: "inner",
  scrollX: {
    enabled: true,
    gt: 20,
  },
  scrollY: {
    enabled: true,
    gt: 20,
  },
  loading: loading.value,
  data: (tableData.value) as LabelManagePageList.Row[],
} as VxeGridProps<LabelManagePageList.Row>),)

// 导出
const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()
function downloadAction() {
  const selected: LabelManagePageList.Row[] | undefined
      = tableRef.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected.length > 0) {
    reqParam = JSON.stringify({
      idList: selected.map((item: LabelManagePageList.Row,) => item.id,),
    },)
  } else {
    const obj = {
      ...formData,
    }
    if (!obj.accuracy && obj.accuracy !== 0) {
      delete obj.relationOperator
    }
    reqParam = JSON.stringify(obj,)
  }
  trackEvent("Export", "标签管理页面导出",)
  exportFn({
    exportType: "qualityTagManager-export",
    reqParam,
  },)
}

// 启用/禁用
async function changeStatus(row: LabelManagePageList.Row,) {
  await tagManageStartOrBan({
    updateStatusReqList: [
      {
        id: row.id,
        oldStatus: row.status,
        newStatus: row.status === LabelStatusEnum.START ? LabelStatusEnum.BAN : LabelStatusEnum.START,
      },
    ],
  },)
  trackEvent("Action", "标签管理页面启用/禁用切换",)
  ElMessage.success("状态更新成功",)
  handleSearch()
}

// 新增/编辑
const editVisible = ref<boolean>(false,)
// 数据检测
function validData(): boolean {
  const selected: LabelManagePageList.Row[] | undefined
      = tableRef.value?.getCheckboxRecords()
  if (!selected?.length) {
    ElMessage.warning("请先勾选数据后再进行操作",)
    return false
  }
  if (selected.length > 10) {
    ElMessage.warning("批量操作最多不超过10条数据",)
    return false
  }
  return true
}
const checkedData = ref<LabelManagePageList.Row[]>([],)
// 新增
function handleAdd() {
  editVisible.value = true
  checkedData.value = []
  trackDialogEvent("标签管理页面批量新增",)
}

// 修改
function handleEdit() {
  if (!validData()) {
    return false
  }
  checkedData.value = tableRef.value?.getCheckboxRecords() ?? []
  if (checkedData.value.length === 0) {
    ElMessage.warning("没有获取到相应数据",)
    return false
  }
  editVisible.value = true
  trackDialogEvent("标签管理页面批量修改",)
}

// 打标
const markLabelVisible = ref<boolean>(false,)
const ids = ref<number[]>([],)
function markLabel() {
  if (!validData()) {
    return false
  }
  checkedData.value = tableRef.value?.getCheckboxRecords() ?? []
  if (checkedData.value.length === 0) {
    ElMessage.warning("没有获取到相应数据",)
    return false
  }
  ids.value = checkedData.value.map((item: LabelManagePageList.Row,) => item.id,)
  markLabelVisible.value = true
  trackDialogEvent("标签管理页面批量打标",)
}

// 样本
const simpleVisible = ref<boolean>(false,)
const currentId = ref<number>(0,)
function simpleCheck(row: LabelManagePageList.Row,) {
  if (!row?.id) {
    return false
  }
  currentId.value = row.id
  simpleVisible.value = true
  trackDialogEvent("标签管理页面查看样本",)
}

// 排序
interface RowVO {
  tagTypeName: string
  firstLevelTagName: string
  secondLevelTagName: string
  effectiveDate: Date
}
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  multiple: true,
},)
function handleSortChange({ order, property, },) {
  if (order) {
    tableData.value.sort((a, b,) => {
      if (a[property] < b[property]) {
        return order === "asc" ? -1 : 1
      }
      if (a[property] > b[property]) {
        return order === "asc" ? 1 : -1
      }
      return 0
    },)
  }
}
</script>

<template>
  <ContentWrap>
    <div v-if="hasPermission('productLabelManage:search')" class="search">
      <LayoutForm
        ref="formRef"
        :loading="loading"
        :model="formData"
        :rules="rules"
        :span="6"
        class="customer-form"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem label="标签类型" prop="tagTypeList">
          <DictSelect
            v-model="formData.tagTypeList"
            dict-code="PLM_TAG_TYPE"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="一级标签" prop="firstLevelTagIdList">
          <ApiSelect
            v-model="formData.firstLevelTagIdList"
            :api-config="apiConfig"
            :params="{ tagLevel: 1, parentIdList: [0], tagTypeList: formData.tagTypeList ?? [] }"
            clearable
            collapse-tags
            filterable
            immediate
            multiple
          />
        </ElFormItem>
        <ElFormItem label="二级标签" prop="secondLevelTagIdList">
          <ApiSelect
            v-model="formData.secondLevelTagIdList"
            :api-config="apiConfig"
            :params="{ tagLevel: 2, parentIdList: formData.firstLevelTagIdList ?? [], tagTypeList: formData.tagTypeList ?? [] }"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="正确率" prop="relationOperator">
          <div class="customer-combo">
            <DictSelect
              v-model="formData.relationOperator"
              class="custom-combo-select"
              dict-code="PLM_RELATION_OPERATE"
              style="width: 110px"
              clearable
              filterable
            />
            <ElInputNumber
              v-model="formData.accuracy"
              :max="1"
              :min="0"
              :precision="2"
              :step="0.01"
              class="custom-combo-input"
              controls-position="right"
              style="flex: 1"
            />
          </div>
        </ElFormItem>
        <ElFormItem label="状态" prop="statusList">
          <DictSelect
            v-model="formData.statusList"
            dict-code="PLM_STATUS"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
      </LayoutForm>
    </div>
    <div class="operation">
      <ElRow :span="24" justify="space-between">
        <ElCol :span="12">
          <ElButton
            v-hasPermi="['productLabelManage:add']"
            type="primary"
            @click="handleAdd"
          >
            <template #icon>
              <ElIcon>
                <Plus />
              </ElIcon>
            </template>
            批量新增
          </ElButton>
          <ElButton
            v-hasPermi="['productLabelManage:edit']"
            type="primary"
            @click="handleEdit"
          >
            <template #icon>
              <ElIcon>
                <EditPen />
              </ElIcon>
            </template>
            批量修改
          </ElButton>
          <ElButton
            v-hasPermi="['productLabelManage:mark']"
            type="primary"
            @click="markLabel"
          >
            样本打标
          </ElButton>
          <ElButton
            v-hasPermi="['productLabelManage:export']"
            :loading="exportLoading"
            type="primary"
            @click="downloadAction"
          >
            导出
          </ElButton>
        </ElCol>
      </ElRow>
    </div>
    <div v-loading="loading" class="content">
      <VxeGrid
        ref="tableRef"
        v-bind="tableOptions"
        :cell-config="{ height: 90 }"
        :header-cell-config="{ height: 48 }"
        :sort-config="sortConfig"
        @sort-change="handleSortChange"
      >
        <template #tagDescription="{ row }">
          <div style="word-break: break-all; white-space: pre-wrap;">
            <ElText line-clamp="4">
              {{ row.tagDescription }}
            </ElText>
          </div>
        </template>
        <template #labelingStatusName="{ row }">
          <ElTag
            v-if="row.labelingStatus"
            :type="row.labelingStatus === 'success' ? 'success' : row.labelingStatus === 'fail' ? 'danger' : 'primary'"
          >
            {{ row.labelingStatusName }}
          </ElTag>
          <span v-else>-</span>
        </template>
        <template #sampleCount="{ row }">
          <ElButton v-if="row.sampleCount" type="text" @click="simpleCheck(row)">
            {{ row.sampleCount }}
          </ElButton>
        </template>
        <template #operation="{ row }: {row:LabelManagePageList.Row}">
          <ElSwitch v-hasPermi="['productLabelManage:operation']" :model-value="row.status === LabelStatusEnum.START" @change="changeStatus(row)" />
        </template>
      </VxeGrid>
      <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
    </div>

    <LabelManageSimpleDialog :id="currentId" v-model:visible="simpleVisible" @refresh="handleReset" />

    <LabelManageEditDialog v-model:visible="editVisible" :rows="checkedData" @refresh="handleReset" />

    <MarkLabelDialog
      v-model:visible="markLabelVisible"
      :ids="ids"
      :no-cancel-refresh="true"
      text="根据勾选的标签信息，基于样本数据打标，需耐心等待，打标完成后，系统会进行通知"
      @refresh="handleReset"
    />
  </ContentWrap>
</template>

<style scoped lang="scss">
.operation{
  margin-bottom:10px;
}

.customer-combo {
  width: 100%;
  display: flex;
  align-items: center;
  box-shadow: 0 0 0 1px #dcdfe6;
  overflow-x: hidden;
  border-radius: 4px;
  ::v-deep .el-input__wrapper, ::v-deep .el-select__wrapper {
    box-shadow: none;
    border-radius: 0;
  }
}

.customer-form {
  ::v-deep .is-error .customer-combo {
    box-shadow: 0 0 0 1px #F56C6C;
  }
}

.custom-combo-select {
  border-right: 1px solid #dcdfe6;
}
</style>
