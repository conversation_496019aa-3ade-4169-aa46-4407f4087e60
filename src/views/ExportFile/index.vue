<script lang="ts" setup>
import type { ExportTaskDTO, } from "@/apis/export"
import { exportPageApi, } from "@/apis/export"
import { ContentWrap, } from "@/components/ContentWrap"
import { LayoutForm, } from "@/components/LayoutForm"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { ElButton, } from "element-plus"

const tableColumn = [
  {
    type: "seq",
    label: "序号",
    width: 50,
  },
  {
    field: "moduleName",
    title: "导出任务名称",
    minWidth: 120,
  },
  {
    field: "createTime",
    title: "创建时间",
    minWidth: 120,
    search: {
      show: true,
      index: 2,
      title: "创建日期",
      component: "DatePicker",
      componentProps: {
        type: "daterange",
        valueFormat: "YYYY-MM-DD",
        format: "YYYY-MM-DD",
      },
    },
  },
  {
    field: "stateI18",
    title: "状态",
    minWidth: 80,
  },
  {
    field: "modifyTime",
    title: "导出完成时间",
    minWidth: 120,
    formatter: ({ row, },) => {
      return row.state === 1 ? row.modifyTime : " "
    },
  },
  {
    field: "errorMsg",
    title: "异常信息",
    minWidth: 100,
  },
  {
    field: "needFeishuNotice",
    title: "是否飞书通知",
    minWidth: 100,
    formatter: ({ row, },) => {
      return row.needFeishuNotice === 0 ? "否" : "是"
    },
  },
  {
    field: "operate",
    title: "操作",
    slots: {
      default: "operate",
    },
    minWidth: 100,
  },
]
const formData = reactive({
  timeArray: [],
  createTimeStart: "",
  createTimeEnd: "",
},)
const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleSearch,
  handleReset,
  handlePagerChange,
} = useReportQuery({
  api: exportPageApi,
  formData,
  formatParams: (formData,) => {
    return {
      createTimeStart: formData?.timeArray?.[0],
      createTimeEnd: formData?.timeArray?.[1],
    }
  },
  autoRequest: true,
},)
const tableOptions = computed(() => ({
  columns: tableColumn,
  loading: loading.value,
  maxHeight: maxHeight.value - 40,
  data: tableData.value as ExportTaskDTO[],
}),)
function handleDownload(url,) {
  url && window.open(url, "_blank",)
}
</script>

<template>
  <ContentWrap>
    <div class="search">
      <LayoutForm
        ref="formRef"
        :loading="loading"
        :model="formData"
        :span="8"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem label="创建时间">
          <ElDatePicker
            v-model="formData.timeArray"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始时间"
            style="width: 100%"
            type="datetimerange"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
      </LayoutForm>
    </div>
    <VxeGrid
      ref="tableRef"
      v-bind="tableOptions"
    >
      <template #operate="{ row }">
        <ElButton
          v-if="row.state === 1"
          type="primary"
          link
          @click="handleDownload(row.ossUrl)"
        >
          <Icon class="mr-1" icon="ep:bottom" />下载
        </ElButton>
      </template>
    </VxeGrid>
    <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
  </ContentWrap>
</template>

<style lang="less" scoped></style>
