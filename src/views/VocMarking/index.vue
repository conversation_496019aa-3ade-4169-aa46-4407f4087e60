<script lang="ts" setup>
import {ContentWrap,} from "@/components/ContentWrap"
import {computed, reactive, ref,} from "vue"

defineOptions({
  name: "VocMarking",
},)
// 响应式数据
const styleInput = ref("",)
const loading = ref(false,)
const showResults = ref(false,)
const showReviewTable = ref(false,)
const aiMarkingLoading = ref(false,)
const markingStatus = ref("",) // "success", "error", ""
const markingErrorMessage = ref("",)
const markingResults = ref(null,)

// 模拟数据
const productInfo = reactive({
  style: "CHUNK",
  productName: "CHUNK 女士高跟鞋",
  image: "https://via.placeholder.com/120x120",
  amazonUrl: "https://amazon.com/product/chunk",
  reviewCount: 3578,
  price: "$29.99",
},)

// 模拟打标结果数据
const markingResultData = ref([
  { category: "一级标签", subcategory: "二级标签", thirdLevel: "三级标签", description: "优点", count: "", percentage: "百分比" },
  { category: "产品舒适性", subcategory: "产品舒适性", thirdLevel: "产品舒适性", description: "产品舒适", count: 53, percentage: "35.33%" },
  { category: "产品设计", subcategory: "只是美观性", thirdLevel: "只是美观性", description: "只是美观性设计", count: 33, percentage: "22.0%" },
  { category: "", subcategory: "", thirdLevel: "外形好看", description: "", count: 26, percentage: "17.33%" },
  { category: "", subcategory: "", thirdLevel: "外观可爱", description: "", count: 19, percentage: "12.67%" },
  { category: "", subcategory: "", thirdLevel: "鞋底很舒适", description: "", count: 17, percentage: "11.33%" }
])

// 模拟Review数据
const reviewData = ref([
  {
    id: 1,
    reviewText: "Love! So comfortable",
    advantages: ["产品舒服",],
    disadvantages: ["商品适合",],
    rating: 5,
    date: "2024-01-15",
  },
  {
    id: 2,
    reviewText: "Me gusta mucho",
    advantages: ["商品适合", "质量准确",],
    disadvantages: ["送货准确",],
    rating: 4,
    date: "2024-01-14",
  },
  {
    id: 3,
    reviewText: "Just like the photo",
    advantages: ["产品舒服", "鞋子很轻",],
    disadvantages: ["外观预期",],
    rating: 5,
    date: "2024-01-13",
  },
  {
    id: 4,
    reviewText: "Lovely comfortable fit. Nice weight",
    advantages: ["产品舒服", "鞋子很轻",],
    disadvantages: [],
    rating: 5,
    date: "2024-01-12",
  },
],)

// 优缺点统计数据
const analysisData = reactive({
  advantages: [
    {label: "产品舒服", count: 53, percentage: 25.23,},
    {label: "产品设计", count: 33, percentage: 22.0,},
    {label: "只是美观", count: 26, percentage: 17.33,},
    {label: "外观可爱", count: 19, percentage: 12.67,},
    {label: "鞋子很轻", count: 17, percentage: 11.33,},
  ],
  disadvantages: [
    {label: "外观预期", count: 26, percentage: 17.33,},
    {label: "商品适合", count: 19, percentage: 12.67,},
    {label: "质量准确", count: 17, percentage: 11.33,},
  ],
},)

// 处理查看评论按钮
function handleViewReviews() {
  showReviewTable.value = true
}

// 处理输入框变化
function handleInputChange() {
  if (styleInput.value.trim()) {
    productInfo.style = styleInput.value.trim()
    showResults.value = true
  } else {
    showResults.value = false
    showReviewTable.value = false
  }
}

// 处理查看评论和达标结果
function handleViewReviewResults() {
  showReviewTable.value = true
}

// 处理开始AI打标
async function handleStartAIMarking() {
  if (aiMarkingLoading.value) {
    return
  }

  aiMarkingLoading.value = true
  markingStatus.value = ""
  markingErrorMessage.value = ""

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000,),)

    // 模拟随机成功/失败
    const isSuccess = Math.random() > 0.3 // 70% 成功率

    if (isSuccess) {
      markingStatus.value = "success"
      markingResults.value = {
        totalReviews: productInfo.reviewCount,
        markedReviews: Math.floor(productInfo.reviewCount * 0.85,),
        successRate: "85.2%",
        processingTime: "2.8秒",
        feishuDocUrl: "https://feishu.cn/docs/doccnXXXXXXXXXXXXXXXX",
      }
      ElMessage.success("AI打标完成！已生成飞书文档",)
      // 显示打标结果数据
      showReviewTable.value = true
    } else {
      markingStatus.value = "error"
      markingErrorMessage.value = "网络连接超时，请检查网络后重试"
      ElMessage.error("AI打标失败，请重试",)
    }
  } catch (error) {
    markingStatus.value = "error"
    markingErrorMessage.value = error.message || "未知错误"
    ElMessage.error("AI打标失败",)
  } finally {
    aiMarkingLoading.value = false
  }
}

// 处理生成飞书文档
function handleGenerateFeishuDoc() {
  ElMessage.success("生成飞书文档功能开发中...",)
}

// 计算样本数量提示
const sampleCountText = computed(() => {
  return `有不重复${productInfo.style}数据 ${productInfo.reviewCount}条数据`
},)
</script>

<template>
  <ContentWrap>
    <div class="voc-marking-container">
      <div class="voc-inner-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            产品Review VOC分析报告
          </h1>
        </div>

        <!-- 输入区域 -->
        <div class="input-section">
          <div class="input-label">
            输入需要打标的style:
          </div>
          <div class="input-container">
            <ElInput
                v-model="styleInput"
                class="style-input"
                placeholder="请输入Style编号"
                size="large"
                @input="handleInputChange"
            />
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <ElButton
              :disabled="!styleInput.trim()"
              size="large"
              type="primary"
              @click="handleViewReviews"
          >
            查看评论条数
          </ElButton>
          <ElButton
              :disabled="!styleInput.trim() || aiMarkingLoading"
              :loading="aiMarkingLoading"
              size="large"
              type="success"
              @click="handleStartAIMarking"
          >
            <template v-if="aiMarkingLoading">
              AI算法引擎精准打标进行中请稍等...
            </template>
            <template v-else>
              开始AI打标 & 生成飞书文档
            </template>
          </ElButton>
        </div>

        <!-- 数据统计提示 -->
        <div v-if="showResults" class="data-info">
          <ElAlert
              :closable="false"
              :title="sampleCountText"
              type="info"
              show-icon
          />
        </div>

        <div v-loading="aiMarkingLoading" element-loading-text="AI算法引擎精准打标进行中请稍等" style="height:50px;"/>
        <!-- AI打标状态提示 -->
        <div v-if="markingStatus === 'success'" class="marking-success">
          <ElAlert
              :closable="false"
              title="🎉 AI打标成功！"
              type="success"
              show-icon
          >
            <template #default>
              <div class="success-content">
                <p>✅ 总评论数：{{ markingResults?.totalReviews }}</p>
                <p>✅ 成功打标：{{ markingResults?.markedReviews }}</p>
                <p>✅ 成功率：{{ markingResults?.successRate }}</p>
                <p>✅ 处理时间：{{ markingResults?.processingTime }}</p>
                <div class="feishu-link">
                  <ElButton type="primary" link @click="window.open(markingResults?.feishuDocUrl)">
                    📄 查看飞书文档
                  </ElButton>
                </div>
              </div>
            </template>
          </ElAlert>
        </div>

        <div v-if="markingStatus === 'error'" class="marking-error">
          <ElAlert
              :closable="false"
              title="❌ AI打标失败"
              type="error"
              show-icon
          >
            <template #default>
              <div class="error-content">
                <p>失败原因：{{ markingErrorMessage }}</p>
                <ElButton type="danger" @click="handleStartAIMarking">
                  🔄 重新打标
                </ElButton>
              </div>
            </template>
          </ElAlert>
        </div>

        <!-- 查看Review按钮 -->
        <div v-if="showResults" class="review-button-container">
          <ElButton
              size="large"
              type="primary"
              @click="handleViewReviewResults"
          >
            查看Review以及达标结果
          </ElButton>
        </div>

        <!-- Review表格显示区域 -->
        <div v-if="showReviewTable" class="review-results-section">
          <!-- 产品信息卡片 -->
          <div class="product-card">
            <div class="product-header">
              <div class="product-info">
                <div class="product-image">
                  <ElImage
                      :preview-src-list="[productInfo.image]"
                      :src="productInfo.image"
                      fit="cover"
                  />
                </div>
                <div class="product-details">
                  <h2 class="product-title">
                    {{ productInfo.style }}
                  </h2>
                  <div class="product-meta">
                    <div class="meta-item">
                      <span class="meta-label">Style：</span>
                      <span class="meta-value">{{ productInfo.style }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">样本数：</span>
                      <span class="meta-value">{{ productInfo.reviewCount }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">价格：</span>
                      <span class="meta-value">{{ productInfo.price }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="product-actions">
                <ElButton type="primary" link>
                  <Icon icon="ep:view"/>
                  展示产品主图
                </ElButton>
                <ElButton type="success" link>
                  支持打标评论数据下载
                </ElButton>
                <ElButton type="warning" link>
                  支持打标统计结果下载
                </ElButton>
              </div>
            </div>
          </div>

          <!-- 用户评论优缺点分析 -->
          <div class="analysis-section">
            <h3 class="section-title">
              用户对产品的哪些优点很满意
            </h3>
            <div class="analysis-grid">
              <ElRow :gutter="24">
                <ElCol :span="12">
                  <ElCard class="analysis-card advantages-card">
                    <template #header>
                      <div class="card-header">
                        <Icon class="card-icon success-icon" icon="ep:thumb"/>
                        <span class="card-title">优点</span>
                      </div>
                    </template>
                    <div class="analysis-list">
                      <div
                          v-for="item in analysisData.advantages"
                          :key="item.label"
                          class="analysis-item"
                      >
                        <div class="item-header">
                          <span class="item-label">{{ item.label }}</span>
                          <span class="item-percentage">{{ item.percentage }}%</span>
                        </div>
                        <ElProgress
                            :percentage="item.percentage"
                            :show-text="false"
                            :stroke-width="8"
                            status="success"
                        />
                        <div class="item-count">
                          {{ item.count }} 条评论
                        </div>
                      </div>
                    </div>
                  </ElCard>
                </ElCol>
                <ElCol :span="12">
                  <ElCard class="analysis-card disadvantages-card">
                    <template #header>
                      <div class="card-header">
                        <Icon class="card-icon danger-icon" icon="ep:thumb"/>
                        <span class="card-title">缺点</span>
                      </div>
                    </template>
                    <div class="analysis-list">
                      <div
                          v-for="item in analysisData.disadvantages"
                          :key="item.label"
                          class="analysis-item"
                      >
                        <div class="item-header">
                          <span class="item-label">{{ item.label }}</span>
                          <span class="item-percentage">{{ item.percentage }}%</span>
                        </div>
                        <ElProgress
                            :percentage="item.percentage"
                            :show-text="false"
                            :stroke-width="8"
                            status="exception"
                        />
                        <div class="item-count">
                          {{ item.count }} 条评论
                        </div>
                      </div>
                    </div>
                  </ElCard>
                </ElCol>
              </ElRow>
            </div>
          </div>

          <!-- 打标结果数据表格 -->
          <div class="review-table-section">
            <h3 class="section-title">
              用户对产品的哪些优点很满意
            </h3>
            <div class="download-buttons">
              <ElButton type="primary" link>
                支持打标评论数据下载
              </ElButton>
              <ElButton type="success" link>
                支持打标统计结果下载
              </ElButton>
            </div>
            <ElCard class="table-card">
              <VxeTable
                  :column-config="{ resizable: true }"
                  :data="markingResultData"
                  :row-config="{ height: 50 }"
                  stripe border
              >
                <VxeColumn field="category" min-width="120" title="一级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.category }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="subcategory" min-width="120" title="二级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.subcategory }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="thirdLevel" min-width="120" title="三级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.thirdLevel }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="description" min-width="120" title="优点">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.description }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="count" width="100" title="数量">
                  <template #default="{ row }">
                    <div class="table-cell count-cell">
                      {{ row.count }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="percentage" width="120" title="百分比">
                  <template #default="{ row }">
                    <div class="table-cell percentage-cell">
                      {{ row.percentage }}
                    </div>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElCard>
          </div>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.voc-marking-container {
  padding: 32px;
  width: calc(100% + 20px);
  margin: 0 auto;
  margin-top: -20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: calc(100vh - 20px);
  border-radius: 16px;

  .voc-inner-container {
    max-width: 1400px;
    margin: 0 auto;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 32px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
  }

  .page-title {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.5px;

    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #fbbf24, #f59e0b);
      border-radius: 2px;
      box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
    }
  }
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(180deg);
  }
}

.input-section {
  margin-bottom: 40px;
  background: #ffffff;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 16px 16px 0 0;
  }

  .input-label {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '🎯';
      font-size: 20px;
    }
  }

  .input-container {
    max-width: 700px;
    position: relative;

    .style-input {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid transparent;
        height: 50px;
        background: linear-gradient(white, white) padding-box,
        linear-gradient(135deg, #667eea, #764ba2) border-box;

        &:hover {
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
          transform: translateY(-2px);
        }

        &.is-focus {
          box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
          transform: translateY(-3px);
        }

        .el-input__inner {
          font-size: 16px;
          font-weight: 500;
          color: #1f2937;

          &::placeholder {
            color: #9ca3af;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
  justify-content: center;
  flex-wrap: wrap;

  .el-button {
    border-radius: 12px;
    padding: 16px 32px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    height: 50px;
    min-width: 200px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-1px);
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
      }
    }

    &.el-button--success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
      }
    }

    &:disabled {
      background: #e5e7eb;
      color: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// AI打标状态样式
.marking-success, .marking-error {
  margin: 32px 0;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .el-alert__icon {
      font-size: 24px;
    }

    .el-alert__title {
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 16px;
    }
  }
}

.marking-success {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 6px solid #10b981;

    .el-alert__icon {
      color: #10b981;
    }

    .el-alert__title {
      color: #065f46;
    }
  }

  .success-content {
    p {
      margin: 8px 0;
      font-size: 15px;
      font-weight: 500;
      color: #047857;
    }

    .feishu-link {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid rgba(16, 185, 129, 0.2);

      .el-button {
        font-size: 16px;
        font-weight: 600;
        padding: 8px 16px;
        border-radius: 8px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }
}

.marking-error {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 6px solid #ef4444;

    .el-alert__icon {
      color: #ef4444;
    }

    .el-alert__title {
      color: #991b1b;
    }
  }

  .error-content {
    p {
      margin: 8px 0 16px 0;
      font-size: 15px;
      font-weight: 500;
      color: #dc2626;
    }

    .el-button {
      font-size: 14px;
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 8px;
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        background: linear-gradient(135deg, #dc2626, #b91c1c);
      }
    }
  }
}

.data-info {
  margin-bottom: 32px;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 50%, #fce7f3 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
    padding: 20px 24px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    }

    .el-alert__icon {
      font-size: 20px;
      color: #3b82f6;
    }

    .el-alert__title {
      font-weight: 600;
      font-size: 16px;
      color: #1e40af;
    }
  }
}

.review-button-container {
  text-align: center;
  margin: 40px 0;
  padding: 32px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  .el-button {
    border-radius: 16px;
    padding: 20px 48px;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 280px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s;
    }

    &:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 32px rgba(240, 147, 251, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-2px) scale(1.01);
    }
  }
}

// Review结果展示区域样式
.review-results-section {
  margin-top: 48px;
  padding: 40px;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    border-radius: 20px 20px 0 0;
  }
}

// 产品信息卡片样式
.product-card {
  margin-bottom: 40px;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      padding: 2px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 16px;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .product-image {
    width: 100px;
    height: 100px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
      z-index: 1;
    }

    :deep(.el-image) {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .product-details {
    flex: 1;

    .product-title {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 16px 0;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .product-meta {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        border-left: 3px solid #667eea;

        .meta-label {
          font-weight: 600;
          color: #4b5563;
          min-width: 70px;
        }

        .meta-value {
          color: #1f2937;
          font-weight: 600;
        }
      }
    }
  }

  .product-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .el-button {
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateX(6px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        &::before {
          left: 100%;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
      }

      &.el-button--success {
        background: linear-gradient(135deg, #10b981, #059669);
        border: none;
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border: none;
      }
    }
  }
}

// 分析区域样式
.analysis-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #667eea;
    position: relative;

    &::before {
      content: '📊';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #667eea, transparent);
    }
  }

  .analysis-grid {
    .analysis-card {
      height: 100%;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
      }

      :deep(.el-card__header) {
        padding: 20px 24px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-bottom: 2px solid #f1f5f9;
        position: relative;
      }

      :deep(.el-card__body) {
        padding: 24px;
        background: #ffffff;
      }
    }

    .advantages-card {
      border-top: 4px solid #10b981;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #34d399);
      }
    }

    .disadvantages-card {
      border-top: 4px solid #ef4444;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #ef4444, #f87171);
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .card-icon {
        font-size: 22px;
        padding: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.success-icon {
          color: #10b981;
          background: linear-gradient(135deg, #d1fae5, #a7f3d0);
        }

        &.danger-icon {
          color: #ef4444;
          transform: rotate(180deg);
          background: linear-gradient(135deg, #fee2e2, #fecaca);
        }
      }

      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .analysis-list {
      .analysis-item {
        margin-bottom: 24px;
        padding: 16px;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateX(4px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
          border-color: #667eea;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .item-label {
            font-weight: 600;
            color: #1f2937;
            font-size: 15px;
          }

          .item-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 16px;
            padding: 4px 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
          }
        }

        :deep(.el-progress) {
          .el-progress-bar__outer {
            border-radius: 6px;
            background: #f1f5f9;
          }

          .el-progress-bar__inner {
            border-radius: 6px;
            transition: all 0.3s ease;
          }
        }

        .item-count {
          font-size: 13px;
          color: #6b7280;
          margin-top: 8px;
          text-align: right;
          font-weight: 500;
          padding: 2px 8px;
          background: rgba(107, 114, 128, 0.1);
          border-radius: 4px;
          display: inline-block;
        }
      }
    }
  }
}

// Review表格区域样式
.review-table-section {
  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #f093fb;
    position: relative;

    &::before {
      content: '📝';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #f093fb, transparent);
    }
  }

  .download-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    justify-content: flex-end;

    .el-button {
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.el-button--primary {
        color: #3b82f6;

        &:hover {
          color: #1d4ed8;
        }
      }

      &.el-button--success {
        color: #10b981;

        &:hover {
          color: #059669;
        }
      }
    }
  }

  .table-card {
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      border-radius: 16px;
      overflow: hidden;
      border: none;

      .vxe-header--column {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-weight: 700;
        color: #ffffff;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);

        &:hover {
          background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
      }

      .vxe-body--row {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f5f9;

        &:hover {
          background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
          transform: scale(1.01);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        &:nth-child(even) {
          background: rgba(248, 250, 252, 0.5);
        }
      }

      .vxe-body--column {
        padding: 12px 16px;
      }
    }

    .table-cell {
      padding: 8px 0;
      line-height: 1.5;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 500;

      &.count-cell {
        text-align: center;
        font-weight: 600;
        color: #1f2937;
      }

      &.percentage-cell {
        text-align: center;
        font-weight: 600;
        color: #667eea;
      }
    }

    .tags-container {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding: 8px 0;

      .tag-item {
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &.el-tag--success {
          background: linear-gradient(135deg, #10b981, #34d399);
          border: none;
        }

        &.el-tag--danger {
          background: linear-gradient(135deg, #ef4444, #f87171);
          border: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .voc-marking-container {
    padding: 24px;
  }

  .product-header {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .product-details .product-meta {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .voc-marking-container {
    padding: 16px;
    background: #f8fafc;
  }

  .page-header {
    margin-bottom: 32px;
    padding: 24px 16px;

    .page-title {
      font-size: 24px;
    }
  }

  .input-section {
    padding: 24px 16px;

    .input-label {
      font-size: 16px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      min-width: auto;
    }
  }

  .review-button-container {
    padding: 24px 16px;

    .el-button {
      min-width: auto;
      width: 100%;
    }
  }

  .analysis-grid {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .review-table-section {
    :deep(.vxe-table) {
      font-size: 12px;

      .vxe-body--column {
        padding: 8px 12px;
      }
    }

    .review-text {
      font-size: 13px;
      padding: 8px;
    }

    .tags-container .tag-item {
      font-size: 11px;
      padding: 2px 6px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.input-section {
  animation: fadeInUp 0.6s ease-out;
}

.action-buttons {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.data-info {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.marking-success, .marking-error {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-button-container {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-results-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
}

.product-card {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.5s;
  animation-fill-mode: both;
}

.analysis-section {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

.analysis-card {
  animation: slideInRight 0.6s ease-out;
  animation-delay: 0.7s;
  animation-fill-mode: both;

  &:nth-child(2) {
    animation-delay: 0.8s;
  }
}

.review-table-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.9s;
  animation-fill-mode: both;
}

// 滚动条美化
:deep(::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 4px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;

  &:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
  }
}
</style>
