<script lang="ts" setup>
import { ContentWrap, } from "@/components/ContentWrap"
import { computed, reactive, ref, } from "vue"

defineOptions({
  name: "VocMarking",
},)
// 响应式数据
const styleInput = ref("",)
const loading = ref(false,)
const showResults = ref(false,)
const showReviewTable = ref(false,)
const showDetailReviewTable = ref(false,)
const aiMarkingLoading = ref(false,)
const markingStatus = ref("",) // "success", "error", ""
const markingErrorMessage = ref("",)
const markingResults = ref(null,)

// 模拟数据
const productInfo = reactive({
  style: "CHUNK",
  productName: "CHUNK 女士高跟鞋",
  image: "https://via.placeholder.com/120x120",
  amazonUrl: "https://amazon.com/product/chunk",
  reviewCount: 3578,
  price: "$29.99",
},)

// 最新打标结果记录数据
const latestMarkingRecords = ref([
  {
    id: 1,
    style: "CHUNK",
    submitTime: "2025-7-3 12:15:33",
    completeTime: "2025-7-3 12:18:33",
    reviewDataFile: "CHUNK.xlsx",
    description: "支持用户点击Style，查看打标的结果数据。",
  },
  {
    id: 2,
    style: "支持用户点击Style，查看打标的结果数据。",
    submitTime: "",
    completeTime: "",
    reviewDataFile: "",
    description: "支持用户点击Style，查看打标的结果数据。",
  },
  { id: 3, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 4, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 5, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 6, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 7, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 8, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 9, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
  { id: 10, style: "", submitTime: "", completeTime: "", reviewDataFile: "", description: "", },
],)

// 模拟打标结果数据
const markingResultData = ref([
  {
    category: "一级标签",
    subcategory: "二级标签",
    thirdLevel: "三级标签",
    description: "优点",
    count: "",
    percentage: "百分比",
  },
  {
    category: "产品舒适性",
    subcategory: "产品舒适性",
    thirdLevel: "产品舒适性",
    description: "产品舒适",
    count: 53,
    percentage: "35.33%",
  },
  {
    category: "产品设计",
    subcategory: "只是美观性",
    thirdLevel: "只是美观性",
    description: "只是美观性设计",
    count: 33,
    percentage: "22.0%",
  },
  { category: "", subcategory: "", thirdLevel: "外形好看", description: "", count: 26, percentage: "17.33%", },
  { category: "", subcategory: "", thirdLevel: "外观可爱", description: "", count: 19, percentage: "12.67%", },
  { category: "", subcategory: "", thirdLevel: "鞋底很舒适", description: "", count: 17, percentage: "11.33%", },
],)

// 模拟Review数据
const reviewData = ref([
  {
    id: 1,
    reviewText: "Love! So comfortable and stylish. I've been wearing these shoes for weeks now and they still feel amazing. The cushioning is perfect for long walks and the design goes with everything in my wardrobe. Highly recommend!",
    advantages: ["产品舒服", "外观设计", "质量优秀",],
    disadvantages: ["价格偏高",],
    rating: 5,
    date: "2024-01-15",
  },
  {
    id: 2,
    reviewText: "Me gusta mucho este producto. La calidad es excelente y el diseño es muy bonito. Lo recomiendo totalmente para uso diario.",
    advantages: ["商品适合", "质量准确", "外观可爱",],
    disadvantages: ["送货时间长",],
    rating: 4,
    date: "2024-01-14",
  },
  {
    id: 3,
    reviewText: "Just like the photo! The color is exactly what I expected and the material feels premium. Very satisfied with this purchase. Will definitely buy again.",
    advantages: ["产品舒服", "鞋子很轻", "颜色准确",],
    disadvantages: ["外观预期", "尺码偏小",],
    rating: 5,
    date: "2024-01-13",
  },
  {
    id: 4,
    reviewText: "Lovely comfortable fit. Nice weight - not too heavy, not too light. Perfect for everyday wear. The arch support is excellent and my feet don't hurt even after wearing them all day.",
    advantages: ["产品舒服", "鞋子很轻", "支撑性好", "适合日常",],
    disadvantages: [],
    rating: 5,
    date: "2024-01-12",
  },
  {
    id: 5,
    reviewText: "Good quality shoes but took a while to break in. Once they're comfortable, they're really nice. The style is trendy and I get compliments on them.",
    advantages: ["质量优秀", "外观时尚", "款式新颖",],
    disadvantages: ["需要磨合期", "初期不舒适",],
    rating: 4,
    date: "2024-01-11",
  },
  {
    id: 6,
    reviewText: "These shoes are okay but not great. The design is nice but the comfort could be better. They're a bit stiff and the sizing runs small.",
    advantages: ["外观设计",],
    disadvantages: ["舒适度一般", "尺码偏小", "材质偏硬",],
    rating: 3,
    date: "2024-01-10",
  },
],)

// 优缺点统计数据
const analysisData = reactive({
  advantages: [
    { label: "产品舒服", count: 53, percentage: 25.23, },
    { label: "产品设计", count: 33, percentage: 22.0, },
    { label: "只是美观", count: 26, percentage: 17.33, },
    { label: "外观可爱", count: 19, percentage: 12.67, },
    { label: "鞋子很轻", count: 17, percentage: 11.33, },
  ],
  disadvantages: [
    { label: "外观预期", count: 26, percentage: 17.33, },
    { label: "商品适合", count: 19, percentage: 12.67, },
    { label: "质量准确", count: 17, percentage: 11.33, },
  ],
},)

// 处理查看评论按钮
function handleViewReviews() {
  showReviewTable.value = true
}

// 处理输入框变化
function handleInputChange() {
  if (styleInput.value.trim()) {
    productInfo.style = styleInput.value.trim()
    showResults.value = true
  } else {
    showResults.value = false
    showReviewTable.value = false
  }
}

// 处理查看评论和达标结果
function handleViewReviewResults() {
  showReviewTable.value = true
}

// 处理开始AI打标
async function handleStartAIMarking() {
  if (aiMarkingLoading.value) {
    return
  }

  aiMarkingLoading.value = true
  markingStatus.value = ""
  markingErrorMessage.value = ""

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000,),)

    // 模拟随机成功/失败
    const isSuccess = Math.random() > 0.3 // 70% 成功率

    if (isSuccess) {
      markingStatus.value = "success"
      markingResults.value = {
        totalReviews: productInfo.reviewCount,
        markedReviews: Math.floor(productInfo.reviewCount * 0.85,),
        successRate: "85.2%",
        processingTime: "2.8秒",
        feishuDocUrl: "https://feishu.cn/docs/doccnXXXXXXXXXXXXXXXX",
      }
      ElMessage.success("AI打标完成！已生成飞书文档",)
    } else {
      markingStatus.value = "error"
      markingErrorMessage.value = "网络连接超时，请检查网络后重试"
      ElMessage.error("AI打标失败，请重试",)
    }
  } catch (error) {
    markingStatus.value = "error"
    markingErrorMessage.value = error.message || "未知错误"
    ElMessage.error("AI打标失败",)
  } finally {
    aiMarkingLoading.value = false
  }
}

// 处理生成飞书文档
function handleGenerateFeishuDoc() {
  ElMessage.success("生成飞书文档功能开发中...",)
}

// 处理查看Style结果
function handleViewStyleResults(record,) {
  // 设置当前查看的Style
  styleInput.value = record.style
  // 显示Review表格
  showReviewTable.value = true
  ElMessage.success(`正在查看 ${record.style} 的打标结果`,)
}

// 处理下载文件
function handleDownloadFile(filename,) {
  ElMessage.success(`正在下载文件：${filename}`,)
}

// 计算样本数量提示
const sampleCountText = computed(() => {
  return `有不重复${productInfo.style}数据 ${productInfo.reviewCount}条数据`
},)
</script>

<template>
  <ContentWrap>
    <div class="voc-marking-container">
      <div class="voc-inner-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">
            产品Review VOC分析报告
          </h1>
        </div>
        <!-- 输入区域 -->
        <div class="input-section">
          <div class="input-label">
            输入需要打标的style:
          </div>
          <div class="input-container">
            <ElInput
              v-model="styleInput"
              class="style-input"
              placeholder="请输入Style编号"
              size="large"
              @input="handleInputChange"
            />
          </div>
        </div>
        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <ElButton
            :disabled="!styleInput.trim() || aiMarkingLoading"
            :loading="aiMarkingLoading"
            size="large"
            type="success"
            @click="handleStartAIMarking"
          >
            <template v-if="aiMarkingLoading">
              AI算法引擎精准打标进行中请稍等...
            </template>
            <template v-else>
              开始AI打标 & 生成飞书文档
            </template>
          </ElButton>
        </div>
        <!-- 数据统计提示 -->
        <div v-if="showResults" class="data-info">
          <ElAlert
            :closable="false"
            :title="sampleCountText"
            type="info"
            show-icon
          />
        </div>

        <div v-loading="aiMarkingLoading" element-loading-text="AI算法引擎精准打标进行中请稍等" style="height:50px;" />
        <!-- AI打标成功结果显示 -->
        <div v-if="markingStatus === 'success'" class="marking-success-result">
          <!-- 产品信息卡片 -->
          <div class="product-result-card">
            <div class="product-header">
              <div class="product-info">
                <div class="product-image">
                  <ElImage
                    :preview-src-list="[productInfo.image]"
                    :src="productInfo.image"
                    fit="cover"
                  />
                </div>
                <div class="product-details">
                  <h2 class="product-title">
                    {{ productInfo.style }}
                  </h2>
                  <div class="product-meta">
                    <div class="meta-item">
                      <span class="meta-label">Style:</span>
                      <span class="meta-value">{{ productInfo.style }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">目标数据:</span>
                      <span
                        class="meta-value link-text"
                      >https://jgqwdwgqm.feishu.cn/file/PnExbwEUNo4Q23WxSFZcdwlnae</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="product-actions">
                <ElButton type="primary" link>
                  展示产品主图
                </ElButton>
                <ElButton type="success" link>
                  支持打标评论数据下载
                </ElButton>
                <ElButton type="warning" link>
                  支持打标统计结果下载
                </ElButton>
              </div>
            </div>
          </div>

          <!-- 打标结果表格 -->
          <div class="marking-result-table">
            <h3 class="table-title">
              用户对产品的哪些优点很满意
            </h3>
            <ElCard class="result-table-card">
              <VxeTable
                :column-config="{ resizable: true }"
                :data="markingResultData"
                :row-config="{ height: 50 }"

                
 stripe border 
              >
                <VxeColumn field="category" min-width="120" title="一级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.category }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="subcategory" min-width="120" title="二级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.subcategory }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="thirdLevel" min-width="120" title="三级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.thirdLevel }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="description" min-width="120" title="优点">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.description }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="count"
                  title="数量"
                  width="100"
                >
                  <template #default="{ row }">
                    <div class="count-cell table-cell">
                      {{ row.count }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="percentage"
                  title="百分比"
                  width="120"
                >
                  <template #default="{ row }">
                    <div class="percentage-cell table-cell">
                      {{ row.percentage }}
                    </div>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElCard>
          </div>

          <!-- Review详细数据表格 -->
          <div class="review-detail-table">
            <div class="review-header">
              <ElButton
                class="view-review-btn"
                size="large"
                type="primary"
                @click="showDetailReviewTable = true"
              >
                查看Review以及打标结果
              </ElButton>
            </div>

            <div v-if="showDetailReviewTable" class="review-table-container">
              <h3 class="table-title">
                Review详细数据及打标结果
              </h3>
              <ElCard class="detail-table-card">
                <VxeTable
                  :column-config="{ resizable: true }"
                  :data="reviewData"
                  :row-config="{ height: 120 }"
                >
                  <VxeColumn field="reviewText" min-width="350" title="评论原文">
                    <template #default="{ row }">
                      <div class="review-text-content">
                        {{ row.reviewText }}
                      </div>
                    </template>
                  </VxeColumn>
                  <VxeColumn field="advantages" min-width="200" title="优点标签">
                    <template #default="{ row }">
                      <div class="tags-container">
                        <ElTag
                          v-for="tag in row.advantages"
                          :key="tag"
                          class="tag-item advantage-tag"
                          effect="light"
                          size="default"
                          type="success"
                        >
                          {{ tag }}
                        </ElTag>
                      </div>
                    </template>
                  </VxeColumn>
                  <VxeColumn field="disadvantages" min-width="200" title="缺点标签">
                    <template #default="{ row }">
                      <div class="tags-container">
                        <ElTag
                          v-for="tag in row.disadvantages"
                          :key="tag"
                          class="tag-item disadvantage-tag"
                          effect="light"
                          size="default"
                          type="danger"
                        >
                          {{ tag }}
                        </ElTag>
                      </div>
                    </template>
                  </VxeColumn>
                  <VxeColumn
                    align="center"
                    field="rating"
                    title="评分"
                    width="120"
                  >
                    <template #default="{ row }">
                      <div class="rating-container">
                        <ElRate
                          :model-value="row.rating"
                          size="small"
                          disabled
                          show-score
                        />
                      </div>
                    </template>
                  </VxeColumn>
                  <VxeColumn
                    align="center"
                    field="date"
                    title="评论日期"
                    width="140"
                  >
                    <template #default="{ row }">
                      <div class="date-cell">
                        {{ row.date }}
                      </div>
                    </template>
                  </VxeColumn>
                </VxeTable>
              </ElCard>
            </div>
          </div>
        </div>

        <div v-if="markingStatus === 'error'" class="marking-error">
          <ElAlert
            :closable="false"
            title="❌ AI打标失败"
            type="error"
            show-icon
          >
            <template #default>
              <div class="error-content">
                <p>失败原因：{{ markingErrorMessage }}</p>
                <ElButton type="danger" @click="handleStartAIMarking">
                  🔄 重新打标
                </ElButton>
              </div>
            </template>
          </ElAlert>
        </div>
        <!-- 最新打标结果记录 -->
        <div class="latest-records-section">
          <h2 class="section-title">
            最新的打标结果记录
          </h2>
          <ElCard class="records-card">
            <VxeTable
              :column-config="{ resizable: true }"
              :data="latestMarkingRecords"
              :row-config="{ height: 60 }"
            >
              <VxeColumn
                align="center"
                field="id"
                title="序号"
                width="80"
              >
                <template #default="{ row }">
                  <div class="record-id">
                    {{ row.id }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="style" min-width="120" title="Style">
                <template #default="{ row }">
                  <div class="style-cell">
                    <ElButton
                      v-if="row.style && row.style !== '支持用户点击Style，查看打标的结果数据。'"
                      type="primary"
                      link
                      @click="handleViewStyleResults(row)"
                    >
                      {{ row.style }}
                    </ElButton>
                    <span v-else class="empty-style">{{ row.style }}</span>
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn
                align="center"
                field="submitTime"
                title="提交时间"
                width="160"
              >
                <template #default="{ row }">
                  <div class="time-cell">
                    {{ row.submitTime }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn
                align="center"
                field="completeTime"
                title="完成时间"
                width="160"
              >
                <template #default="{ row }">
                  <div class="time-cell">
                    {{ row.completeTime }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="reviewDataFile" title="评论清单数据" width="200">
                <template #default="{ row }">
                  <div class="file-cell">
                    <ElButton
                      v-if="row.reviewDataFile"
                      type="success"
                      link
                      @click="handleDownloadFile(row.reviewDataFile)"
                    >
                      📄 {{ row.reviewDataFile }}
                    </ElButton>
                    <span v-else class="empty-file">{{ row.description }}</span>
                  </div>
                </template>
              </VxeColumn>
            </VxeTable>
          </ElCard>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.voc-marking-container {
  padding: 40px;
  width: 100%;
  margin: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 25%, #06b6d4 50%, #10b981 75%, #f59e0b 100%);
    z-index: 1;
  }

  .voc-inner-container {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 48px 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 25%, #06b6d4 50%, #10b981 75%, #f59e0b 100%);
    border-radius: 20px 20px 0 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
  }

  .page-title {
    font-size: 32px;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 4px;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
      border-radius: 2px;
      box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(180deg);
  }
}

.input-section {
  margin-bottom: 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  backdrop-filter: blur(10px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
    border-radius: 16px 16px 0 0;
  }

  .input-label {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;

    &::before {
      content: '🎯';
      font-size: 24px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }
  }

  .input-container {
    max-width: 800px;
    margin: 0 auto;

    .style-input {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
        border: 2px solid transparent;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(white, white) padding-box,
                   linear-gradient(135deg, #6366f1, #8b5cf6) border-box;
        height: 56px;

        &:hover {
          box-shadow: 0 12px 40px rgba(99, 102, 241, 0.15);
          transform: translateY(-2px);
        }

        &.is-focus {
          box-shadow: 0 16px 48px rgba(99, 102, 241, 0.2);
          transform: translateY(-3px);
        }

        .el-input__inner {
          font-size: 16px;
          font-weight: 500;
          color: #1e293b;

          &::placeholder {
            color: #9ca3af;
            font-weight: 400;
          }
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 24px;
  margin-bottom: 48px;
  justify-content: center;

  .el-button {
    border-radius: 12px;
    padding: 16px 32px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 200px;
    height: 56px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

      &::before {
        left: 100%;
      }
    }

    &.el-button--success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border: none;
      color: #ffffff;
      box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);

      &:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
      }
    }

    &:disabled {
      background: #e5e7eb;
      color: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// 最新打标结果记录样式
.latest-records-section {
  margin-bottom: 48px;

  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    border-left: 6px solid #6366f1;
    position: relative;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    &::before {
      content: '📊';
      margin-right: 12px;
      font-size: 28px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 24px;
      right: 24px;
      height: 2px;
      background: linear-gradient(90deg, #6366f1, transparent);
      border-radius: 1px;
    }
  }

  .records-card {
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.8);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    backdrop-filter: blur(10px);

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      .vxe-header--column {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        font-weight: 600;
        color: #1e293b;
        font-size: 15px;
      }

      .vxe-body--row {
        &:hover {
          background: rgba(99, 102, 241, 0.05);
        }
      }
    }

    .record-id {
      font-weight: 600;
      color: #1e293b;
      font-size: 15px;
    }

    .style-cell {
      .el-button {
        font-weight: 600;
        font-size: 14px;
        color: #6366f1;
        transition: all 0.2s ease;

        &:hover {
          color: #4f46e5;
          transform: translateY(-1px);
        }
      }

      .empty-style {
        color: #9ca3af;
        font-style: italic;
        font-size: 14px;
      }
    }

    .time-cell {
      font-size: 13px;
      color: #64748b;
      font-weight: 500;
    }

    .file-cell {
      .el-button {
        font-size: 13px;
        font-weight: 600;
        color: #10b981;
        transition: all 0.2s ease;

        &:hover {
          color: #059669;
          transform: translateY(-1px);
        }
      }

      .empty-file {
        color: #9ca3af;
        font-size: 13px;
        font-style: italic;
      }
    }
  }
}

// AI打标成功结果样式
.marking-success-result {
  margin: 48px 0;

  .product-result-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 32px;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    position: relative;
    backdrop-filter: blur(10px);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #10b981 0%, #059669 100%);
      border-radius: 20px 20px 0 0;
    }

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 32px;

      .product-info {
        display: flex;
        gap: 24px;
        flex: 1;

        .product-image {
          width: 120px;
          height: 120px;
          border-radius: 16px;
          overflow: hidden;
          border: 2px solid #e2e8f0;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

          :deep(.el-image) {
            width: 100%;
            height: 100%;
          }
        }

        .product-details {
          flex: 1;

          .product-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .product-meta {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 12px;

              .meta-label {
                font-weight: 600;
                color: #64748b;
                min-width: 90px;
                font-size: 16px;
              }

              .meta-value {
                color: #1e293b;
                font-weight: 500;
                font-size: 16px;

                &.link-text {
                  color: #6366f1;
                  text-decoration: underline;
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &:hover {
                    color: #4f46e5;
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }
      }

      .product-actions {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .el-button {
          font-size: 15px;
          font-weight: 600;
          padding: 12px 20px;
          border-radius: 12px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border: 2px solid transparent;

          &.el-button--primary {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
            }
          }

          &.el-button--success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
            }
          }

          &.el-button--warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 24px rgba(245, 158, 11, 0.3);
            }
          }
        }
      }
    }
  }

  .marking-result-table {
    .table-title {
      font-size: 22px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 24px;
      padding: 20px 24px;
      background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      border-radius: 16px;
      border-left: 6px solid #10b981;
      position: relative;
      box-shadow: 0 4px 16px rgba(16, 185, 129, 0.1);

      &::before {
        content: '✨';
        margin-right: 12px;
        font-size: 24px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 24px;
        right: 24px;
        height: 2px;
        background: linear-gradient(90deg, #10b981, transparent);
        border-radius: 1px;
      }
    }

    .result-table-card {
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      overflow: hidden;

      :deep(.el-card__body) {
        padding: 0;
      }

      .table-cell {
        padding: 8px 0;
        line-height: 1.4;
        color: #374151;
        word-break: break-word;
        font-size: 13px;
        font-weight: 400;

        &.count-cell {
          text-align: center;
          font-weight: 500;
          color: #374151;
        }

        &.percentage-cell {
          text-align: center;
          font-weight: 500;
          color: #3b82f6;
        }
      }
    }
  }

  .review-detail-table {
    margin-top: 40px;

    .review-header {
      text-align: center;
      margin-bottom: 32px;
      padding: 24px;
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border-radius: 16px;
      border: 2px solid rgba(59, 130, 246, 0.1);
      box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);

      .view-review-btn {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        color: white;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);

        &:hover {
          background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
          transform: translateY(-2px);
          box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
        }
      }
    }

    .review-table-container {
      animation: fadeInUp 0.3s ease-out;

      .table-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 16px;
        padding: 12px 16px;
        background: #f9fafb;
        border-radius: 6px;
        border-left: 3px solid #10b981;
      }

      .detail-table-card {
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        overflow: hidden;

        :deep(.el-card__body) {
          padding: 0;
        }

        .review-text-content {
          padding: 12px;
          line-height: 1.5;
          color: #374151;
          word-break: break-word;
          font-size: 13px;
          font-weight: 400;
          white-space: pre-wrap;
          max-height: 100px;
          overflow-y: auto;
          background: #f9fafb;
          border-radius: 4px;
          border-left: 2px solid #3b82f6;
        }

        .tags-container {
          padding: 8px;
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          align-items: flex-start;

          .tag-item {
            margin: 1px;
            font-size: 12px;
            font-weight: 400;
            padding: 4px 8px;
            border-radius: 12px;
            transition: all 0.2s ease;

            &.advantage-tag {
              background: #d1fae5;
              color: #065f46;
              border: 1px solid #10b981;
            }

            &.disadvantage-tag {
              background: #fee2e2;
              color: #991b1b;
              border: 1px solid #ef4444;
            }
          }
        }

        .rating-container {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px;

          :deep(.el-rate) {
            .el-rate__text {
              font-size: 11px;
              color: #6b7280;
              margin-left: 6px;
            }
          }
        }

        .date-cell {
          font-size: 12px;
          color: #6b7280;
          font-weight: 400;
          text-align: center;
        }
      }
    }
  }
}

// AI打标状态样式
.marking-error {
  margin: 32px 0;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .el-alert__icon {
      font-size: 24px;
    }

    .el-alert__title {
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 16px;
    }
  }
}

.marking-success {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 6px solid #10b981;

    .el-alert__icon {
      color: #10b981;
    }

    .el-alert__title {
      color: #065f46;
    }
  }

  .success-content {
    p {
      margin: 8px 0;
      font-size: 15px;
      font-weight: 500;
      color: #047857;
    }

    .feishu-link {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid rgba(16, 185, 129, 0.2);

      .el-button {
        font-size: 16px;
        font-weight: 600;
        padding: 8px 16px;
        border-radius: 8px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }
}

.marking-error {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 6px solid #ef4444;

    .el-alert__icon {
      color: #ef4444;
    }

    .el-alert__title {
      color: #991b1b;
    }
  }

  .error-content {
    p {
      margin: 8px 0 16px 0;
      font-size: 15px;
      font-weight: 500;
      color: #dc2626;
    }

    .el-button {
      font-size: 14px;
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 8px;
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        background: linear-gradient(135deg, #dc2626, #b91c1c);
      }
    }
  }
}

.data-info {
  margin-bottom: 32px;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 50%, #fce7f3 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
    padding: 20px 24px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    }

    .el-alert__icon {
      font-size: 20px;
      color: #3b82f6;
    }

    .el-alert__title {
      font-weight: 600;
      font-size: 16px;
      color: #1e40af;
    }
  }
}

.review-button-container {
  text-align: center;
  margin: 40px 0;
  padding: 32px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  .el-button {
    border-radius: 16px;
    padding: 20px 48px;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 280px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s;
    }

    &:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 32px rgba(240, 147, 251, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-2px) scale(1.01);
    }
  }
}

// Review结果展示区域样式
.review-results-section {
  margin-top: 48px;
  padding: 40px;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    border-radius: 20px 20px 0 0;
  }
}

// 产品信息卡片样式
.product-card {
  margin-bottom: 40px;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      padding: 2px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 16px;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .product-image {
    width: 100px;
    height: 100px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
      z-index: 1;
    }

    :deep(.el-image) {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .product-details {
    flex: 1;

    .product-title {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 16px 0;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .product-meta {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        border-left: 3px solid #667eea;

        .meta-label {
          font-weight: 600;
          color: #4b5563;
          min-width: 70px;
        }

        .meta-value {
          color: #1f2937;
          font-weight: 600;
        }
      }
    }
  }

  .product-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .el-button {
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateX(6px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        &::before {
          left: 100%;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
      }

      &.el-button--success {
        background: linear-gradient(135deg, #10b981, #059669);
        border: none;
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border: none;
      }
    }
  }
}

// 分析区域样式
.analysis-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #667eea;
    position: relative;

    &::before {
      content: '📊';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #667eea, transparent);
    }
  }

  .analysis-grid {
    .analysis-card {
      height: 100%;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
      }

      :deep(.el-card__header) {
        padding: 20px 24px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-bottom: 2px solid #f1f5f9;
        position: relative;
      }

      :deep(.el-card__body) {
        padding: 24px;
        background: #ffffff;
      }
    }

    .advantages-card {
      border-top: 4px solid #10b981;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #34d399);
      }
    }

    .disadvantages-card {
      border-top: 4px solid #ef4444;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #ef4444, #f87171);
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .card-icon {
        font-size: 22px;
        padding: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.success-icon {
          color: #10b981;
          background: linear-gradient(135deg, #d1fae5, #a7f3d0);
        }

        &.danger-icon {
          color: #ef4444;
          transform: rotate(180deg);
          background: linear-gradient(135deg, #fee2e2, #fecaca);
        }
      }

      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .analysis-list {
      .analysis-item {
        margin-bottom: 24px;
        padding: 16px;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateX(4px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
          border-color: #667eea;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .item-label {
            font-weight: 600;
            color: #1f2937;
            font-size: 15px;
          }

          .item-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 16px;
            padding: 4px 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
          }
        }

        :deep(.el-progress) {
          .el-progress-bar__outer {
            border-radius: 6px;
            background: #f1f5f9;
          }

          .el-progress-bar__inner {
            border-radius: 6px;
            transition: all 0.3s ease;
          }
        }

        .item-count {
          font-size: 13px;
          color: #6b7280;
          margin-top: 8px;
          text-align: right;
          font-weight: 500;
          padding: 2px 8px;
          background: rgba(107, 114, 128, 0.1);
          border-radius: 4px;
          display: inline-block;
        }
      }
    }
  }
}

// Review表格区域样式
.review-table-section {
  .review-header {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 2px solid #e5e7eb;

    .view-review-btn {
      padding: 16px 32px;
      font-size: 16px;
      font-weight: 700;
      border-radius: 12px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      color: white;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      }
    }
  }

  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #f093fb;
    position: relative;

    &::before {
      content: '📝';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #f093fb, transparent);
    }
  }

  .download-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    justify-content: flex-end;

    .el-button {
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.el-button--primary {
        color: #3b82f6;

        &:hover {
          color: #1d4ed8;
        }
      }

      &.el-button--success {
        color: #10b981;

        &:hover {
          color: #059669;
        }
      }
    }
  }

  .table-card {
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      border-radius: 16px;
      overflow: hidden;
      border: none;

      .vxe-header--column {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-weight: 700;
        color: #ffffff;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);

        &:hover {
          background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
      }

      .vxe-body--row {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f5f9;

        &:hover {
          background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
          transform: scale(1.01);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        &:nth-child(even) {
          background: rgba(248, 250, 252, 0.5);
        }
      }

      .vxe-body--column {
        padding: 12px 16px;
      }
    }

    .review-text-cell {
      padding: 12px 8px;
      line-height: 1.6;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 500;
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
      border-radius: 6px;
      border-left: 3px solid #3b82f6;
    }

    .table-cell {
      padding: 8px 0;
      line-height: 1.5;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 500;

      &.count-cell {
        text-align: center;
        font-weight: 600;
        color: #1f2937;
      }

      &.percentage-cell {
        text-align: center;
        font-weight: 600;
        color: #667eea;
      }
    }

    .tags-container {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding: 8px 0;

      .tag-item {
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &.el-tag--success {
          background: linear-gradient(135deg, #10b981, #34d399);
          border: none;
        }

        &.el-tag--danger {
          background: linear-gradient(135deg, #ef4444, #f87171);
          border: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .voc-marking-container {
    padding: 24px;
  }

  .product-header {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .product-details .product-meta {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .voc-marking-container {
    padding: 16px;
    background: #f8fafc;
  }

  .page-header {
    margin-bottom: 32px;
    padding: 24px 16px;

    .page-title {
      font-size: 24px;
    }
  }

  .input-section {
    padding: 24px 16px;

    .input-label {
      font-size: 16px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      min-width: auto;
    }
  }

  .review-button-container {
    padding: 24px 16px;

    .el-button {
      min-width: auto;
      width: 100%;
    }
  }

  .analysis-grid {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .review-table-section {
    :deep(.vxe-table) {
      font-size: 12px;

      .vxe-body--column {
        padding: 8px 12px;
      }
    }

    .review-text {
      font-size: 13px;
      padding: 8px;
    }

    .tags-container .tag-item {
      font-size: 11px;
      padding: 2px 6px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.input-section {
  animation: fadeInUp 0.6s ease-out;
}

.action-buttons {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.data-info {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.marking-success, .marking-error {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-button-container {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-results-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
}

.product-card {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.5s;
  animation-fill-mode: both;
}

.analysis-section {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

.analysis-card {
  animation: slideInRight 0.6s ease-out;
  animation-delay: 0.7s;
  animation-fill-mode: both;

  &:nth-child(2) {
    animation-delay: 0.8s;
  }
}

.review-table-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.9s;
  animation-fill-mode: both;
}

// 滚动条美化
:deep(::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 4px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;

  &:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
  }
}
</style>
