<script lang="ts" setup>
import type { LatestMarkingRecordsResp, ProductInfoResp, ReviewDetailResp, } from "@/apis/voc"
import { generateReport, queryOldestVoc, queryReviewVoc, queryTagVoc, queryVocStatus, } from "@/apis/voc"
import { ContentWrap, } from "@/components/ContentWrap"
import { ElMessage, } from "element-plus"
import { computed, onMounted, ref, } from "vue"
import { MarkingStatusEnum, MaxPollAttempts, PageStateEnum, PollInterval, } from "./const.ts"

defineOptions({
  name: "VocMarking",
},)
// 响应式数据
const styleInput = ref("",)
const loading = ref(false,)
const showResults = ref(false,)
const showReviewTable = ref(false,)
const showDetailReviewTable = ref(false,)
const aiMarkingLoading = ref(false,)
const pageState = ref(PageStateEnum.Initial,) // 'initial', 'marking', 'results', 'review-detail'
const markingStatus = ref("",) // "success", "fail", "Analyzing"
const markingCount = ref(0,) // 剩余多少报告
const markingErrorMessage = ref("",)

// 数据定义
const productInfo = ref<ProductInfoResp>({},)

// 最新打标结果记录数据
const latestMarkingRecords = ref<LatestMarkingRecordsResp[]>([],)

// Review数据
const reviewData = ref<ReviewDetailResp[]>([],)

// 获取最新打标结果记录
async function fetchLatestRecords() {
  try {
    const response = await queryOldestVoc()
    if (response?.data) {
      latestMarkingRecords.value = response.data
    }
  } catch (error) {
    console.error("获取最新记录失败:", error,)
    ElMessage.error("获取最新记录失败",)
  }
}

// 处理输入框变化
function handleInputChange() {
  if (styleInput.value.trim()) {
    productInfo.value.style = styleInput.value.trim()
    showResults.value = true
  } else {
    showResults.value = false
    showReviewTable.value = false
  }
}

// 处理开始AI打标
async function handleStartAIMarking() {
  if (aiMarkingLoading.value || !styleInput.value.trim()) {
    ElMessage.warning("请输入Style编号",)
    return
  }

  aiMarkingLoading.value = true
  markingStatus.value = ""
  markingErrorMessage.value = ""
  pageState.value = PageStateEnum.Marking

  try {
    // 调用开始打标API
    const response = await generateReport({ styleNo: styleInput.value.trim(), },)
    if (response?.data) {
      markingStatus.value = response.data as string
      if (markingStatus.value === MarkingStatusEnum.Analyzing) {
        // 轮询查询打标状态
        pageState.value = PageStateEnum.Marking
        await pollMarkingStatus()
      }
      if (markingStatus.value === MarkingStatusEnum.Success) {
        pageState.value = PageStateEnum.Results
        aiMarkingLoading.value = false
        // 获取产品信息和打标结果
        const style = styleInput.value.trim() as string
        await fetchProductInfo(style,)
        ElMessage.success("AI打标完成",)
      }
    }
  } catch (error) {
    markingStatus.value = MarkingStatusEnum.Fail
    pageState.value = PageStateEnum.Initial
    aiMarkingLoading.value = false
    markingErrorMessage.value = error.message || "网络连接超时，请检查网络后重试"
    ElMessage.error("AI打标失败，请重试",)
  }
}

// 轮询查询打标状态
async function pollMarkingStatus() {
  const maxAttempts = MaxPollAttempts // 最多轮询60次，每次30秒，总共1800分钟
  let attempts = 0

  const poll = async() => {
    const response = await queryVocStatus({ styleNo: styleInput.value.trim(), },)
    const status = response?.data?.reportStatus
    markingCount.value = response?.data?.analyzingCount
    markingStatus.value = status
    if (status === MarkingStatusEnum.Success) {
      pageState.value = PageStateEnum.Results
      aiMarkingLoading.value = false
      // 获取产品信息和打标结果
      await fetchProductInfo(styleInput.value.trim(),),

      ElMessage.success("AI打标完成！",)
    } else if (status === MarkingStatusEnum.Fail) {
      throw new Error(response.data?.statusDesc || "打标失败",)
    } else if (status === MarkingStatusEnum.Analyzing && attempts < maxAttempts) {
      attempts++
      setTimeout(poll, PollInterval,) // 30秒后再次查询
    } else {
      throw new Error("打标超时，请重试",)
    }
  }

  await poll()
}

// 获取产品信息
async function fetchProductInfo(styleNo: string,) {
  try {
    const response = await queryTagVoc({ styleNo, },)
    if (response?.data) {
      productInfo.value = response.data
    }
  } catch (error) {
    console.error("获取产品信息失败:", error,)
  }
}

// 获取Review详细数据
async function fetchReviewDetails(params,) {
  try {
    const response = await queryReviewVoc(params,)
    if (response?.data) {
      reviewData.value = response.data as ReviewDetailResp[]
    }
    pageState.value = PageStateEnum.ReviewDetail
    showDetailReviewTable.value = true
  } catch (error) {
    console.error("获取Review详细数据失败:", error,)
    ElMessage.error("获取Review详细数据失败",)
  }
}

// 处理查看Style结果
async function handleViewStyleResults(record: LatestMarkingRecordsResp,) {
  if (!record.style) {
    ElMessage.warning("Style信息不完整",)
    return
  }

  try {
    loading.value = true
    // 设置当前查看的Style
    styleInput.value = record.style

    // 获取Style的详细结果
    const response = await queryReviewVoc({
      styleNo: record.style,
      reportDate: record.submitTime,
      reportId: record.id,
    },)
    if (response?.data) {
      productInfo.value = response.data.productInfo
      pageState.value = PageStateEnum.Results
      markingStatus.value = MarkingStatusEnum.Success
    }

    ElMessage.success(`正在查看 ${record.style} 的打标结果`,)
  } catch (error) {
    console.error("查看Style结果失败:", error,)
    ElMessage.error("查看Style结果失败",)
  } finally {
    loading.value = false
  }
}

// 处理下载文件
async function handleDownloadFile(filename: string,) {
  // try {
  //   const response = await downloadFile(filename,)
  //
  //   // 创建下载链接
  //   const url = window.URL.createObjectURL(new Blob([response.data,],),)
  //   const link = document.createElement("a",)
  //   link.href = url
  //   link.setAttribute("download", filename,)
  //   document.body.appendChild(link,)
  //   link.click()
  //   document.body.removeChild(link,)
  //   window.URL.revokeObjectURL(url,)
  //
  //   ElMessage.success(`文件 ${filename} 下载成功`,)
  // } catch (error) {
  //   console.error("下载文件失败:", error,)
  //   ElMessage.error("下载文件失败",)
  // }
}

// 计算样本数量提示
const sampleCountText = computed(() => {
  return `有不重复${productInfo.value.style}数据 ${productInfo.value.reviewCount}条数据`
},)

// 页面初始化
onMounted(() => {
  fetchLatestRecords()
},)
</script>

<template>
  <ContentWrap>
    <div class="voc-marking-container">
      <div class="voc-inner-container">
        <!-- 页面标题和输入区域 -->
        <div class="header-input-section">
          <div class="header-content">
            <h1 class="page-title">
              产品Review VOC分析报告
            </h1>
            <p class="page-subtitle">
              智能分析产品评论，提取关键洞察
            </p>
          </div>
          <div class="input-area">
            <div class="input-group">
              <label class="input-label">输入需要打标的Style:</label>
              <div class="input-wrapper">
                <ElInput
                  v-model="styleInput"
                  class="style-input"
                  placeholder="请输入Style编号"
                  size="large"
                  @input="handleInputChange"
                />
                <ElButton
                  :disabled="!styleInput.trim() || aiMarkingLoading"
                  :loading="aiMarkingLoading"
                  class="submit-btn"
                  size="large"
                  type="primary"
                  @click="handleStartAIMarking"
                >
                  开始AI打标
                </ElButton>
              </div>
            </div>
          </div>
        </div>
        <!-- 数据统计提示 -->
        <div v-if="showResults" class="data-info">
          <ElAlert
            :closable="false"
            :title="sampleCountText"
            type="info"
            show-icon
          />
        </div>
        <div v-if="markingStatus === MarkingStatusEnum.Analyzing" class="marking-analyzing">
          <span v-loading="true" />
          <p>AI算法引擎精准打标进行中请稍等</p>
          <span>前面有 {{ markingCount }} 个报告在等待</span>
        </div>
        <!-- Review详细数据表格 -->
        <div v-if="pageState === PageStateEnum.ReviewDetail" class="review-detail-table">
          <div v-if="showDetailReviewTable" class="review-table-container">
            <h3 class="review-title">
              Review详细数据及打标结果
              <ElButton
                class="back-btn"
                type="primary"
                @click="() => {
                  if (productInfo.styleNo){
                    pageState = PageStateEnum.Results
                  }
                  else {
                    pageState = PageStateEnum.Initial
                  }
                }"
              >
                返回
              </ElButton>
            </h3>
            <ElCard class="detail-table-card">
              <VxeTable
                :column-config="{ resizable: true }"
                :data="reviewData"
                :row-config="{ height: 120 }"
              >
                <VxeColumn field="reviewText" min-width="350" title="评论原文">
                  <template #default="{ row }">
                    <div class="review-text-content">
                      {{ row.reviewText }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="advantages" min-width="200" title="优点标签">
                  <template #default="{ row }">
                    <div class="tags-container">
                      {{ row.advantage }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="disadvantages" min-width="200" title="缺点标签">
                  <template #default="{ row }">
                    <div class="tags-container">
                      {{ row.defect }}
                    </div>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElCard>
          </div>
        </div>
        <!-- AI打标成功结果显示 -->
        <div
          v-if="markingStatus === MarkingStatusEnum.Success && pageState === PageStateEnum.Results"
          class="marking-success-result"
        >
          <!-- 产品信息卡片 -->
          <div class="product-result-card">
            <div class="product-header">
              <div class="product-info">
                <div class="product-image">
                  <ElImage
                    :preview-src-list="[productInfo.styleImg as string]"
                    :src="productInfo.styleImg"
                    fit="cover"
                  />
                </div>
                <div class="product-details">
                  <h2 class="product-title">
                    {{ productInfo.styleNo }}
                  </h2>
                  <div class="product-meta">
                    <div class="meta-item">
                      <span class="meta-label">Style:</span>
                      <span class="meta-value">{{ productInfo.styleNo }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="product-actions">
                <ElButton
                  class="view-review-btn"
                  size="large"
                  type="primary"
                  @click="fetchReviewDetails({
                    styleNo: productInfo.styleNo,
                  })"
                >
                  查看Review以及打标结果
                </ElButton>
                <ElButton type="success" link>
                  打标评论数据下载
                </ElButton>
                <ElButton type="warning" link>
                  打标统计结果下载
                </ElButton>
              </div>
            </div>
          </div>

          <!-- 打标结果表格 -->
          <div class="marking-result-table">
            <h3 class="review-title">
              用户对产品的哪些优点很满意
            </h3>
            <ElCard class="result-table-card">
              <VxeTable
                :column-config="{ resizable: true }"
                :data="productInfo?.advantageList"
                :row-config="{ height: 50 }"
              >
                <VxeColumn field="firstLevelTag" min-width="120" title="一级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.firstLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="secondLevelTag" min-width="120" title="二级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.secondLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="thirdLevelTag" min-width="120" title="三级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.thirdLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="descriptionprosConsDesc" min-width="120" title="优点">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.prosConsDesc }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="frequency"
                  title="数量"
                  width="100"
                >
                  <template #default="{ row }">
                    <div class="count-cell table-cell">
                      {{ row?.frequency }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="percentage"
                  title="百分比"
                  width="120"
                >
                  <template #default="{ row }">
                    <div class="percentage-cell table-cell">
                      {{ row.percentage }}
                    </div>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElCard>
          </div>
          <!-- 打标结果表格 -->
          <div class="marking-result-table">
            <h3 class="review-title">
              用户对产品的哪些缺点不满意
            </h3>
            <ElCard class="result-table-card">
              <VxeTable
                :column-config="{ resizable: true }"
                :data="productInfo?.defectList"
                :row-config="{ height: 50 }"
              >
                <VxeColumn field="firstLevelTag" min-width="120" title="一级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.firstLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="secondLevelTag" min-width="120" title="二级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.secondLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="thirdLevelTag" min-width="120" title="三级标签">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.thirdLevelTag }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn field="descriptionprosConsDesc" min-width="120" title="优点">
                  <template #default="{ row }">
                    <div class="table-cell">
                      {{ row.prosConsDesc }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="frequency"
                  title="数量"
                  width="100"
                >
                  <template #default="{ row }">
                    <div class="count-cell table-cell">
                      {{ row.frequency }}
                    </div>
                  </template>
                </VxeColumn>
                <VxeColumn
                  align="center"
                  field="percentage"
                  title="百分比"
                  width="120"
                >
                  <template #default="{ row }">
                    <div class="percentage-cell table-cell">
                      {{ row.percentage }}
                    </div>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElCard>
          </div>
        </div>

        <div v-if="markingStatus === MarkingStatusEnum.Fail" class="marking-error">
          <ElAlert
            :closable="false"
            title="AI打标失败"
            type="error"
            show-icon
          >
            <template #default>
              <div class="error-content">
                <p>失败原因：{{ markingErrorMessage }}</p>
                <ElButton type="danger" @click="handleStartAIMarking">
                  🔄 重新打标
                </ElButton>
              </div>
            </template>
          </ElAlert>
        </div>
        <!-- 最新打标结果记录 -->
        <div v-if="pageState === PageStateEnum.Initial" class="latest-records-section">
          <h2 class="review-title">
            最新的打标结果记录
          </h2>
          <ElCard class="records-card">
            <VxeTable
              :column-config="{ resizable: true }"
              :data="latestMarkingRecords"
              :row-config="{ height: 60 }"
            >
              <VxeColumn title="序号" type="seq" width="60" />
              <VxeColumn field="style" min-width="120" title="Style">
                <template #default="{ row }">
                  <div class="style-cell">
                    <ElButton
                      v-if="row.styleNo"
                      type="primary"
                      link
                      @click="handleViewStyleResults(row)"
                    >
                      {{ row.styleNo }}
                    </ElButton>
                    <span v-else class="empty-style">{{ row.styleNo }}</span>
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn
                align="center"
                field="createTime"
                title="提交时间"
                width="180"
              >
                <template #default="{ row }">
                  <div class="time-cell">
                    {{ row.createTime }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn
                align="center"
                field="reportFinishTime"
                title="完成时间"
                width="180"
              >
                <template #default="{ row }">
                  <div class="time-cell">
                    {{ row.reportFinishTime }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="reviewDataFile" title="评论清单数据" width="200">
                <template #default="{ row }">
                  <div class="file-cell">
                    <ElButton
                      type="success"
                      link
                      @click="fetchReviewDetails({
                        styleNo: row.styleNo,
                        id: row.id,
                      })"
                    >
                      查看清单
                    </ElButton>
                  </div>
                </template>
              </VxeColumn>
            </VxeTable>
          </ElCard>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.voc-marking-container {
  padding: 24px;
  background: linear-gradient(-45deg, #f8fafc, #e2e8f0, #f1f5f9, #ddd6fe);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  min-height: 100vh;
  position: relative;
  margin-top: -20px;
  margin-left: -20px;
  overflow: hidden;
  width: calc(100% + 40px);

  .marking-analyzing {
    text-align: center;
    font-size: 40px;

    :deep( .el-loading-spinner .circular ) {
      height: 60px;
      width: 60px;
    }

    p {
      margin-top: 35px;
      font-size: 28px;
    }

    span {
      display: inline-block;
      width: 100%;
      font-size: 18px;
      color: #737373;

    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: floatingBubbles 20s ease-in-out infinite;
    pointer-events: none;
  }

  .customClass {
    font-size: 20px;
  }

  .voc-inner-container {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes floatingBubbles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-30px) rotate(120deg);
    opacity: 0.9;
  }
  66% {
    transform: translateY(-60px) rotate(240deg);
    opacity: 0.8;
  }
}

// 页面标题和输入区域样式
.header-input-section {
  margin-bottom: 2px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: slideInDown 0.8s ease-out;

  .header-content {
    text-align: center;
    padding: 40px 24px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: rotate 20s linear infinite;
    }

    .page-title {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 8px 0;
      letter-spacing: -0.5px;
      position: relative;
      z-index: 1;
      animation: titleGlow 3s ease-in-out infinite alternate;
    }

    .page-subtitle {
      font-size: 16px;
      font-weight: 400;
      margin: 0;
      opacity: 0.9;
      position: relative;
      z-index: 1;
      animation: subtitleFloat 4s ease-in-out infinite;
    }
  }

  .input-area {
    padding: 24px;

    .input-group {
      max-width: 700px;
      margin: 0 auto;

      .input-label {
        display: block;
        font-size: 16px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 12px;
      }

      .input-wrapper {
        display: flex;
        gap: 20px;
        align-items: stretch;

        .style-input {
          flex: 1;

          :deep(.el-input__wrapper) {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 50px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

            &:hover {
              border-color: #667eea;
              box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
            }

            &.is-focus {
              border-color: #667eea;
              box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            }

            .el-input__inner {
              font-size: 16px;
              font-weight: 500;
              color: #374151;
              padding: 0 16px;

              &::placeholder {
                color: #9ca3af;
                font-weight: 400;
              }
            }
          }
        }

        .submit-btn {
          border-radius: 12px;
          padding: 0 32px;
          font-weight: 600;
          font-size: 16px;
          height: 50px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: white;
          transition: all 0.3s ease;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);

            &::before {
              left: 100%;
            }
          }

          &:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleGlow {
  from {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  to {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

@keyframes subtitleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressBar {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

// 最新打标结果记录样式
.latest-records-section {
  margin-bottom: 32px;
  margin-top: 20px;
  animation: slideInUp 0.8s ease-out 0.2s both;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
      animation: shimmer 2s infinite;
    }
  }

  .records-card {
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.8s ease-out 0.4s both;
    transition: all 0.3s ease;
    padding: 10px 20px;

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      .vxe-header--column {
        background: #f9fafb;
        font-weight: 500;
        color: #374151;
        font-size: 14px;
      }

      .vxe-body--row {
        &:hover {
          background: #f9fafb;
        }
      }
    }

    .record-id {
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }

    .style-cell {
      .el-button {
        font-weight: 600;
        font-size: 14px;
        color: #667eea;
        transition: all 0.2s ease;

        &:hover {
          color: #5a67d8;
          transform: translateY(-1px);
        }
      }

      .empty-style {
        color: #9ca3af;
        font-style: italic;
        font-size: 13px;
      }
    }

    .time-cell {
      font-size: 14px;
      color: #6b7280;
      font-weight: 400;
    }

    .file-cell {
      .el-button {
        font-size: 14px;
        font-weight: 600;
        color: #10b981;
        transition: all 0.2s ease;

        &:hover {
          color: #059669;
          transform: translateY(-1px);
        }
      }

      .empty-file {
        color: #9ca3af;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

// AI打标成功结果样式
.marking-success-result {
  margin: 32px 0;
  animation: slideInUp 0.8s ease-out;

  .product-result-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 32px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #10b981, #059669);
      animation: progressBar 2s ease-out;
    }

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 24px;

      .product-info {
        display: flex;
        gap: 16px;
        flex: 1;

        .product-image {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid #e5e7eb;

          :deep(.el-image) {
            width: 100%;
            height: 100%;
          }
        }

        .product-details {
          flex: 1;

          .product-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
          }

          .product-meta {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .meta-label {
                font-weight: 500;
                color: #6b7280;
                min-width: 70px;
                font-size: 15px;
              }

              .meta-value {
                color: #374151;
                font-weight: 400;
                font-size: 15px;

                &.link-text {
                  color: #3b82f6;
                  text-decoration: underline;
                  cursor: pointer;

                  &:hover {
                    color: #1d4ed8;
                  }
                }
              }
            }
          }
        }
      }

      .product-actions {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .view-review-btn {
          font-size: 16px;
          font-weight: 600;
          border-radius: 12px;
          margin-top: 30px;
          height: 40px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: white;
          transition: all 0.3s ease;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);

            &::before {
              left: 100%;
            }
          }
        }

        .el-button {
          font-size: 14px;
          font-weight: 600;
          padding: 10px 20px;
          border-radius: 10px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);

            &::before {
              left: 100%;
            }
          }

          &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;

            &:hover {
              background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            }
          }

          &.el-button--success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            color: white;

            &:hover {
              background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
          }

          &.el-button--warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border: none;
            color: white;

            &:hover {
              background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            }
          }
        }
      }
    }
  }
}

.marking-result-table {
  margin-bottom: 20px;

  .table-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f0fdf4;
    border-radius: 6px;
    border-left: 3px solid #667eea;
  }

  .result-table-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 0;
    }

    .table-cell {
      padding: 8px 0;
      line-height: 1.4;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 400;

      &.count-cell {
        text-align: center;
        font-weight: 500;
        color: #374151;
      }

      &.percentage-cell {
        text-align: center;
        font-weight: 500;
        color: #3b82f6;
      }
    }
  }
}

.review-detail-table {
  margin-top: 32px;

  .review-header {
    text-align: left;
    margin-bottom: 20px;
    height: 46px;
  }

  .review-table-container {
    animation: fadeInUp 0.3s ease-out;

    .detail-table-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid #e5e7eb;
      overflow: hidden;

      :deep(.el-card__body) {
        padding: 0;
      }

      .review-text-content {
        padding: 12px;
        line-height: 1.5;
        color: #374151;
        word-break: break-word;
        font-size: 14px;
        font-weight: 400;
        white-space: pre-wrap;
        max-height: 100px;
        overflow-y: auto;
        background: #f9fafb;
        border-radius: 4px;
        border-left: 2px solid #3b82f6;
      }

      .tags-container {
        padding: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        align-items: flex-start;

        .tag-item {
          margin: 1px;
          font-size: 12px;
          font-weight: 400;
          padding: 4px 8px;
          border-radius: 12px;

          &.advantage-tag {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
          }

          &.disadvantage-tag {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
          }
        }
      }

      .rating-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px;

        :deep(.el-rate) {
          .el-rate__text {
            font-size: 11px;
            color: #6b7280;
            margin-left: 6px;
          }
        }
      }

      .date-cell {
        font-size: 12px;
        color: #6b7280;
        font-weight: 400;
        text-align: center;
      }
    }
  }
}

// AI打标状态样式
.marking-error {
  margin: 32px 0;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    padding: 28px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .el-alert__icon {
      font-size: 24px;
    }

    .el-alert__title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 16px;
    }
  }
}

.marking-success {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-left: 6px solid #10b981;

    .el-alert__icon {
      color: #10b981;
    }

    .el-alert__title {
      color: #065f46;
    }
  }

  .success-content {
    p {
      margin: 8px 0;
      font-size: 15px;
      font-weight: 500;
      color: #047857;
    }

    .feishu-link {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid rgba(16, 185, 129, 0.2);

      .el-button {
        font-size: 16px;
        font-weight: 600;
        padding: 8px 16px;
        border-radius: 8px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }
}

.marking-error {
  :deep(.el-alert) {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border-left: 6px solid #ef4444;

    .el-alert__icon {
      color: #ef4444;
    }

    .el-alert__title {
      color: #991b1b;
    }
  }

  .error-content {
    p {
      margin: 8px 0 16px 0;
      font-size: 15px;
      font-weight: 500;
      color: #dc2626;
    }

    .el-button {
      font-size: 14px;
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 8px;
      background: linear-gradient(135deg, #ef4444, #dc2626);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        background: linear-gradient(135deg, #dc2626, #b91c1c);
      }
    }
  }
}

.data-info {
  margin-bottom: 32px;

  :deep(.el-alert) {
    border-radius: 16px;
    border: none;
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 50%, #fce7f3 100%);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
    padding: 20px 24px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    }

    .el-alert__icon {
      font-size: 20px;
      color: #3b82f6;
    }

    .el-alert__title {
      font-weight: 400;
      font-size: 18px;
      color: #1e40af;
    }
  }
}

.review-button-container {
  text-align: center;
  margin: 40px 0;
  padding: 32px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  .el-button {
    border-radius: 16px;
    padding: 20px 48px;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 280px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.6s;
    }

    &:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow: 0 12px 32px rgba(240, 147, 251, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-2px) scale(1.01);
    }
  }
}

// Review结果展示区域样式
.review-results-section {
  margin-top: 48px;
  padding: 40px;
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    border-radius: 20px 20px 0 0;
  }
}

// 产品信息卡片样式
.product-card {
  margin-bottom: 40px;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      padding: 2px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 16px;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
    }
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .product-image {
    width: 100px;
    height: 100px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
      z-index: 1;
    }

    :deep(.el-image) {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .product-details {
    flex: 1;

    .product-title {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 16px 0;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .product-meta {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 8px;
        border-left: 3px solid #667eea;

        .meta-label {
          font-weight: 600;
          color: #4b5563;
          min-width: 70px;
        }

        .meta-value {
          color: #1f2937;
          font-weight: 600;
        }
      }
    }
  }

  .product-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .el-button {
      border-radius: 10px;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateX(6px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        &::before {
          left: 100%;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border: none;
      }

      &.el-button--success {
        background: linear-gradient(135deg, #10b981, #059669);
        border: none;
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border: none;
      }
    }
  }
}

// 分析区域样式
.analysis-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #667eea;
    position: relative;

    &::before {
      content: '📊';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #667eea, transparent);
    }
  }

  .analysis-grid {
    .analysis-card {
      height: 100%;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
      }

      &:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
      }

      :deep(.el-card__header) {
        padding: 20px 24px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-bottom: 2px solid #f1f5f9;
        position: relative;
      }

      :deep(.el-card__body) {
        padding: 24px;
        background: #ffffff;
      }
    }

    .advantages-card {
      border-top: 4px solid #10b981;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #34d399);
      }
    }

    .disadvantages-card {
      border-top: 4px solid #ef4444;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #ef4444, #f87171);
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .card-icon {
        font-size: 22px;
        padding: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &.success-icon {
          color: #10b981;
          background: linear-gradient(135deg, #d1fae5, #a7f3d0);
        }

        &.danger-icon {
          color: #ef4444;
          transform: rotate(180deg);
          background: linear-gradient(135deg, #fee2e2, #fecaca);
        }
      }

      .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }
    }

    .analysis-list {
      .analysis-item {
        margin-bottom: 24px;
        padding: 16px;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateX(4px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
          border-color: #667eea;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .item-label {
            font-weight: 600;
            color: #1f2937;
            font-size: 15px;
          }

          .item-percentage {
            font-weight: 700;
            color: #667eea;
            font-size: 16px;
            padding: 4px 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
          }
        }

        :deep(.el-progress) {
          .el-progress-bar__outer {
            border-radius: 6px;
            background: #f1f5f9;
          }

          .el-progress-bar__inner {
            border-radius: 6px;
            transition: all 0.3s ease;
          }
        }

        .item-count {
          font-size: 13px;
          color: #6b7280;
          margin-top: 8px;
          text-align: right;
          font-weight: 500;
          padding: 2px 8px;
          background: rgba(107, 114, 128, 0.1);
          border-radius: 4px;
          display: inline-block;
        }
      }
    }
  }
}

// Review表格区域样式
.review-table-section {
  .review-header {
    text-align: center;
    margin-bottom: 24px;

    .view-review-btn {
      padding: 16px 32px;
      font-size: 16px;
      font-weight: 700;
      border-radius: 12px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      color: white;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      }
    }
  }

  .section-title {
    font-size: 22px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border-left: 6px solid #f093fb;
    position: relative;

    &::before {
      content: '📝';
      margin-right: 8px;
      font-size: 20px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 2px;
      background: linear-gradient(90deg, #f093fb, transparent);
    }
  }

  .download-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    justify-content: flex-end;

    .el-button {
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.el-button--primary {
        color: #3b82f6;

        &:hover {
          color: #1d4ed8;
        }
      }

      &.el-button--success {
        color: #10b981;

        &:hover {
          color: #059669;
        }
      }
    }
  }

  .table-card {
    border-radius: 16px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      border-radius: 16px;
      overflow: hidden;
      border: none;

      .vxe-header--column {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-weight: 700;
        color: #ffffff;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);

        &:hover {
          background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }
      }

      .vxe-body--row {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f5f9;

        &:hover {
          background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
          transform: scale(1.01);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        &:nth-child(even) {
          background: rgba(248, 250, 252, 0.5);
        }
      }

      .vxe-body--column {
        padding: 12px 16px;
      }
    }

    .review-text-cell {
      padding: 12px 8px;
      line-height: 1.6;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 500;
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
      border-radius: 6px;
      border-left: 3px solid #3b82f6;
    }

    .table-cell {
      padding: 8px 0;
      line-height: 1.5;
      color: #374151;
      word-break: break-word;
      font-size: 14px;
      font-weight: 500;

      &.count-cell {
        text-align: center;
        font-weight: 600;
        color: #1f2937;
      }

      &.percentage-cell {
        text-align: center;
        font-weight: 600;
        color: #667eea;
      }
    }

    .tags-container {
      display: flex;
      flex-direction: column;
      gap: 6px;
      padding: 8px 0;

      .tag-item {
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &.el-tag--success {
          background: linear-gradient(135deg, #10b981, #34d399);
          border: none;
        }

        &.el-tag--danger {
          background: linear-gradient(135deg, #ef4444, #f87171);
          border: none;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .voc-marking-container {
    padding: 24px;
  }

  .product-header {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .product-details .product-meta {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .voc-marking-container {
    padding: 16px;
    background: #f8fafc;
  }
  .input-section {
    padding: 24px 16px;

    .input-label {
      font-size: 16px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      min-width: auto;
    }
  }

  .review-button-container {
    padding: 24px 16px;

    .el-button {
      min-width: auto;
      width: 100%;
    }
  }

  .analysis-grid {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .review-table-section {
    :deep(.vxe-table) {
      font-size: 12px;

      .vxe-body--column {
        padding: 8px 12px;
      }
    }

    .review-text {
      font-size: 13px;
      padding: 8px;
    }

    .tags-container .tag-item {
      font-size: 11px;
      padding: 2px 6px;
    }
  }
}

.review-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  padding: 12px 16px;
  background: white;
  border-radius: 6px 6px 0 0;
  border-left: 3px solid #667eea;
  position: relative;

  .back-btn {
    float: right;
    top: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.input-section {
  animation: fadeInUp 0.6s ease-out;
}

.action-buttons {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.data-info {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.marking-success, .marking-error {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-button-container {
  animation: scaleIn 0.6s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.review-results-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
}

.product-card {
  animation: slideInLeft 0.6s ease-out;
  animation-delay: 0.5s;
  animation-fill-mode: both;
}

.analysis-section {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

.analysis-card {
  animation: slideInRight 0.6s ease-out;
  animation-delay: 0.7s;
  animation-fill-mode: both;

  &:nth-child(2) {
    animation-delay: 0.8s;
  }
}

.review-table-section {
  animation: fadeInUp 0.8s ease-out;
  animation-delay: 0.9s;
  animation-fill-mode: both;
}

// 滚动条美化
:deep(::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(::-webkit-scrollbar-track) {
  background: #f1f5f9;
  border-radius: 4px;
}

:deep(::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;

  &:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
  }
}
</style>
