<script lang="ts" setup>
import { ContentWrap, } from "@/components/ContentWrap"
import { computed, reactive, ref, } from "vue"

defineOptions({
  name: "VocMarking",
},)
// 响应式数据
const styleInput = ref("",)
const loading = ref(false,)
const showResults = ref(false,)
const showReviewTable = ref(false,)

// 模拟数据
const productInfo = reactive({
  style: "CHUNK",
  productName: "CHUNK 女士高跟鞋",
  image: "https://via.placeholder.com/120x120",
  amazonUrl: "https://amazon.com/product/chunk",
  reviewCount: 3578,
  price: "$29.99",
},)

// 模拟Review数据
const reviewData = ref([
  {
    id: 1,
    reviewText: "Love! So comfortable",
    advantages: ["产品舒服",],
    disadvantages: ["商品适合",],
    rating: 5,
    date: "2024-01-15",
  },
  {
    id: 2,
    reviewText: "Me gusta mucho",
    advantages: ["商品适合", "质量准确",],
    disadvantages: ["送货准确",],
    rating: 4,
    date: "2024-01-14",
  },
  {
    id: 3,
    reviewText: "Just like the photo",
    advantages: ["产品舒服", "鞋子很轻",],
    disadvantages: ["外观预期",],
    rating: 5,
    date: "2024-01-13",
  },
  {
    id: 4,
    reviewText: "Lovely comfortable fit. Nice weight",
    advantages: ["产品舒服", "鞋子很轻",],
    disadvantages: [],
    rating: 5,
    date: "2024-01-12",
  },
],)

// 优缺点统计数据
const analysisData = reactive({
  advantages: [
    { label: "产品舒服", count: 53, percentage: 25.23, },
    { label: "产品设计", count: 33, percentage: 22.0, },
    { label: "只是美观", count: 26, percentage: 17.33, },
    { label: "外观可爱", count: 19, percentage: 12.67, },
    { label: "鞋子很轻", count: 17, percentage: 11.33, },
  ],
  disadvantages: [
    { label: "外观预期", count: 26, percentage: 17.33, },
    { label: "商品适合", count: 19, percentage: 12.67, },
    { label: "质量准确", count: 17, percentage: 11.33, },
  ],
},)

// 处理查看评论按钮
function handleViewReviews() {
  showReviewTable.value = true
}

// 处理输入框变化
function handleInputChange() {
  if (styleInput.value.trim()) {
    productInfo.style = styleInput.value.trim()
    showResults.value = true
  } else {
    showResults.value = false
    showReviewTable.value = false
  }
}

// 处理查看评论和达标结果
function handleViewReviewResults() {
  showReviewTable.value = true
}

// 处理开始AI打标
function handleStartAIMarking() {
  ElMessage.success("AI打标功能开发中...",)
}

// 处理生成飞书文档
function handleGenerateFeishuDoc() {
  ElMessage.success("生成飞书文档功能开发中...",)
}

// 计算样本数量提示
const sampleCountText = computed(() => {
  return `有不重复${productInfo.style}数据 ${productInfo.reviewCount}条数据`
},)
</script>

<template>
  <ContentWrap>
    <div class="voc-marking-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          产品Review VOC分析报告
        </h1>
      </div>

      <!-- 输入区域 -->
      <div class="input-section">
        <div class="input-label">
          输入需要打标的style:
        </div>
        <div class="input-container">
          <ElInput
            v-model="styleInput"
            class="style-input"
            placeholder="请输入Style编号"
            size="large"
            @input="handleInputChange"
          />
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <ElButton
          :disabled="!styleInput.trim()"
          size="large"
          type="primary"
          @click="handleViewReviews"
        >
          查看评论条数
        </ElButton>
        <ElButton
          :disabled="!styleInput.trim()"
          size="large"
          type="success"
          @click="handleStartAIMarking"
        >
          开始AI打标 & 生成飞书文档
        </ElButton>
      </div>

      <!-- 数据统计提示 -->
      <div v-if="showResults" class="data-info">
        <ElAlert
          :closable="false"
          :title="sampleCountText"
          type="info"
          show-icon
        />
      </div>

      <!-- 查看Review按钮 -->
      <div v-if="showResults" class="review-button-container">
        <ElButton
          size="large"
          type="primary"
          @click="handleViewReviewResults"
        >
          查看Review以及达标结果
        </ElButton>
      </div>

      <!-- Review表格显示区域 -->
      <div v-if="showReviewTable" class="review-results-section">
        <!-- 产品信息卡片 -->
        <div class="product-card">
          <div class="product-header">
            <div class="product-info">
              <div class="product-image">
                <ElImage
                  :preview-src-list="[productInfo.image]"
                  :src="productInfo.image"
                  fit="cover"
                />
              </div>
              <div class="product-details">
                <h2 class="product-title">
                  {{ productInfo.style }}
                </h2>
                <div class="product-meta">
                  <div class="meta-item">
                    <span class="meta-label">Style：</span>
                    <span class="meta-value">{{ productInfo.style }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">样本数：</span>
                    <span class="meta-value">{{ productInfo.reviewCount }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">价格：</span>
                    <span class="meta-value">{{ productInfo.price }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="product-actions">
              <ElButton type="primary" link>
                <Icon icon="ep:view" />
                展示产品主图
              </ElButton>
              <ElButton type="success" link>
                支持打标评论数据下载
              </ElButton>
              <ElButton type="warning" link>
                支持打标统计结果下载
              </ElButton>
            </div>
          </div>
        </div>

        <!-- 用户评论优缺点分析 -->
        <div class="analysis-section">
          <h3 class="section-title">
            用户对产品的哪些优点很满意
          </h3>
          <div class="analysis-grid">
            <ElRow :gutter="24">
              <ElCol :span="12">
                <ElCard class="analysis-card advantages-card">
                  <template #header>
                    <div class="card-header">
                      <Icon class="card-icon success-icon" icon="ep:thumb" />
                      <span class="card-title">优点</span>
                    </div>
                  </template>
                  <div class="analysis-list">
                    <div
                      v-for="item in analysisData.advantages"
                      :key="item.label"
                      class="analysis-item"
                    >
                      <div class="item-header">
                        <span class="item-label">{{ item.label }}</span>
                        <span class="item-percentage">{{ item.percentage }}%</span>
                      </div>
                      <ElProgress
                        :percentage="item.percentage"
                        :show-text="false"
                        :stroke-width="8"
                        status="success"
                      />
                      <div class="item-count">
                        {{ item.count }} 条评论
                      </div>
                    </div>
                  </div>
                </ElCard>
              </ElCol>
              <ElCol :span="12">
                <ElCard class="analysis-card disadvantages-card">
                  <template #header>
                    <div class="card-header">
                      <Icon class="card-icon danger-icon" icon="ep:thumb" />
                      <span class="card-title">缺点</span>
                    </div>
                  </template>
                  <div class="analysis-list">
                    <div
                      v-for="item in analysisData.disadvantages"
                      :key="item.label"
                      class="analysis-item"
                    >
                      <div class="item-header">
                        <span class="item-label">{{ item.label }}</span>
                        <span class="item-percentage">{{ item.percentage }}%</span>
                      </div>
                      <ElProgress
                        :percentage="item.percentage"
                        :show-text="false"
                        :stroke-width="8"
                        status="exception"
                      />
                      <div class="item-count">
                        {{ item.count }} 条评论
                      </div>
                    </div>
                  </div>
                </ElCard>
              </ElCol>
            </ElRow>
          </div>
        </div>

        <!-- Review数据表格 -->
        <div class="review-table-section">
          <h3 class="section-title">
            Review详细数据
          </h3>
          <ElCard class="table-card">
            <VxeTable
              :column-config="{ resizable: true }"
              :data="reviewData"
              :row-config="{ height: 80 }"

              
 stripe border 
            >
              <VxeColumn field="reviewText" min-width="200" title="review_text">
                <template #default="{ row }">
                  <div class="review-text">
                    {{ row.reviewText }}
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="advantages" title="优点" width="150">
                <template #default="{ row }">
                  <div class="tags-container">
                    <ElTag
                      v-for="tag in row.advantages"
                      :key="tag"
                      class="tag-item"
                      size="small"
                      type="success"
                    >
                      {{ tag }}
                    </ElTag>
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="disadvantages" title="缺点" width="150">
                <template #default="{ row }">
                  <div class="tags-container">
                    <ElTag
                      v-for="tag in row.disadvantages"
                      :key="tag"
                      class="tag-item"
                      size="small"
                      type="danger"
                    >
                      {{ tag }}
                    </ElTag>
                  </div>
                </template>
              </VxeColumn>
              <VxeColumn field="rating" title="评分" width="100">
                <template #default="{ row }">
                  <ElRate :model-value="row.rating" size="small" disabled />
                </template>
              </VxeColumn>
              <VxeColumn field="date" title="日期" width="120" />
            </VxeTable>
          </ElCard>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.voc-marking-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #6366f1;
    margin: 0;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
      border-radius: 2px;
    }
  }
}

.input-section {
  margin-bottom: 32px;

  .input-label {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 12px;
  }

  .input-container {
    max-width: 600px;

    .style-input {
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.is-focus {
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;

  .el-button {
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.data-info {
  margin-bottom: 24px;

  :deep(.el-alert) {
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);

    .el-alert__title {
      font-weight: 500;
    }
  }
}

.review-button-container {
  text-align: center;
  margin-top: 32px;

  .el-button {
    border-radius: 8px;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
    }
  }
}

// Review结果展示区域样式
.review-results-section {
  margin-top: 40px;
  padding-top: 32px;
  border-top: 2px solid #f3f4f6;
}

// 产品信息卡片样式
.product-card {
  margin-bottom: 32px;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .product-info {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .product-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    :deep(.el-image) {
      width: 100%;
      height: 100%;
    }
  }

  .product-details {
    .product-title {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 12px 0;
    }

    .product-meta {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .meta-item {
        display: flex;
        align-items: center;

        .meta-label {
          font-weight: 500;
          color: #6b7280;
          min-width: 60px;
        }

        .meta-value {
          color: #374151;
          font-weight: 500;
        }
      }
    }
  }

  .product-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .el-button {
      border-radius: 6px;
      font-size: 14px;

      &:hover {
        transform: translateX(4px);
      }
    }
  }
}

// 分析区域样式
.analysis-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
    padding-left: 12px;
    border-left: 4px solid #6366f1;
  }

  .analysis-grid {
    .analysis-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      :deep(.el-card__header) {
        padding: 16px 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 1px solid #e2e8f0;
      }

      :deep(.el-card__body) {
        padding: 20px;
      }
    }

    .advantages-card {
      border-top: 3px solid #10b981;
    }

    .disadvantages-card {
      border-top: 3px solid #ef4444;
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .card-icon {
        font-size: 18px;

        &.success-icon {
          color: #10b981;
        }

        &.danger-icon {
          color: #ef4444;
          transform: rotate(180deg);
        }
      }

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .analysis-list {
      .analysis-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .item-label {
            font-weight: 500;
            color: #374151;
          }

          .item-percentage {
            font-weight: 600;
            color: #6366f1;
          }
        }

        .item-count {
          font-size: 12px;
          color: #6b7280;
          margin-top: 4px;
          text-align: right;
        }
      }
    }
  }
}

// Review表格区域样式
.review-table-section {
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
    padding-left: 12px;
    border-left: 4px solid #6366f1;
  }

  .table-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    :deep(.el-card__body) {
      padding: 0;
    }

    :deep(.vxe-table) {
      border-radius: 12px;
      overflow: hidden;

      .vxe-header--column {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        font-weight: 600;
        color: #374151;
      }

      .vxe-body--row {
        &:hover {
          background-color: #f8fafc;
        }
      }
    }

    .review-text {
      padding: 8px 0;
      line-height: 1.5;
      color: #374151;
      word-break: break-word;
    }

    .tags-container {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .tag-item {
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .voc-marking-container {
    padding: 16px;
  }

  .page-header .page-title {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }

  .product-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .analysis-grid {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .review-table-section {
    :deep(.vxe-table) {
      font-size: 12px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.review-results-section {
  animation: fadeInUp 0.6s ease-out;
}

.analysis-card {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.table-card {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
}
</style>
