<script setup lang="ts">
import type { CommentSimpleManageEditData, CommentSimpleManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeTablePropTypes, } from "vxe-table"
import { getEnableLabelOptionList, saveSimpleList, } from "@/apis/productQualityAnalysis"
import { DictSelect, } from "@/components/DictSelect"

const props = defineProps<{
  rows: CommentSimpleManagePageList.List
}>()

const emits = defineEmits<{
  (e: "refresh"): void
}>()

const visible = defineModel<boolean>("visible",)

const tableRef = useTemplateRef("tableRef",)
const tableData = ref<CommentSimpleManageEditData.Params>([],)
const isLoading = ref<boolean>(false,)

watch(() => props.rows, (val,) => {
  if (!val) {
    tableData.value = []
    return false
  }
  tableData.value = JSON.parse(JSON.stringify(val,),)
}, {
  immediate: true,
},)

// 标签数据
type OptionData = { label?: string, value: string, children?: { label: string, value: string }[] }[]
const firstTagOptions = ref<OptionData>([],)
getEnableLabelOptionList().then((res,) => {
  const arr: OptionData = []
  for (const key in (res?.data ?? {})) {
    arr.push({
      label: res?.data[key][0]?.firstLevelTagName,
      value: res?.data[key][0]?.firstLevelTagName ?? "",
      children: (res?.data[key] ?? []).map((item,) => {
        return {
          label: item.secondLevelTagName,
          value: item.secondLevelTagName,
        }
      },),
    },)
  }
  firstTagOptions.value = arr
},)
function getSecondTagOptions(firstTagName: string,): OptionData {
  return firstTagOptions.value.filter((item,) => {
    if (!firstTagName) {
      return false
    }
    return firstTagName === item.value
  },)
}

// 校验
const validRules = ref<VxeTablePropTypes.EditRules<CommentSimpleManageEditData.Row>>({
  site: [
    { required: true, message: "必须填写", },
  ],
  rating: [
    { required: true, message: "必须填写", },
  ],
  commentId: [
    { required: true, message: "必须填写", },
  ],
  commentText: [
    { required: true, message: "必须填写", },
  ],
  commentDate: [
    { required: true, message: "必须填写", },
  ],
  exampleType: [
    { required: true, message: "必须填写", },
  ],
  suggestionTag: [
    { required: true, message: "必须填写", },
  ],
  tagCategory: [
    { required: true, message: "必须填写", },
  ],
},)
async function fullValid() {
  const $table = tableRef.value
  if (!tableData.value?.length) {
    ElMessage.warning("至少需要一条数据",)
    return false
  }
  if ($table) {
    const errMap = await $table.validate(true,)
    return !errMap
  }
  return false
}

function onClose(done: () => void,) {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  done()
}
function onCancel() {
  if (isLoading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  visible.value = false
}
function onRefresh() {
  visible.value = false
  emits("refresh",)
}
async function onConfirm() {
  const checkResult = await fullValid()
  if (!checkResult) {
    return false
  }
  isLoading.value = true
  saveSimpleList(tableData.value,)
    .then((res,) => {
      if (res?.success) {
        ElMessage.success("保存成功",)
        onRefresh()
      }
    },)
    .finally(() => {
      isLoading.value = false
    },)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="批量修改 样本信息"
    top="15vh"
    width="1100px"
    append-to-body
  >
    <div class="table-wrapper">
      <VxeTable
        ref="tableRef"
        :border="true"
        :cell-config="{ height: 80 }"
        :data="tableData"
        :edit-config="{ mode: 'row', trigger: 'click', autoClear: false, showStatus: true }"
        :edit-rules="validRules"
        :show-overflow="true"
        max-height="500px"
      >
        <VxeColumn type="seq" width="55" />
        <VxeColumn
          :edit-render="{}"
          field="site"
          min-width="200"
          title="站点"
        >
          <template #edit="{ row }">
            <DictSelect
              v-model="row.site"
              class="!w-full"
              default-label="dictEnName"
              dict-code="VOC_REPORT_STATION"
              popper-class="vxe-table--ignore-clear"
              clearable
              filterable
            />
          </template>
          <template #default="{ row }">
            {{ row.site }}
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="commentId"
          min-width="200"
          title="评论ID"
        >
          <template #edit="{ row }">
            <VxeTextarea v-model="row.commentId" max-length="100" clearable />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{}"
          field="commentDate"
          min-width="200"
          title="评论日期"
        >
          <template #edit="{ row }">
            <ElDatePicker
              v-model="row.commentDate"
              placeholder="请选择"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </template>
          <template #default="{ row }">
            {{ row.commentDate }}
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="rating"
          min-width="200"
          title="评分"
        >
          <template #edit="{ row }">
            <ElInputNumber
              v-model="row.rating"
              :controls="false"
              :max="5"
              :min="0"
              :precision="1"
              clearable
            />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="commentTitle"
          min-width="200"
          title="评论标题"
        >
          <template #edit="{ row }">
            <VxeTextarea v-model="row.commentTitle" max-length="500" clearable />
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{ autoFocus: 'input' }"
          field="commentText"
          min-width="300"
          title="评论原文"
        >
          <template #edit="{ row }">
            <VxeTextarea v-model="row.commentText" max-length="2000" clearable />
          </template>
          <template #default="{ row }">
            <div style="word-break: break-all; white-space: pre-wrap;">
              <ElText line-clamp="4">
                {{ row.commentText }}
              </ElText>
            </div>
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{}"
          field="tagCategory"
          min-width="200"
          title="标签分类"
        >
          <template #edit="{ row }">
            <ElSelect
              v-model="row.tagCategory"
              clearable
              filterable
              @change="row.suggestionTag = null"
            >
              <ElOption
                v-for="item in firstTagOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </template>
          <template #default="{ row }">
            {{ row.tagCategory }}
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{}"
          field="suggestionTag"
          min-width="200"
          title="建议标签值"
        >
          <template #edit="{ row }">
            <ElSelect
              v-model="row.suggestionTag"
              clearable
              filterable
            >
              <ElOptionGroup v-for="group in getSecondTagOptions(row.tagCategory)" :key="group.value" :label="group.label">
                <ElOption
                  v-for="item in (group.children ?? [])"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElOptionGroup>
            </ElSelect>
          </template>
          <template #default="{ row }">
            {{ row.suggestionTag }}
          </template>
        </VxeColumn>
        <VxeColumn
          :edit-render="{}"
          field="exampleType"
          min-width="100"
          title="正 / 反例"
        >
          <template #edit="{ row }">
            <ElSelect v-model="row.exampleType">
              <ElOption :value="0" label="正例" />
              <ElOption :value="1" label="反例" />
            </ElSelect>
          </template>
          <template #default="{ row }">
            {{ row.exampleType === 0 ? '正例' : '反例' }}
          </template>
        </VxeColumn>
      </VxeTable>
    </div>
    <template #footer>
      <div class="footer-actions">
        <ElButton :disabled="isLoading" @click="onCancel">
          关闭
        </ElButton>
        <ElButton
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onConfirm"
        >
          保存
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
