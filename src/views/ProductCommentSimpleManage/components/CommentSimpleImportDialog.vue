<script setup lang="ts">
import type { CommentSimpleManageEditData, } from "@/apis/productQualityAnalysis/types.ts"
import type { UploadRawFile, } from "element-plus"
import { importCommentSimpleByUrl, submitCommentSimpleList, } from "@/apis/productQualityAnalysis"
import { useCopyFile, } from "@/hooks/useCopyFile.ts"
import { ajaxUpload, } from "@/utils/uploadFile.ts"
import { Download, Upload, Warning, } from "@element-plus/icons-vue"
import { utils, writeFile, } from "xlsx"

const emits = defineEmits<{
  (e: "refresh"): void
}>()

const tableRef = useTemplateRef("tableRef",)
const uploadRef = useTemplateRef("uploadRef",)
const visible = defineModel<boolean>("visible",)
const isLoading = ref<boolean>(false,)
const tableData = ref<CommentSimpleManageEditData.ImportRow[]>([],)

const failTableData = computed<CommentSimpleManageEditData.ImportRow[]>(() => {
  return tableData.value.filter((item,) => {
    return item.success === 1
  },)
},)

const { onInit, onDestroy, fileList, } = useCopyFile()

onMounted(() => {
  onInit && onInit()
},)

onUnmounted(() => {
  onDestroy && onDestroy()
},)

watch(() => fileList.value.length, (val,) => {
  if (val === 0) {
    return false
  }
  const file = fileList.value[0]
  uploadRef.value?.handleStart(file as UploadRawFile,)
  uploadRef.value?.submit()
},)

// 步骤
const activeStep = ref<number>(1,)
const isFirst = computed<boolean>(() => {
  return activeStep.value === 1
},)
const isLast = computed<boolean>(() => {
  return activeStep.value === 2
},)

// 文件上传
const accepts = [".xls", ".xlsx",]
const isUploading = ref<boolean>(false,)
const fileName = ref<string>("",)
function onBeforeUpload(file: UploadRawFile,): boolean {
  const name = file.name
  const suffix = name.split(".",).pop()
  if (!suffix || !accepts.includes(`.${suffix.toLowerCase()}`,)) {
    ElMessage.warning(`请上传正确的文件格式：${accepts.join(",",)}`,)
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error("请上传小于 10M 的文件!",)
  }
  return isLt10M
}
function onProgress() {
  isUploading.value = true
}
function onError(e,) {
  console.error(e,)
  isUploading.value = false
}
function onSuccess(res,) {
  if (!res?.datas) {
    isUploading.value = false
    ElMessage.error("数据上传OSS失败",)
    return false
  }
  importCommentSimpleByUrl({ fileUrl: res.datas.objectName, fileName: res.datas.originFileName, },)
    .then((newRes,) => {
      if (newRes?.success) {
        fileName.value = res.datas.originFileName
        tableData.value = newRes.data ?? []
      }
    },)
    .finally(() => {
      isUploading.value = false
    },)
}

// 下一步
function onNext() {
  if (!fileName.value) {
    ElMessage.warning("请先上传文件",)
    return false
  }
  activeStep.value++
}

// 下载异常
function onDownloadFail() {
  const columns = tableRef.value?.getColumns()
  if (!columns?.length) {
    ElMessage.warning("读取表格信息失败",)
    return false
  }
  const data = failTableData.value.map((item,) => {
    const obj = {}
    columns.forEach((column,) => {
      obj[column.title] = item[column.field]
    },)
    return obj
  },)
  const workBook = utils.book_new()
  const workSheet = utils.json_to_sheet(data,)
  utils.book_append_sheet(workBook, workSheet,)
  writeFile(workBook, `评论样本导入异常错误信息.xlsx`, {
    bookType: "xlsx",
  },)
}

// 关闭
function onClose(done: () => void,) {
  if (isLoading.value || isUploading.value) {
    ElMessage.warning("数据处理中，请完成后再关闭",)
    return false
  }
  tableData.value = []
  fileName.value = ""
  activeStep.value = 1
  done()
}

function onCancel() {
  tableData.value = []
  fileName.value = ""
  activeStep.value = 1
  visible.value = false
}

// 提交
const process = ref<number>(0,)
function loadingProgress() {
  const val = process.value
  if (isLoading.value) {
    const temp = val + Math.random() * 10 >> 0
    process.value = temp > 98 ? 98 : temp
    setTimeout(() => {
      loadingProgress()
    }, 30,)
    return false
  }
  process.value = 100
}
function onSubmit() {
  if (failTableData.value.length) {
    ElMessage.warning("存在错误的导入数据，请纠正后重新导入",)
    return false
  }
  isLoading.value = true
  loadingProgress()
  submitCommentSimpleList({ reqList: tableData.value, },)
    .then((res,) => {
      if (res?.success) {
        setTimeout(() => {
          ElMessage.success("导入成功",)
          isLoading.value = false
          setTimeout(() => {
            onCancel()
            emits("refresh",)
          }, 300,)
        }, 1000,)
      } else {
        ElMessage.warning("导入失败",)
        isLoading.value = false
      }
    },)
    .catch(() => {
      ElMessage.warning("导入失败",)
      isLoading.value = false
    },)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :before-close="onClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="!isLoading"
    :title="isLoading ? '' : '评论样本导入'"
    :width="isLoading ? '700px' : '86%'"
    top="15vh"
    append-to-body
  >
    <template v-if="!isLoading" #default>
      <div class="step-wrapper">
        <ElSteps :active="activeStep" style="width: 100%" align-center>
          <ElStep title="上传文件" />
          <ElStep title="导入数据" />
        </ElSteps>
      </div>
      <div v-if="activeStep === 1" v-loading="isUploading" class="upload-wrapper">
        <ElUpload
          ref="uploadRef"
          :accept="accepts.join(',')"
          :before-upload="onBeforeUpload"
          :disabled="isUploading"
          :http-request="ajaxUpload"
          :on-error="onError"
          :on-progress="onProgress"
          :on-success="onSuccess"
          :show-file-list="false"
          action="#"
          class="upload-demo"
          drag
        >
          <div class="upload-content">
            <ElButton type="primary">
              上传文件
            </ElButton>
            <div v-if="fileName" style="color: #67c23a; margin-top: 8px">
              √ {{ fileName }}
            </div>
            <div class="tips" @click.stop>
              <span>支持.xls或.xlsx格式，文件大小不超过10M，最多支持导入3000条数据；
                或者你也可以先下载</span><ElLink
                href="https://mmt-sz.oss-cn-shenzhen.aliyuncs.com/44aad42f5f6243189c231fa46a197688/8bf464a09deb401e81d4846dd43991e7.xlsx"
                style="padding: 0; line-height: 1;margin-left: 4px; margin-top: -2px"
                target="_blank"
                type="primary"
              >
                导入模版
              </ElLink>
            </div>
          </div>
        </ElUpload>

        <div class="extra-tips">
          <ElIcon style="color: #FF9900; font-size: 32px">
            <Warning />
          </ElIcon>
          <div class="extra-tips-text">
            <div class="extra-tips-title">
              特别提示
            </div>
            <div class="extra-tips-content">
              导入过程中如发现个别数据校验不通过，则全量回滚修正后再重新操作导入
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeStep === 2" class="table-wrapper">
        <div class="import-message">
          <div class="success-item">
            <span class="success-text">{{ tableData.length - failTableData.length }}</span>
            <span class="tips-text">正常数量</span>
          </div>
          <div class="divider" />
          <div class="fail-item">
            <span class="fail-text">
              {{ failTableData.length }}
              <ElButton
                v-if="failTableData.length > 0"
                :icon="Download"
                class="download-btn"
                type="text"
                @click="onDownloadFail"
              >
                下载异常数据详情提示
              </ElButton>
            </span>
            <span class="tips-text">异常数量</span>
          </div>
        </div>
        <VxeTable
          ref="tableRef"
          :border="true"
          :cell-config="{ height: 120 }"
          :data="tableData"
          :header-cell-config="{ height: 48 }"
          :show-overflow="false"
          max-height="500px"
        >
          <VxeColumn
            field="errorMsg"
            min-width="200"
            title="错误原因"
          >
            <template #default="{ row }">
              <span style="color: red">{{ row.errorMsg }}</span>
            </template>
          </VxeColumn>
          <VxeColumn
            field="site"
            min-width="100"
            title="站点"
          />
          <VxeColumn
            field="commentId"
            min-width="180"
            title="评论ID"
          />
          <VxeColumn
            field="commentDate"
            min-width="120"
            title="评论日期"
          />
          <VxeColumn
            field="rating"
            min-width="100"
            title="评分"
          >
            <template #default="{ row }">
              {{ row.rating ?? '-' }} / 5.0
            </template>
          </VxeColumn>
          <VxeColumn
            field="commentTitle"
            min-width="180"
            title="评论标题"
          />
          <VxeColumn
            field="commentText"
            min-width="180"
            title="评论原文"
          >
            <template #default="{ row }">
              <div style="word-break: break-all; white-space: pre-wrap;">
                <ElText line-clamp="4">
                  {{ row.commentText }}
                </ElText>
              </div>
            </template>
          </VxeColumn>
          <VxeColumn
            field="suggestionTag"
            min-width="120"
            title="建议标签值"
          />
          <VxeColumn
            field="tagCategory"
            min-width="120"
            title="标签分类"
          />
          <VxeColumn
            field="exampleTypeName"
            min-width="120"
            title="正/反例"
          >
            <template #default="{ row }">
              <ElTag v-if="row.exampleTypeName" :type="row.exampleType === 0 ? 'success' : 'danger'">
                {{ row.exampleTypeName }}
              </ElTag>
            </template>
          </VxeColumn>
          <VxeColumn
            v-if="false"
            field="aiExampleTypeName"
            min-width="120"
            title="AI正/反例"
          >
            <template #default="{ row }">
              <ElTag v-if="row.aiExampleTypeName" :type="row.aiExampleType === 0 ? 'success' : 'danger'">
                {{ row.aiExampleTypeName }}
              </ElTag>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
    </template>
    <template v-else #default>
      <div class="loading-wrapper">
        <div class="loading-icon">
          <div class="loading-icon-inner">
            <ElIcon color="#67C23A" size="32px">
              <Upload />
            </ElIcon>
          </div>
        </div>
        <div class="loading-text">
          数据导入中
        </div>
        <div class="loading-process">
          <ElProgress
            :percentage="process"
            :stroke-width="12"
            :text-inside="true"
            status="success"
          >
            <span />
          </ElProgress>
        </div>
      </div>
    </template>
    <template v-if="!isLoading" #footer>
      <div class="footer-actions">
        <ElButton v-if="!isFirst" :disabled="isLoading" @click="activeStep--">
          上一步
        </ElButton>
        <ElButton
          v-if="!isLast"
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onNext"
        >
          下一步
        </ElButton>
        <ElButton
          v-if="isLast"
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onSubmit"
        >
          提交
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
.step-wrapper {
  position: relative;
  max-width: 900px;
  margin: 0 auto 24px;
}

.tips {
  margin: 12px auto 0;
  max-width: 400px;
  text-align: center;
}

.extra-tips {
  position: relative;
  padding: 12px;
  border: 1px solid #FF9900;
  border-radius: 8px;
  background-color: #FCF6EB;
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.extra-tips-text {
  position: relative;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: #646464;
}

.extra-tips-title {
  font-weight: 700;
}

.footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.import-message {
  background-color: #F7F7F7;
  height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.success-item,.fail-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  align-items: center;
}

.tips-text {
  color: #646464;
  font-size: 12px;
}

.success-text {
  color: #67C23A;
  font-size: 24px;
  font-weight: 700;
}

.fail-text {
  color: #F56C6C;
  font-size: 24px;
  font-weight: 700;
  position: relative;
}

.divider {
  width: 1px;
  background: #DCDFE6;
  height: 40px;
}

.download-btn {
  position: absolute;
  transform: translate(12px, -2px);
}

.loading-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  padding-bottom: 40px;
}

.loading-icon {
  width: 120px;
  height: 120px;
  background-color: #67C23A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  .loading-icon-inner {
    width: 72px;
    height: 72px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.loading-text {
  font-size: 18px;
  text-align: center;
  font-weight: 700;
  color: #646464;
}

.loading-process {
  width: 90%;
}
</style>
