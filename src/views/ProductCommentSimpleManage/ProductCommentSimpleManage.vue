<script setup lang="ts">
import type { CommentSimpleManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeGridProps, VxeTablePropTypes, } from "vxe-table"
import { getCommentSimpleManageList, } from "@/apis/productQualityAnalysis"
import { ApiSelect, } from "@/components/ApiSelect"
import { ContentWrap, } from "@/components/ContentWrap"
import { DictSelect, } from "@/components/DictSelect"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { trackEvent, } from "@/utils/monitor.ts"
import { ref, } from "vue"
import { TableColumn, } from "./const.ts"

// 查询初始化
const formData = reactive<CommentSimpleManagePageList.Params>({
  siteList: [],
  commentDate: [],
  suggestionTagList: [],
  tagCategoryList: [],
},)
const rules = computed(() => {
  return {}
},)

// 标签数据
const apiConfig = reactive({
  api: () => Promise.resolve(),
  config: {
    label: "name",
    value: "id",
    children: "child",
  },
},)

const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getCommentSimpleManageList,
  formData,
  resetWithSearch: true,
  formatParams: (formData,) => {
    let [commentDateStart, commentDateEnd,] = ["", "",]
    if (formData?.commentDate?.length) {
      commentDateStart = formData.commentDate[0]
      commentDateEnd = formData.commentDate[1]
    }
    return {
      ...formData,
      commentDateStart,
      commentDateEnd,
    }
  },
},)

// 表格配置
const tableOptions = computed(() => ({
  columns: TableColumn,
  maxHeight: maxHeight.value - 50,
  minHeight: 280,
  border: "inner",
  scrollX: {
    enabled: true,
    gt: 20,
  },
  scrollY: {
    enabled: true,
    gt: 20,
  },
  loading: loading.value,
  data: (tableData.value) as CommentSimpleManagePageList.Row[],
} as VxeGridProps<CommentSimpleManagePageList.Row>),)

// 排序
interface RowVO {
  site: string
  commentDate: string
}
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  multiple: true,
},)
function handleSortChange({ order, property, },) {
  if (order) {
    tableData.value.sort((a, b,) => {
      if (a[property] < b[property]) {
        return order === "asc" ? -1 : 1
      }
      if (a[property] > b[property]) {
        return order === "asc" ? 1 : -1
      }
      return 0
    },)
  }
}

// 修改
const editVisible = ref<boolean>(false,)
function onEdit() {
  trackEvent("Edit", "评论样本管理页面批量编辑",)
  editVisible.value = true
}

// 删除
const deleteLoading = ref<boolean>(false,)
function onDelete(row,) {
  trackEvent("Delete", "评论样本管理页面删除数据",)
  console.error(row,)
}
</script>

<template>
  <ContentWrap>
    <div v-if="hasPermission('productCommentManage:search')" class="search">
      <LayoutForm
        ref="formRef"
        :loading="loading"
        :model="formData"
        :rules="rules"
        :span="6"
        class="customer-form"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem label="站点" prop="siteCodeList">
          <DictSelect
            v-model="formData.siteList"
            class="!w-full"
            default-label="dictEnName"
            dict-code="VOC_REPORT_STATION"
            popper-class="vxe-table--ignore-clear"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="评论日期" prop="commentDate">
          <ElDatePicker
            v-model="formData.commentDate"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始时间"
            style="width: 100%"
            type="daterange"
            value-format="YYYY-MM-DD"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="标签分类" prop="tagCategoryList">
          <ApiSelect
            v-model="formData.tagCategoryList"
            :api-config="apiConfig"
            :params="{}"
            clearable
            collapse-tags
            filterable
            immediate
            multiple
          />
        </ElFormItem>
        <ElFormItem label="标签值" prop="suggestionTagList">
          <ApiSelect
            v-model="formData.suggestionTagList"
            :api-config="apiConfig"
            :params="{}"
            clearable
            collapse-tags
            filterable
            immediate
            multiple
          />
        </ElFormItem>
      </LayoutForm>
    </div>
    <div class="operation">
      <ElRow :span="24" justify="space-between">
        <ElCol :span="12">
          <ElButton
            v-hasPermi="['productCommentSimpleManage:import']"
            type="primary"
          >
            批量导入
          </ElButton>
          <ElButton
            v-hasPermi="['productCommentSimpleManage:edit']"
            type="primary"
          >
            批量修改
          </ElButton>
        </ElCol>
      </ElRow>
    </div>
    <div v-loading="loading" class="content">
      <VxeGrid
        ref="tableRef"
        v-bind="tableOptions"
        :cell-config="{ height: 90 }"
        :header-cell-config="{ height: 48 }"
        :sort-config="sortConfig"
        @sort-change="handleSortChange"
      >
        <template #commentText="{ row }">
          <div style="word-break: break-all; white-space: pre-wrap;">
            <ElText line-clamp="4">
              {{ row.commentText }}
            </ElText>
          </div>
        </template>
        <template #operation="{ row }">
          <ElButton v-hasPermi="['productCommentSimpleManage:delete']" type="text" @click="onDelete(row)">
            删除
          </ElButton>
        </template>
      </VxeGrid>
      <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
    </div>
  </ContentWrap>
</template>

<style scoped lang="scss">
.operation{
  margin-bottom:10px;
}
</style>
