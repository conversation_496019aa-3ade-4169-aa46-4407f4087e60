<script setup lang="ts">
import type { CommentSimpleManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeGridProps, VxeTablePropTypes, } from "vxe-table"
import { deleteCommentSimpleById, getCommentSimpleManageList, getLabelOptionList, } from "@/apis/productQualityAnalysis"
import { ContentWrap, } from "@/components/ContentWrap"
import { DictSelect, } from "@/components/DictSelect"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { trackEvent, } from "@/utils/monitor.ts"
import CommentSimpleEditDialog from "@/views/ProductCommentSimpleManage/components/CommentSimpleEditDialog.vue"
import CommentSimpleImportDialog from "@/views/ProductCommentSimpleManage/components/CommentSimpleImportDialog.vue"
import { ref, } from "vue"
import { TableColumn, } from "./const.ts"

// 查询初始化
const formData = reactive<CommentSimpleManagePageList.Params>({
  siteList: [],
  commentDate: [],
  suggestionTagList: [],
  tagCategoryList: [],
},)
const rules = computed(() => {
  return {}
},)

// 标签数据
type OptionData = { label?: string, value: string, children?: { label: string, value: string }[] }[]
const firstTagOptions = ref<OptionData>([],)
getLabelOptionList().then((res,) => {
  const arr: OptionData = []
  for (const key in (res?.data ?? {})) {
    arr.push({
      label: res?.data[key][0]?.firstLevelTagName,
      value: res?.data[key][0]?.firstLevelTagName ?? "",
      children: (res?.data[key] ?? []).map((item,) => {
        return {
          label: item.secondLevelTagName,
          value: item.secondLevelTagName,
        }
      },),
    },)
  }
  firstTagOptions.value = arr
},)
const secondTagOptions = computed<OptionData>(() => {
  return firstTagOptions.value.filter((item,) => {
    if (!formData.tagCategoryList?.length) {
      return false
    }
    return formData.tagCategoryList.includes(item.value ?? "",)
  },)
},)

const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getCommentSimpleManageList,
  formData,
  resetWithSearch: true,
  defaultPageSize: 100,
  formatParams: (formData,) => {
    let [commentDateStart, commentDateEnd,] = ["", "",]
    if (formData?.commentDate?.length) {
      commentDateStart = formData.commentDate[0]
      commentDateEnd = formData.commentDate[1]
    }
    return {
      ...formData,
      commentDateStart,
      commentDateEnd,
    }
  },
},)

// 表格配置
const tableOptions = computed(() => ({
  columns: TableColumn,
  maxHeight: maxHeight.value - 50,
  minHeight: 280,
  border: "inner",
  scrollX: {
    enabled: true,
    gt: 20,
  },
  scrollY: {
    enabled: true,
    gt: 20,
  },
  loading: loading.value,
  data: (tableData.value) as CommentSimpleManagePageList.Row[],
} as VxeGridProps<CommentSimpleManagePageList.Row>),)

// 排序
interface RowVO {
  site: string
  commentDate: string
}
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  multiple: true,
},)
function handleSortChange({ order, property, },) {
  if (order) {
    tableData.value.sort((a, b,) => {
      if (a[property] < b[property]) {
        return order === "asc" ? -1 : 1
      }
      if (a[property] > b[property]) {
        return order === "asc" ? 1 : -1
      }
      return 0
    },)
  }
}

// 修改
// 数据检测
function validData(): boolean {
  const selected: CommentSimpleManagePageList.Row[] | undefined
      = tableRef.value?.getCheckboxRecords()
  if (!selected?.length) {
    ElMessage.warning("请先勾选数据后再进行操作",)
    return false
  }
  return true
}
const checkedData = ref<CommentSimpleManagePageList.Row[]>([],)
const editVisible = ref<boolean>(false,)
function onEdit() {
  if (!validData()) {
    return false
  }
  checkedData.value = tableRef.value?.getCheckboxRecords() ?? []
  if (checkedData.value.length === 0) {
    ElMessage.warning("没有获取到相应数据",)
    return false
  }
  trackEvent("Edit", "评论样本管理页面批量编辑",)
  editVisible.value = true
}

// 删除
const deleteLoading = ref<boolean>(false,)
function onDelete(row,) {
  ElMessageBox.confirm("是否确认删除本条数据？", "注意", {
    type: "warning",
  },).then(() => {
    trackEvent("Delete", "评论样本管理页面删除数据",)
    deleteLoading.value = true
    deleteCommentSimpleById(row.id,).then((res,) => {
      if (res?.success) {
        ElMessage.success("删除成功",)
        handleReset()
      }
    },).finally(() => {
      deleteLoading.value = false
    },)
  },).catch(() => {},)
}

// 导入
const importVisible = ref<boolean>(false,)
function onImport() {
  trackEvent("Import", "评论样本管理页面导入数据",)
  importVisible.value = true
}
</script>

<template>
  <ContentWrap>
    <div v-if="hasPermission('productCommentSimpleManage:search')" class="search">
      <LayoutForm
        ref="formRef"
        :loading="loading"
        :model="formData"
        :rules="rules"
        :span="6"
        class="customer-form"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem label="站点" prop="siteCodeList">
          <DictSelect
            v-model="formData.siteList"
            class="!w-full"
            default-label="dictEnName"
            dict-code="VOC_REPORT_STATION"
            popper-class="vxe-table--ignore-clear"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="评论日期" prop="commentDate">
          <ElDatePicker
            v-model="formData.commentDate"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始时间"
            style="width: 100%"
            type="daterange"
            value-format="YYYY-MM-DD"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="标签分类" prop="tagCategoryList">
          <ElSelect
            v-model="formData.tagCategoryList"
            clearable
            collapse-tags
            filterable
            multiple
            @change="formData.suggestionTagList = []"
          >
            <ElOption
              v-for="item in firstTagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="标签值" prop="suggestionTagList">
          <ElSelect
            v-model="formData.suggestionTagList"
            clearable
            collapse-tags
            filterable
            multiple
          >
            <ElOptionGroup v-for="group in secondTagOptions" :key="group.value" :label="group.label">
              <ElOption
                v-for="item in (group.children ?? [])"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElOptionGroup>
          </ElSelect>
        </ElFormItem>
      </LayoutForm>
    </div>
    <div class="operation">
      <ElRow :span="24" justify="space-between">
        <ElCol :span="12">
          <ElButton
            v-hasPermi="['productCommentSimpleManage:import']"
            type="primary"
            @click="onImport"
          >
            批量导入
          </ElButton>
          <ElButton
            v-hasPermi="['productCommentSimpleManage:edit']"
            type="primary"
            @click="onEdit"
          >
            批量修改
          </ElButton>
        </ElCol>
      </ElRow>
    </div>
    <div v-loading="loading || deleteLoading" class="content">
      <VxeGrid
        ref="tableRef"
        v-bind="tableOptions"
        :cell-config="{ height: 90 }"
        :header-cell-config="{ height: 48 }"
        :sort-config="sortConfig"
        @sort-change="handleSortChange"
      >
        <template #commentText="{ row }">
          <div style="word-break: break-all; white-space: pre-wrap;">
            <ElText line-clamp="4">
              {{ row.commentText }}
            </ElText>
          </div>
        </template>
        <template #exampleTypeName="{ row }">
          <ElTag v-if="row.exampleTypeName" :type="row.exampleType === 0 ? 'success' : 'danger'">
            {{ row.exampleTypeName }}
          </ElTag>
        </template>
        <template #aiExampleTypeName="{ row }">
          <ElTag v-if="row.aiExampleTypeName" :type="row.aiExampleType === 0 ? 'success' : 'danger'">
            {{ row.aiExampleTypeName }}
          </ElTag>
        </template>
        <template #operation="{ row }">
          <ElButton
            v-hasPermi="['productCommentSimpleManage:delete']"
            :disabled="deleteLoading"
            style="color: red"
            type="text"
            @click="onDelete(row)"
          >
            删除
          </ElButton>
        </template>
      </VxeGrid>
      <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
    </div>

    <CommentSimpleEditDialog v-model:visible="editVisible" :rows="checkedData" @refresh="handleSearch" />

    <CommentSimpleImportDialog v-model:visible="importVisible" @refresh="handleSearch" />
  </ContentWrap>
</template>

<style scoped lang="scss">
.operation{
  margin-bottom:10px;
}
</style>
