import { ApiSelect, } from "@/components/ApiSelect"
import { DictSelect, } from "@/components/DictSelect"
import { ElCascader, } from "@/components/ElCascader"
import { queryCategoryTree, queryTagStructureBox, } from "@/views/Label/apis/Info.ts"

export const FormConfig = [
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "productCategoryNamesArr",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "props": {
        multiple: true,
      },
    },
  },
  {
    label: "人群",
    layout: DictSelect,
    field: "audienceList",
    props: {
      "multiple": true,
      "dictCode": "PLATFORM_PRODUCT_PEOPLE",
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
  },
  {
    "label": "一级标签",
    "layout": ApiSelect,
    "field": "firstLevelTagIdList",
    "props": {
      "multiple": true,
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
    "params": {
      tagType: "review",
      tagLevel: 1,
      parentIdList: [0,] as number[],
    },
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
  },
  {
    "label": "二级标签",
    "layout": ApiSelect,
    "field": "secondLevelTagIdList",
    "props": {
      "multiple": true,
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
    "params": {
      tagType: "review",
      tagLevel: 2,
      parentIdList: [0,] as number[],
    },
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
  },
  {
    "label": "三级标签",
    "layout": ApiSelect,
    "field": "thirdLevelTagIdList",
    "params": {
      tagType: "review",
      tagLevel: 3,
      parentIdList: [0,] as number[],
    },
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
    "props": {
      "multiple": true,
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
  },

]
export const tableColumn = [
  {
    type: "checkbox",
    fixed: "left",
    width: 80,
  },
  {
    type: "seq",
    title: "序号",
    width: 60,
    fixed: "left",
  },
  {
    title: "人群",
    field: "audience",
    sortable: true,
    width: 150,
  },
  {
    title: "MMT类目",
    field: "productCategoryName",
    cellStyle: { whiteSpace: "pre-line", wordBreak: "break-all", },
    width: 250,
  },
  {
    title: "一级标签/二级标签/三级标签",
    field: "firstLevelTagName",
    width: 200,
    sortable: true,
    slots: {
      default: "firstLevelTagName",
    },
  },
  {
    title: "标签值",
    field: "tagValue",
    minWidth: 250,
  },
  {
    title: "状态",
    field: "statusName",
    width: 80,
  },
  {
    title: "生效日期",
    field: "effectiveDate",
    sortable: true,
    minWidth: 120,
  },
  {
    title: "操作人",
    field: "modifyByName",
    minWidth: 120,
  },
  {
    title: "操作时间",
    field: "modifyTime",
    minWidth: 150,
  },
  {
    title: "操作",
    field: "operation",
    width: 120,
    fixed: "right",
    slots: {
      default: "operation",
    },
  },
]
export const InfoConfig = [
  {
    "label": "MMT类目",
    "width": "200",
    "layout": ApiSelect,
    "component": ElCascader,
    "field": "productCategoryNameList",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "params": {
      placeholder: "请选择类目",
    },
    "props": {
      clearable: true,
      span: 8,
    },
  },
  {
    "label": "一级标签",
    "width": "200",
    "layout": ApiSelect,
    "component": ElSelect,
    "field": "firstLevelTagId",
    "params": {
      tagType: "review",
      tagLevel: 1,
      placeholder: "请选择一级标签",
      parentIdList: [0,],
    },
    "props": {
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
  },
  {
    "label": "二级标签",
    "width": "200",
    "layout": ApiSelect,
    "field": "secondLevelTagId",
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
    "props": {
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
    "params": {
      tagType: "review",
      tagLevel: 2,
      placeholder: "请选择二级标签",
      parentIdList: [0,],
    },
  },
  {
    "label": "三级标签",
    "width": "200",
    "layout": ApiSelect,
    "field": "thirdLevelTagId",
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
    "props": {
      "collapse-tags": true,
      "filterable": true,
      "clearable": true,
    },
    "params": {
      placeholder: "请选择三级标签",
      tagType: "review",
      tagLevel: 3,
      parentIdList: [0,],
    },
  },
  {
    "label": "标签值",
    "layout": ElInputTag,
    "field": "tagValueList",
    "width": "400",
    "api-config": {
      api: queryTagStructureBox,
      config: {
        label: "name",
        value: "id",
        children: "child",
      },
    },
    "params": {
      placeholder: "请输入标签值",
    },
    "props": {
      height: 50,
      draggable: true,
    },
  },
]
