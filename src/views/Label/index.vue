<script setup lang="ts">
import type { LabelPageList, } from "@/views/Label/apis/List.ts"
import type { VxeColumnPropTypes, } from "vxe-pc-ui/types/components/column"
import type { VxeGridProps, VxeTablePropTypes, } from "vxe-table"
import { ContentWrap, } from "@/components/ContentWrap"
import { Icon, } from "@/components/Icon"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi"
import { LabelStatusEnum, } from "@/enums"
import { useHandleExport, } from "@/hooks/useExport.tsx"
import { useReportQuery, } from "@/hooks/useReportQuery"
import { trackDialogEvent, } from "@/utils/monitor.ts"
import { startOrBan, } from "@/views/Label/apis/Info.ts"
import { getLabelByPage, } from "@/views/Label/apis/List.ts"
import OperateDialog from "@/views/Label/components/OperateDialog.vue"
import { FormConfig, tableColumn, } from "@/views/Label/help"
import { ref, } from "vue"

defineOptions({
  name: "CustomLabel",
},)
const dialog = reactive<{
  visible: boolean
  idList: string[]
}>({
  visible: false,
  idList: [],

},)

const formData = reactive<LabelPageList.Params>({
  productCategoryNamesArr: [],
  audienceList: [],
  firstLevelTagIdList: [],
  secondLevelTagIdList: [],
  thirdLevelTagIdList: [],
},)
const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getLabelByPage,
  formData,
  formatParams: (formData,) => {
    return {
      ...formData,
      productCategoryNames: (formData?.productCategoryNamesArr ?? []).map(item => item.join("/",),),
    }
  },
},)
onActivated(handleSearch,)
const tableOptions = computed(() => ({
  columns: tableColumn,
  maxHeight: maxHeight.value - 50,
  minHeight: 280,
  scrollX: {
    enabled: true,
    gt: 20,
  },
  scrollY: {
    enabled: true,
    gt: 20,
  },
  loading: loading.value,
  data: tableData.value as LabelPageList.Row[],
} as VxeGridProps<LabelPageList.Row>),)

/**
 * 更新状态
 * @param row
 */
async function changeStatus(row: LabelPageList.Row,) {
  await startOrBan({
    updateStatusReqList: [
      {
        id: row.id,
        oldStatus: row.status,
        newStatus: row.status === LabelStatusEnum.START ? LabelStatusEnum.BAN : LabelStatusEnum.START,
      },
    ],
  },)
  ElMessage.success("状态更新成功",)
  handleSearch()
}
function handleAdd() {
  dialog.visible = true
  dialog.idList = []
  trackDialogEvent("标签结构页面批量新增",)
}
function handleEdit() {
  const selectRows: LabelPageList.List | undefined = unref(tableRef,)?.getCheckboxRecords()
  trackDialogEvent("标签结构页面批量修改",)
  if (selectRows && selectRows.length > 0) {
    dialog.idList = (selectRows || []).map(e => e.id,)
    dialog.visible = true
  } else {
    ElMessage.error("请选择一条数据",)
  }
}
// 在ApiSelect组件中增加级联处理逻辑
async function handleCascade(field: string,) {
  // 清除下级选项的值
  switch (field) {
    case "firstLevelTagIdList":
      formData.secondLevelTagIdList = []
      formData.thirdLevelTagIdList = []
      break
    case "secondLevelTagIdList":
      formData.thirdLevelTagIdList = []
      break
  }
}
// 修改formConfigComputed计算属性
const formConfigComputed = computed(() => {
  // 显式声明依赖项
  const { firstLevelTagIdList, secondLevelTagIdList, } = formData

  return FormConfig.map((item,) => {
    const newItem = { ...item, }

    switch (newItem.field) {
      case "secondLevelTagIdList":
        newItem.params = {
          ...newItem.params,
          tagType: newItem.params?.tagType as string,
          tagLevel: newItem.params?.tagLevel as number,
          parentIdList: firstLevelTagIdList as number[],
        }
        break
      case "thirdLevelTagIdList":
        newItem.params = {
          ...newItem.params,
          tagType: newItem.params?.tagType as string,
          tagLevel: newItem.params?.tagLevel as number,
          parentIdList: secondLevelTagIdList as number[],
        }
        break
    }

    return newItem
  },)
},)
const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()
function downloadAction() {
  const selected: LabelPageList.Row[] | undefined
      = tableRef.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected.length > 0) {
    reqParam = JSON.stringify({
      idList: selected.map((item: LabelPageList.Row,) => item.id,),
    },)
  } else {
    reqParam = JSON.stringify({
      ...formData,
      productCategoryNames: (formData?.productCategoryNamesArr ?? []).map(item => item.join("/",),),
    },)
  }
  exportFn({
    exportType: "tagStructure-export",
    reqParam,
  },)
}
interface RowVO {
  audience: string
  firstLevelTagName: string
  effectiveDate: Date
}
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  multiple: true,
},)
function handleSortChange(params: {
  order: string
  property: VxeColumnPropTypes.Field
},) {
  const { order, property, } = params
  if (order) {
    tableData.value.sort((a, b,) => {
      if (a[property] < b[property]) {
        return order === "asc" ? -1 : 1
      }
      if (a[property] > b[property]) {
        return order === "asc" ? 1 : -1
      }
      return 0
    },)
  }
}
// 添加调试watch
watch(formData, (newVal,) => {
  console.log("formData变更:", JSON.parse(JSON.stringify(newVal,),),)
}, { deep: true, },)
watch(formConfigComputed, (newConfig,) => {
  console.log("表单配置变更:", newConfig.map(c => ({
    field: c.field,
    params: c.params,
  }),),)
}, { immediate: true, },)
</script>

<template>
  <ContentWrap>
    <div v-if="hasPermission('label:search')" class="search">
      <LayoutForm
        ref="formRef"
        :descriptions="FormConfig"
        :loading="loading"
        :model="formData"
        :sort-config="sortConfig"
        :span="6"
        query-form
        @reset="handleReset"
        @search="handleSearch"
        @sort-change="handleSortChange"
      >
        <ElFormItem
          v-for="({ label, layout, field, props, ...other }, index) in formConfigComputed"
          :key="index"
          :label="label"
        >
          <component
            :is="layout as Component"
            v-model="formData[field as keyof LabelPageList.Params]"
            v-bind="{ ...other,
                      key: JSON.stringify(other.params),
                      ...props, // 级联处理
                      onChange: () => {
                        handleCascade(field)
                      } }"
          />
        </ElFormItem>
      </LayoutForm>
    </div>
    <div class="operation">
      <ElRow :span="24" justify="space-between">
        <ElCol :span="12">
          <ElButton
            v-hasPermi="['label:add']"
            type="primary"
            @click="handleAdd"
          >
            <Icon :size="20" icon="ep:plus" />
            批量新增
          </ElButton>
          <ElButton
            v-hasPermi="['label:edit']"
            type="primary"
            @click="handleEdit"
          >
            <Icon :size="20" icon="ep:edit" />
            批量修改
          </ElButton>
          <ElButton
            v-hasPermi="['label:loading']"
            :loading="exportLoading"
            type="primary"
            @click="downloadAction"
          >
            <Icon :size="20" icon="ep:download" />
            导出
          </ElButton>
        </ElCol>
      </ElRow>
    </div>
    <div v-loading="loading" class="content">
      <VxeGrid
        ref="tableRef"
        v-bind="tableOptions"
      >
        <template #operation="{ row }: {row:LabelPageList.Row}">
          <ElSwitch v-hasPermi="['voc:operate']" :model-value="row.status === LabelStatusEnum.START" @change="changeStatus(row)" />
        </template>
        <template #firstLevelTagName="{ row }: {row:LabelPageList.Row}">
          {{ row.firstLevelTagName }}{{ row.secondLevelTagName ? `/${row.secondLevelTagName}` : '' }}{{ row.thirdLevelTagName ? `/${row.thirdLevelTagName}` : '' }}
        </template>
      </VxeGrid>
      <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
    </div>
  </ContentWrap>
  <OperateDialog v-model="dialog.visible" v-bind="{ ...dialog }" @submit="handleSearch" />
</template>

<style scoped lang="scss">
  .operation{
    margin-bottom:10px;
  }
</style>
