<script setup lang="ts">
import type { LabelInfo, } from "@/views/Label/apis/Info.ts"
import type { ComponentPublicInstance, } from "vue"
import type { VxeTableDefines, VxeTableInstance, VxeTablePropTypes, } from "vxe-table"
import { InfoConfig, } from "@/views/Label/help.ts"
import {

  defineEmits,
  defineProps,
  nextTick,
  onMounted,
  ref,
  watch,
  withDefaults,
} from "vue"
// 定义组件方法接口
interface EditableTableExposed {
  validate: () => Promise<unknown> // 明确方法签名
}

const props = withDefaults(defineProps<{
  modelValue?: LabelInfo.Row[]
  idList?: string[]
}>(), {
  modelValue: () => [],

},)
const emit = defineEmits(["update:modelValue",],)
const tableRef = ref<VxeTableInstance>()
const eidtableTableRef = ref<HTMLElement | ComponentPublicInstance>()
const tableData = ref<LabelInfo.Row[]>([],)
const tableRules = ref<VxeTablePropTypes.EditRules>({
  productCategoryNameList: [
    {
      required: true,
      message: "请选择MMT类目",
      trigger: "change",
    },
  ],
  firstLevelTagId: [
    {
      required: true,
      message: "请选择一级标签",
      trigger: "change",
    },
  ],
  secondLevelTagId: [
    {
      required: true,
      message: "请选择二级标签",
      trigger: "change",
    },
  ],
  thirdLevelTagId: [
    {
      required: true,
      message: "请选择三级标签",
      trigger: "change",
    },
  ],
},)
watch(tableData, (newVal,) => {
  console.log("formData变更:", JSON.parse(JSON.stringify(newVal,),),)
}, { deep: true, },)

function addRow() {
  const newRow: LabelInfo.Row = {
    productCategoryNameList: [],
    firstLevelTagId: "",
    secondLevelTagId: "",
    thirdLevelTagId: "",
    tagValueList: [],
    productCategoryName: "",
    tagValue: "",
  }
  tableData.value.push(newRow,)
}
function removeSelectedRow() {
  const $table = tableRef.value
  if ($table) {
    const selectedRows = $table.getCheckboxRecords()
    tableData.value = tableData.value.filter(row => !selectedRows.includes(row,),)
  }
}

// 使用防抖的更新逻辑
let updateTimer: number
function emitUpdate(row: LabelInfo.Row, field: string, label: string | string[] | undefined,) {
  clearTimeout(updateTimer,)
  updateTimer = window.setTimeout(() => {
    // 确保先更新数据再验证
    if (tableRef.value) {
      // 使用正确的验证方法
      // 验证通过
      // 使用映射关系优化代码
      if (label && typeof label === "string") {
        const fieldNameMap: Record<string, keyof LabelInfo.Row> = {
          firstLevelTagId: "firstLevelTagName",
          secondLevelTagId: "secondLevelTagName",
          thirdLevelTagId: "thirdLevelTagName",
        }

        const nameField = fieldNameMap[field]
        if (nameField) {
          // 使用类型断言解决类型不匹配问题
          (row[nameField as keyof LabelInfo.Row] as any) = label
        }
      }
      emit("update:modelValue", tableData.value,)
    }
  }, 300,)
}
async function handleCascade(row: LabelInfo.Row, field: string, column: VxeTableDefines.ColumnInfo,) {
  const $table = tableRef.value
  if (!$table) {
    return
  }

  // 清除下级选项的值
  switch (field) {
    case "firstLevelTagId": // 当选择一级标签时，清除二级和三级标签
      row.secondLevelTagId = ""
      row.thirdLevelTagId = ""
      break
    case "secondLevelTagId": // 当选择二级标签时，清除三级标签
      row.thirdLevelTagId = ""
      break
  }
  // 使用正确的参数结构
  tableRef.value?.updateStatus({ row, column, },)
}
// 辅助方法
function getParentId(row: LabelInfo.Row, currentField: string,) {
  // 定义字段映射关系
  const fieldMap: Record<string, string> = {
    secondLevelTagId: "firstLevelTagId",
    thirdLevelTagId: "secondLevelTagId",
  }

  const parentField = fieldMap[currentField]
  if (!parentField) {
    return 0
  }

  // 获取父级ID
  const parentValue = row[parentField as keyof LabelInfo.Row]
  // 如果是字符串且有值，直接返回
  // 默认返回0
  return parentValue || 0
}

// 格式化预览单元格值
function formatCellValue(row: LabelInfo.Row, field: string,) {
  const value = row[field as keyof LabelInfo.Row]
  // 处理数组类型
  const levelMap = {
    firstLevelTagId: "firstLevelTagName",
    secondLevelTagId: "secondLevelTagName",
    thirdLevelTagId: "thirdLevelTagName",
  }
  if (Object.keys(levelMap,).includes(field,)) {
    return row[levelMap[field as keyof typeof levelMap]]
  }
  if (Array.isArray(value,)) {
    return field === "productCategoryNameList" ? value.join("/",) : value.join(",",)
  }

  // 处理对象类型
  if (value && typeof value === "object") {
    return JSON.stringify(value,)
  }

  // 处理空值
  if (value === undefined || value === null) {
    return ""
  }

  // 返回原始值
  return value
}
// 使用 watchEffect 自动追踪依赖
watchEffect(() => {
  // 使用类型断言解决类型不匹配问题
  tableData.value = props.modelValue as LabelInfo.Row[]
},)

// 添加手动验证方法
function validateTable() {
  return new Promise((resolve, reject,) => {
    if (tableRef.value) {
      tableRef.value.validate(true,).then((errMap,) => {
        if (errMap) {
          // 高亮显示错误单元格
          // tableRef.value?.updateStatus()
          reject(errMap,)
        } else {
          resolve(true,)
        }
      },).catch((err,) => {
        reject(err,)
      },)
    } else {
      resolve(true,)
    }
  },)
}
const { maxHeight, } = useMaxHeight({ targetRef: eidtableTableRef, },)

// 暴露验证方法
defineExpose<EditableTableExposed>({
  validate: validateTable,
},)

// 确保在组件挂载后初始化验证
onMounted(() => {
  // 初始化表格
  nextTick(() => {
    if (tableRef.value) {
      // 确保表格已经渲染完成
      tableRef.value.loadData(tableData.value,)
      // 初始验证所有行
      tableRef.value.validate()
    }
  },)
},)
</script>

<template>
  <div ref="eidtableTableRef" class="editable-table" style="height: 100%;">
    <VxeToolbar v-if="idList && idList.length <= 0">
      <template #buttons>
        <VxeButton @click="addRow">
          新增
        </VxeButton>
        <VxeButton @click="removeSelectedRow">
          删除
        </VxeButton>
      </template>
    </VxeToolbar>

    <VxeTable
      ref="tableRef"
      :checkbox-config="{ highlight: true }"
      :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell', autoClear: false, showStatus: true }"
      :edit-rules="tableRules"
      :max-height="maxHeight - 20"
      :row-config="{ height: 'auto' }"
      :scroll-x="{ enabled: true, gt: 5 }"
      :scroll-y="{ enabled: true, gt: 20 }"
      :valid-config="{ showMessage: true, autoPos: true, message: '请填写必填项', cellStyle: true }"
      keep-source
      show-overflow
    >
      <VxeColumn type="checkbox" width="60" />
      <VxeColumn
        v-for="({ label, width, layout, params, field, ...other }) in InfoConfig"
        :key="field"
        :edit-render="{ name: 'input', enabled: true }"
        :field="field"
        :title="label"
        :width="width"
        header-class-name="header-required"
      >
        <template #edit="{ row, rowIndex, column }">
          <component
            :is="layout"
            v-model="(tableData[rowIndex] as any)[field]"
            v-bind="{ ...other,
                      key: JSON.stringify({
                        rowIndex,
                        field,
                        parentId: getParentId(row, field),
                      }),
                      params: { ...params,
                                parentIdList: [getParentId(row, field)] },
                      ...other.props,
            }"
            @change="handleCascade(row, field, column)"
            @option-change="emitUpdate(row, field, $event?.label ?? '')"
          />
        </template>
        <template #default="{ row }">
          {{ formatCellValue(row, field) || params?.placeholder }}
        </template>
      </VxeColumn>
    </VxeTable>
  </div>
</template>

<style lang="scss" scoped>
/* 关键样式 */
:deep(.vxe-table){
  .vxe-body--column {
    white-space: pre-wrap !important;  /* 允许换行 */
    word-break: break-word !important; /* 强制断词 */
    line-height: 1.5; /* 行高倍数 */
    padding: 12px 10px; /* 适当增加内边距 */
  }

  /* 校验错误样式 - 预览状态 */
  .col__valid-error {
    background-color: rgba(255, 0, 0, 0.05);
    border: 1px solid #ff4d4f !important;
  }

  /* 校验错误样式 - 编辑状态 */
  .vxe-cell--valid-error {
    border: 1px solid #ff4d4f !important;
    box-shadow: 0 0 0 1px #ff4d4f !important;
  }

  /* 错误提示文字样式 */
  .vxe-form--item-valid {
    color: #ff4d4f;
    font-size: 12px;
  }
}

/* 行高自适应 */
:deep(.vxe-table--render-default){
  .vxe-table--body{
    height: auto !important;
  }
}
</style>
