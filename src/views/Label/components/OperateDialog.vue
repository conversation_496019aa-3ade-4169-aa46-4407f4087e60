<script setup lang="ts">
import type { LabelInfo, } from "@/views/Label/apis/Info.ts"
import { getDetailByIds, saveTagStructures, updateTagStructures, } from "@/views/Label/apis/Info.ts"

import { ElMessage, } from "element-plus"
import { defineEmits, defineProps, ref, } from "vue"
import EditableTable from "./EditableTable.vue"

interface FormData {
  baseInfo?: string
  tableData: LabelInfo.Row[]
}
type EditableTableInstance = InstanceType<typeof EditableTable> & {
  validate: () => Promise<boolean>
}
const props = defineProps<{
  modelValue: boolean
  idList: string[]
}>()
const emit = defineEmits(["update:modelValue", "submit",],)
const loading = ref<boolean>(false,)
const submitLoading = ref<boolean>(false,)
const editTableRef = ref<EditableTableInstance | null>(null,)
const formData = ref<FormData>({
  baseInfo: "",
  tableData: [],
},)
const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)
async function getTableList() {
  if (!props.idList || props.idList?.length <= 0) {
    formData.value.tableData = []
    return false
  }
  loading.value = true
  const result = await getDetailByIds({ idList: props.idList, },)
  if (result && result.success) {
    // 转数据
    const { data, } = result
    formData.value.tableData = data.map((item: LabelInfo.Row,) => {
      item.tagValueList = item.tagValue.split(",",)
      item.productCategoryNameList = item?.productCategoryName.split("/",)
      return item
    },)
  }
  loading.value = false
}
watch(() => dialogVisible.value, (val,) => {
  if (val) {
    getTableList()
  }
},)

// 提交数据
async function handleSubmit() {
  // 进行表格验证
  const valid = await editTableRef.value?.validate()
  if (!valid) {
    ElMessage.error("请填写必填项",)
    return
  }
  if (formData.value.tableData.length < 1) {
    ElMessage.error("请填写数据",)
    return
  }
  // 转数据
  const tableDatas = formData.value.tableData.map((item: LabelInfo.Row,) => {
    item.productCategoryName = item?.productCategoryNameList.join("/",)
    return item
  },)
  let result: any
  submitLoading.value = true
  try {
    // 修改
    if (props.idList.length > 0) {
      result = await updateTagStructures(tableDatas,)
    } else {
      result = await saveTagStructures(tableDatas,)
    }
    submitLoading.value = false
    if (result && result.success) {
      ElMessage.success("操作成功",)
      dialogVisible.value = false
      emit("submit",)
    } else {
      ElMessage.error("操作失败",)
    }
  } catch {
    submitLoading.value = false
  }
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="idList.length > 0 ? '修改' : '新增'"
    width="60%"
    @close="dialogVisible = false"
  >
    <!-- 其他表单内容 -->
    <div v-loading="loading" class="dialog-content">
      <!-- 可编辑表格 -->
      <EditableTable
        ref="editTableRef"
        v-model="formData.tableData"
        :id-list="idList"
        class="editable-table-wrapper"
      />
    </div>

    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确认</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<style scoped>
.dialog-content {
  height:60vh;
  min-height:200px;
}
</style>
