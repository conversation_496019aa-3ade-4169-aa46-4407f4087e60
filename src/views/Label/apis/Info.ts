import { get, postJSON, } from "@/utils/fetch.ts"

export namespace LabelInfo {
  export interface Params {
    idList: string[]
  }
  export interface Row {
    id?: string
    productCategoryName: string
    firstLevelTagId: string
    firstLevelTagName?: string
    secondLevelTagId: string
    secondLevelTagName?: string
    thirdLevelTagId: string
    thirdLevelTagName?: string
    tagValueList: string[]
    productCategoryNameList: string[]
    status?: string
    tagValue: string
    statusName?: string
  }
  export type List = Row[]
  export type Response = NewResponseData<List>
  export type Request = Params
}
export interface editStatus {
  id: string
  oldStatus: string
  newStatus: string
}
export interface StatusResponse {
  updateStatusReqList: editStatus[]
}
export interface labelStructure {
  tagType: string
  tagLevel: number | string
  parentIdList: (number | string)[]
}
/**
 * 获取table详情
 */
export function getDetailByIds(params: LabelInfo.Request,) {
  return get<LabelInfo.Response>({
    url: `/tagStructure/queryDetailByIds`,
    params,
  },)
}

/**
 * 批量新增
 * @param formData
 */
export function saveTagStructures(formData: LabelInfo.List,) {
  return postJSON<NewBasicResponseData>({
    url: `/tagStructure/saveTagStructures`,
    data: formData,
  },)
}

/**
 * 批量删除
 */
export function delTagStructures(params: LabelInfo.Params,) {
  return postJSON<NewBasicResponseData>({
    url: `/tagStructure/delTagStructures`,
    params,
  },)
}

/**
 * 批量新增
 */
export function updateTagStructures(formData: LabelInfo.List,) {
  return postJSON<NewBasicResponseData>({
    url: `/tagStructure/updateTagStructures`,
    data: formData,
  },)
}
/**
 * 启用或禁用
 *
 */
export function startOrBan(formData: StatusResponse,) {
  return postJSON<NewBasicResponseData>({
    url: `/tagStructure/startOrBan`,
    data: formData,
  },)
}

/**
 * 标签结构查询下拉框
 */
export function queryTagStructureBox(formData: labelStructure,) {
  if (formData.parentIdList.length > 0) {
    return postJSON<NewBasicResponseData>({
      url: `/tagStructure/queryTagStructureBox`,
      data: formData,
    },)
  } else {
    return Promise.resolve()
  }
}
export function queryCategoryTree() {
  return get<NewResponseData<any[]>>({
    url: `/vocReport/category/tree`,
  },)
}
