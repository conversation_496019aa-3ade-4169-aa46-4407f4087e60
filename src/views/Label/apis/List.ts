import { postJSON, } from "@/utils/fetch"
/**
 * 标签结构相关
 */
export namespace LabelPageList {
  export interface Params {
    productCategoryNamesArr: string[][]
    audienceList: string[]
    firstLevelTagIdList: (number | string)[]
    secondLevelTagIdList: (number | string)[]
    thirdLevelTagIdList: (number | string)[]
  }
  export interface Row {
    id: string
    audience?: number
    productCategoryName?: string
    firstLevelTagName?: string
    secondLevelTagName?: string
    thirdLevelTagName?: string
    status: string
    tagValue?: string
    statusName?: string
    effectiveDate?: string
    modifyByName?: string
    modifyTime?: string
  }
  export type List = Row[]
  export type Response = NewPageResponseData<Row>
  export type Request = Params & PageParams
}
// 获取标签结构
export function getLabelByPage(data: LabelPageList.Request,) {
  return postJSON<LabelPageList.Response>({
    url: `/tagStructure/queryTagPage`,
    data,
  },)
}
