<script setup lang="ts">
import type { GroupData, TagData, } from "@/components/TagSelect/src/TagSelect.vue"
import { getEnableLabelOptionList, saveTagInfo, } from "@/apis/productQualityAnalysis"
import { TagSelect, } from "@/components/TagSelect"
import { STYLE_LIST, } from "@/components/TagSelect/src/const.ts"
import { useKeyIndex, } from "@/views/ProductCommentManage/hooks/useKeyIndex.ts"
import { Plus, } from "@element-plus/icons-vue"

const props = defineProps<{
  row?: any
}>()

const emits = defineEmits<{
  (e: "refresh"): void
}>()

const visible = defineModel<boolean>("visible",)
const tableRef = useTemplateRef("tableRef",)
const tableData = ref<any>([],)
const isLoading = ref<boolean>(false,)

// 获取tag数据
const options = ref<GroupData[]>([],)
getEnableLabelOptionList().then((res,) => {
  const arr: GroupData[] = []
  for (const key in (res?.data ?? {})) {
    arr.push({
      group: res?.data[key][0]?.firstLevelTagId ?? undefined,
      groupName: res?.data[key][0]?.firstLevelTagName ?? key,
      options: (res?.data[key] ?? []).map((item,) => {
        return {
          label: item.secondLevelTagName,
          value: item.id,
        }
      },),
    },)
  }
  options.value = [...arr,]
},)

// tag操作
const tagList = ref<TagData[]>([],)
function onAddTag() {
  tagList.value.push({},)
}
function onRemoveTag(index: number,) {
  tagList.value.splice(index, 1,)
}
function onChangeTag(data: TagData, index: number,) {
  tagList.value[index] = data
}

// 初始化
const storage = localStorage.getItem("styleData",)
const { getIndexByKey, } = useKeyIndex()
watch(() => visible.value, (val,) => {
  if (!val) {
    tagList.value = [{},]
    tableData.value = []
    return false
  }
  tableData.value = [props.row ?? {},]
  tagList.value = ((props.row ?? {}).tagRespList ?? []).map((item,) => {
    const data = storage ? JSON.parse(storage,) : {}
    const index = getIndexByKey(item.firstLevelTagId,)
    const style = item.firstLevelTagId ? data[item.firstLevelTagId] ?? STYLE_LIST[index % STYLE_LIST.length] : STYLE_LIST[index % STYLE_LIST.length]
    return {
      label: item.tagName,
      value: item.tagId,
      style,
    }
  },)
  if (tagList.value.length === 0) {
    tagList.value = [{},]
  }
}, {
  immediate: true,
},)

function onCancel() {
  visible.value = false
}
function onConfirm() {
  // 去重与空值
  const obj: Record<number, boolean> = {}
  const arr: { tagId: number, tagName: string }[] = []
  tagList.value.forEach((item,) => {
    if (!item.value) {
      return false
    }
    if (obj[item.value]) {
      return false
    }
    obj[item.value] = true
    arr.push({ tagId: item.value, tagName: item.label ?? "", },)
  },)
  if (arr.length === 0) {
    ElMessage.warning("请至少选择一个标签",)
    return false
  }
  isLoading.value = true
  saveTagInfo({ reviewId: props.row.id, tagList: arr, },)
    .then((res,) => {
      if (res?.success) {
        onCancel()
        ElMessage.success("添加成功",)
        emits("refresh",)
      }
    },)
    .finally(() => {
      isLoading.value = false
    },)
}
</script>

<template>
  <ElDialog
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    title="标签信息修改"
    top="15vh"
    width="1160px"
    append-to-body
  >
    <template #header>
      <span style="font-size: 18px; font-weight: 700">标签信息修改</span>
    </template>
    <div class="tag-edit-table">
      <VxeTable
        ref="tableRef"
        :data="tableData"
        :show-overflow="false"
        border="none"
        max-height="500px"
      >
        <VxeColumn field="style" min-width="80" title="Style" />
        <VxeColumn field="reviewDatetime" min-width="100" title="评论时间" />
        <VxeColumn
          :show-overflow="false"
          field="reviewText"
          min-width="480"
          title="评论内容"
        />
        <VxeColumn field="tag" min-width="200" title="标签">
          <template #default>
            <div style="display: flex; flex-wrap: wrap; gap: 4px">
              <TagSelect
                v-for="(item, index) in tagList"
                :key="`${index}`"
                :default-data="item"
                :options="options"
                show-close
                @change="onChangeTag($event, index)"
                @close="onRemoveTag(index)"
              />
              <ElButton
                :icon="Plus"
                style="border-radius: 20px; height: 29px; padding: 7px 16px; border: none;"
                type="info"
                plain
                @click="onAddTag"
              />
            </div>
          </template>
        </VxeColumn>
      </VxeTable>
    </div>
    <template #footer>
      <div class="footer-actions">
        <ElButton :disabled="isLoading" @click="onCancel">
          关闭
        </ElButton>
        <ElButton
          :disabled="isLoading"
          :loading="isLoading"
          type="primary"
          @click="onConfirm"
        >
          保存
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
