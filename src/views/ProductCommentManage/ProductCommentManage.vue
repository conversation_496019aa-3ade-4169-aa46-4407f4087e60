<script setup lang="ts">
import type { CommentManagePageList, } from "@/apis/productQualityAnalysis/types.ts"
import type { VxeGridProps, VxeTablePropTypes, } from "vxe-table"
import { getCommentManageList, getLabelOptionList, getStyleList, getVendorList, } from "@/apis/productQualityAnalysis"
import { ContentWrap, } from "@/components/ContentWrap"
import { DictSelect, } from "@/components/DictSelect"
import { LayoutForm, } from "@/components/LayoutForm"
import { STYLE_LIST, } from "@/components/TagSelect/src/const.ts"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { useHandleExport, } from "@/hooks/useExport.tsx"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { trackEvent, } from "@/utils/monitor.ts"
import CommentTagEdit from "@/views/ProductCommentManage/components/CommentTagEdit.vue"
import { useKeyIndex, } from "@/views/ProductCommentManage/hooks/useKeyIndex.ts"
import dayjs from "dayjs"
import { ref, } from "vue"
import { TableColumn, } from "./const.ts"

// 查询初始化
const formData = reactive<CommentManagePageList.Params>({
  siteCodeList: [],
  styleList: [],
  reviewRatingList: [],
  commentDate: [dayjs().subtract(2, "month",).format("YYYY-MM-DD",), dayjs().format("YYYY-MM-DD",),],
  tagIdList: [],
  vendorIdList: [],
  isReviewUpdate: undefined,
},)
const rules = computed(() => {
  return {}
},)

// 获取Style
const styleOptions = ref<{ label: string, value: string }[]>([],)
getStyleList().then((res,) => {
  if (res?.success) {
    styleOptions.value = (res.data ?? []).map((item,) => {
      return {
        label: item.styleNumber,
        value: item.styleNumber,
      }
    },)
  }
},)

// 获取供应商
const vendorOptions = ref<{ label: string, value: string }[]>([],)
getVendorList().then((res,) => {
  if (res?.success) {
    vendorOptions.value = (res.data ?? []).map((item,) => {
      return {
        label: item.vendorName,
        value: item.vendorId,
      }
    },)
  }
},)

// 标签数据
const tagOptions = ref<Record<string, { secondLevelTagName: string, id: number }[]>>({},)
getLabelOptionList().then((res,) => {
  tagOptions.value = res?.data ?? {}
},)

const {
  tableData,
  tableRef,
  formRef,
  pager,
  pagerRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: getCommentManageList,
  formData,
  resetWithSearch: true,
  formatParams: (formData,) => {
    let [reviewDatetimeStart, reviewDatetimeEnd,] = ["", "",]
    if (formData?.commentDate?.length) {
      reviewDatetimeStart = formData.commentDate[0]
      reviewDatetimeEnd = formData.commentDate[1]
    }
    return {
      ...formData,
      reviewDatetimeStart,
      reviewDatetimeEnd,
    }
  },
},)

// 表格配置
const tableOptions = computed(() => ({
  columns: TableColumn,
  maxHeight: maxHeight.value - 50,
  minHeight: 280,
  border: "inner",
  scrollX: {
    enabled: true,
    gt: 20,
  },
  scrollY: {
    enabled: true,
    gt: 20,
  },
  loading: loading.value,
  data: (tableData.value) as CommentManagePageList.Row[],
} as VxeGridProps<CommentManagePageList.Row>),)

// 导出
const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()
function downloadAction() {
  const selected: CommentManagePageList.Row[] | undefined
      = tableRef.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected.length > 0) {
    reqParam = JSON.stringify({
      idList: selected.map((item: CommentManagePageList.Row,) => item.id,),
    },)
  } else {
    let [reviewDatetimeStart, reviewDatetimeEnd,] = ["", "",]
    if (formData?.commentDate?.length) {
      reviewDatetimeStart = formData.commentDate[0]
      reviewDatetimeEnd = formData.commentDate[1]
    }
    const obj = {
      ...formData,
      reviewDatetimeStart,
      reviewDatetimeEnd,
    }
    reqParam = JSON.stringify(obj,)
  }
  trackEvent("Export", "评论管理页面导出",)
  exportFn({
    exportType: "amazonReviews-export",
    reqParam,
  },)
}

// 排序
interface RowVO {
  site: string
}
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  multiple: true,
},)
function handleSortChange({ order, property, },) {
  if (order) {
    tableData.value.sort((a, b,) => {
      if (a[property] < b[property]) {
        return order === "asc" ? -1 : 1
      }
      if (a[property] > b[property]) {
        return order === "asc" ? 1 : -1
      }
      return 0
    },)
  }
}

// 修改标签
const editVisible = ref<boolean>(false,)
const currentRow = ref(null,)
function onEditTags(row,) {
  trackEvent("Edit", "评论管理页面编辑标签",)
  editVisible.value = true
  currentRow.value = row ?? {}
}

// 获取标签样式
const { getIndexByKey, } = useKeyIndex()
function getStyle(key: number,) {
  const storage = localStorage.getItem("styleData",)
  const index = getIndexByKey(key,)
  if (!storage) {
    return STYLE_LIST[index % STYLE_LIST.length]
  }
  const data = JSON.parse(storage,)
  return data[`${key}`] ?? STYLE_LIST[index % STYLE_LIST.length]
}
</script>

<template>
  <ContentWrap>
    <div v-if="hasPermission('productCommentManage:search')" class="search">
      <LayoutForm
        ref="formRef"
        :loading="loading"
        :model="formData"
        :rules="rules"
        :span="6"
        class="customer-form"
        query-form
        @reset="handleReset"
        @search="handleSearch"
      >
        <ElFormItem label="站点" prop="siteCodeList">
          <DictSelect
            v-model="formData.siteCodeList"
            class="!w-full"
            default-label="dictEnName"
            dict-code="station"
            popper-class="vxe-table--ignore-clear"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="评分" prop="reviewRatingList">
          <DictSelect
            v-model="formData.reviewRatingList"
            class="!w-full"
            dict-code="PLM_AIDC_STAR_RATING"
            popper-class="vxe-table--ignore-clear"
            clearable
            collapse-tags
            filterable
            multiple
          />
        </ElFormItem>
        <ElFormItem label="Style" prop="styleList">
          <ElSelect
            v-model="formData.styleList"
            clearable
            collapse-tags
            filterable
            multiple
          >
            <ElOption
              v-for="item in styleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="评论日期" prop="commentDate">
          <ElDatePicker
            v-model="formData.commentDate"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始时间"
            style="width: 100%"
            type="daterange"
            value-format="YYYY-MM-DD"
            clearable
          />
        </ElFormItem>
        <ElFormItem label="标签" prop="tagIdList">
          <ElSelect
            v-model="formData.tagIdList"
            clearable
            collapse-tags
            filterable
            multiple
          >
            <ElOptionGroup v-for="(group, key) in tagOptions" :key="key" :label="key">
              <ElOption
                v-for="item in group"
                :key="item.id"
                :label="item.secondLevelTagName"
                :value="item.id"
              />
            </ElOptionGroup>
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="供应商" prop="vendorIdList">
          <ElSelect
            v-model="formData.vendorIdList"
            clearable
            collapse-tags
            filterable
            multiple
          >
            <ElOption
              v-for="item in vendorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="评论有更新" prop="isReviewUpdate">
          <ElSelect v-model="formData.isReviewUpdate" clearable filterable>
            <ElOption :value="1" label="是">
              是
            </ElOption>
            <ElOption :value="0" label="否">
              否
            </ElOption>
          </ElSelect>
        </ElFormItem>
      </LayoutForm>
    </div>
    <div class="operation">
      <ElRow :span="24" justify="space-between">
        <ElCol :span="12">
          <ElButton
            v-hasPermi="['productCommentManage:export']"
            :loading="exportLoading"
            type="primary"
            @click="downloadAction"
          >
            导出
          </ElButton>
        </ElCol>
      </ElRow>
    </div>
    <div v-loading="loading" class="content">
      <VxeGrid
        ref="tableRef"
        v-bind="tableOptions"
        :cell-config="{ height: 140 }"
        :header-cell-config="{ height: 48 }"
        :sort-config="sortConfig"
        @sort-change="handleSortChange"
      >
        <template #productThumbnail="{ row }">
          <ElImage
            :preview-src-list="[row.productThumbnail]"
            :src="row.productThumbnail"
            fit="cover"
            style="width: 60px; height: 60px;"
          />
        </template>
        <template #color="{ row }">
          {{ `${row.color ?? '-'} / ${row.size ?? '-'}` }}
        </template>
        <template #reviewRating="{ row }">
          {{ (row.reviewRating)?.toFixed(1) }} / 5.0
        </template>
        <template #reviewText="{ row }">
          <div style="word-break: break-all; white-space: pre-wrap;">
            <ElText line-clamp="4">
              {{ row.reviewText }}
            </ElText>
          </div>
          <div v-if="row.isReviewUpdate ?? true" class="has-update">
            有更新
          </div>
        </template>
        <template #reviewUrl="{ row }">
          <ElLink :href="row.reviewUrl" target="_blank">
            {{ row.reviewUrl }}
          </ElLink>
        </template>
        <template #productStarRating="{ row }">
          {{ row.productStarRating?.toFixed(1) }}
        </template>
        <template #tagNameList="{ row }">
          <ElPopover trigger="hover" width="400px">
            <div class="pop-tag-wrapper">
              <div
                v-for="item in (row.tagRespList ?? [])"
                :key="item.id"
                :style="getStyle(item.firstLevelTagId)"
                class="custom-tag"
              >
                {{ item.tagName }}
              </div>
            </div>
            <template #reference>
              <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                <div
                  v-for="item in ((row.tagRespList ?? []).slice(0, 6))"
                  :key="item.id"
                  :style="getStyle(item.firstLevelTagId)"
                  class="custom-tag"
                >
                  {{ item.tagName }}
                </div>
                <div v-if="(row.tagRespList ?? []).length > 6" class="custom-tag" style="color: #696969; background-color: #f5f7fa;">
                  ...
                </div>
              </div>
            </template>
          </ElPopover>
        </template>
        <template #operation="{ row }">
          <ElButton type="text" @click="onEditTags(row)">
            修改标签
          </ElButton>
        </template>
      </VxeGrid>
      <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
    </div>

    <CommentTagEdit v-model:visible="editVisible" :row="currentRow" @refresh="handleSearch" />
  </ContentWrap>
</template>

<style scoped lang="scss">
.operation{
  margin-bottom:10px;
}

.has-update {
  padding: 2px 8px;
  background-color: #409EFF;
  border-radius: 20px;
  color: #fff;
  margin: 0 auto;
  font-size: 12px;
  width: 60px;
  text-align: center;
}

.custom-tag {
  padding: 4px 12px;
  border-radius: 20px;
}

.pop-tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
