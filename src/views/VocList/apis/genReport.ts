import { get, postJSON, } from "@/utils/fetch.ts"

export namespace genReport {
  export interface Params {
    queryDimensions: string
    siteList?: string[]
    querySites?: string[]
    queryCommentStartDate?: string
    queryCommentEndDate?: string
    queryStyleStructure?: string
    dateTimeRange?: string[]
    vocReportDTOS?: string[]
    productCategoryName?: string
    productCategoryId?: number
    productCategoryNameList: string[]
    styleNos?: string[]
    asins?: string
    styleStructure?: string
  }
  export interface Row {
    styleImg?: string // 产品主图
    styleNo?: string
    asin?: string
    onlineDate?: string // 上架日期
    starRating?: string // 评分星级
    styleStructure?: string // 款式结构
    productCategoryName?: string // mmt类目名
    styleImagesList?: string[]

  }
  export type List = Row[]
  export type Response = NewPageResponseData<List>
  export type Request = Params
}
export interface ReportNumResp {
  /**
   * 每天允许生成的总报告数
   */
  allowReportTotalNum?: number
  /**
   * 剩余可以使用的报告数
   */
  laveReportNum?: number
  /**
   * 目前已生成的报告数
   */
  usedReportNum?: number
}
/**
 *
 * 分页查询style数据
 */
export function queryStylePage(data: genReport.Request,) {
  return postJSON<genReport.Response>({
    url: `/vocReport/queryStylePage`,
    data,
  },)
}
/** 生成voc报告 */
export function generateReport(data: genReport.Request,) {
  return postJSON<NewBasicResponseData>({
    url: `/vocReport/generateReport`,
    data,
  },)
}
/** 删除VOC报告 */
export function deleteVocReportByIds(params: { idList: string[] },) {
  return get<NewBasicResponseData>({
    url: `/vocReport/deleteVocReportByIds`,
    params,
  },)
}
/***
 刷新voc报告
 **/
export function refreshVocReport(params: { id: string[] },) {
  return get<NewBasicResponseData>({
    url: `/vocReport/refreshVocReport`,
    params,
  },)
}
/****/
export function getReportNum() {
  return get<NewResponseData<ReportNumResp>>({
    url: `/vocReport/getReportNum`,
  },)
}
