import { postJSON, } from "@/utils/fetch.ts"

export namespace Report {
  export interface Params {
    id: string
    reportId?: string
    type?: string
    reportType?: string
  }
  export interface Row {
    styleNo?: string
    id: string
    asin: string
    productImg: string
    reviewCount: string
  }
  export type List = Row[]
  export type Response = NewResponseData<List>
  export type Request = Params
}
/**
 * 数据
 * 查询voc分析报告详情
 */
export interface ReportDetails {
  /**
   * 产品优点分析
   */
  advantages?: KeywordDetail[]
  /**
   * 业务唯一id
   */
  businessId?: number
  /**
   * 购买动机分析
   */
  buyingMotivation?: KeywordDetail[]
  /**
   * 颜色维度分析
   */
  colorSizeAnalyses?: ColorSizeAnalysis[]
  /**
   * 产品缺点分析
   */
  disadvantages?: KeywordDetail[]
  /**
   * 性别画像占比
   */
  genderPortrait?: GenderPortrait
  /**
   * 主键ID
   */
  id: number
  /**
   * 评论数量按月统计
   */
  monthlyReviewStats?: MapInteger
  /**
   * 评论Style数按月统计
   */
  monthlyStyleStats?: MapInteger
  /**
   * 评分关键字排行榜
   */
  ratingKeywords?: KeywordPercent[]
  /**
   * 尺码维度分析
   */
  sizeAnalysis?: ColorSizeAnalysis[]
  /**
   * 评分星级占比
   */
  starRatingSummary?: StarRatingSummary
  /**
   * 未被满足需求分析
   */
  unmetNeeds?: KeywordDetail[]
  /**
   * 使用场景
   */
  usedSence?: KeywordDetail[]
  /**
   * 产品VOC分析头（仅单品）
   */
  vocHead?: VocHead
  /**
   * WHAT购买原因分析
   */
  whatAnalysis?: KeywordPercent[]
  /**
   * WHEN穿着时间分析
   */
  whenAnalysis?: KeywordPercent[]
  /**
   * WHERE使用场景分析
   */
  whereAnalysis?: KeywordPercent[]
  /**
   * WHO画像分析
   */
  whoAnalysis?: KeywordPercent[]
  [property: string]: any
}

export interface KeywordDetail {
  /**
   * 描述
   */
  desc?: string
  /**
   * 标签
   */
  keyword?: string
  /**
   * 占比
   */
  percent?: string
  [property: string]: any
}

export interface ColorSizeAnalysis {
  /**
   * 差评
   */
  bad?: number
  /**
   * 好评
   */
  good?: number
  /**
   * 好评率
   */
  goodRate?: string
  /**
   * 颜色/尺码名称
   */
  name?: string
  /**
   * 评分占比
   */
  percent?: string
  /**
   * 评分数量
   */
  reviewCount?: number
  /**
   * SKU数
   */
  skuCount?: number
  [property: string]: any
}

/**
 * 性别画像占比
 *
 * GenderPortrait
 */
export interface GenderPortrait {
  /**
   * 女性占比
   */
  femalePercent?: string
  /**
   * 男性占比
   */
  malePercent?: string
  [property: string]: any
}

/**
 * 评论数量按月统计
 *
 * 评论Style数按月统计
 */
export interface MapInteger {
  [key: string]: number
}
export interface KeywordPercent {
  /**
   * 标签
   */
  keyword?: string
  /**
   * 占比
   */
  percent?: string
  avg_rating?: string
  [property: string]: any
}

/**
 * 评分星级占比
 *
 * StarRatingSummary
 */
export interface StarRatingSummary {
  /**
   * 平均星级
   */
  averageStar?: number
  stars?: StarDetail[]
  /**
   * 总评论数
   */
  totalReviews?: number
  [property: string]: any
}

export interface StarDetail {
  /**
   * 评分数
   */
  count?: number
  /**
   * 评分数占比
   */
  percent?: string
  /**
   * 星级,5,4,3,2,1
   */
  star?: number
  [property: string]: any
}

/**
 * 产品VOC分析头（仅单品）
 *
 * VocHead
 */
export interface VocHead {
  /**
   * 亚马逊地址链接
   */
  amazonUrl?: string
  /**
   * 首次留评日期
   */
  firstReviewTime?: string
  /** 样本数 */
  sampleNum?: string
  /* 上架天数**/
  shelfDates?: string
  /**
   * 价格
   */
  price?: string
  /**
   * 产品主图
   */
  productImgUrl?: string
  /**
   * 产品名称
   */
  productNameDesc?: string
  /**
   * 最近留评日期
   */
  recentReviewTime?: string
  /**
   * 评分数
   */
  reviewNum?: number
  /**
   * 评分
   */
  reviewStar?: number
  /**
   * 上架时间
   */
  shelfTime?: string
  /**
   * Style编号
   */
  styleNo?: string
  [property: string]: any
}
/**
 * 多品分析产品清单
 */
export function queryMultiProductList(data: Report.Request,) {
  return postJSON<Report.Response>({
    url: `/vocReport/queryMultiProductList`,
    data,
  },)
}
/**
 * 多品详情
 */
export function queryVocReportDetail(data: Report.Request,) {
  return postJSON<NewResponseData<ReportDetails>>({
    url: `/vocReport/queryVocReportDetail`,
    data,
  },)
}
