import type { DimensionsEnums, StatusEnum, } from "@/enums"

import { get, postJSON, } from "@/utils/fetch.ts"

export namespace VocList {
  export interface Params {
    asin?: string
    styleNoList?: string[] // style编号,
    productCategoryNameList?: string // mmt类目名称
    productCategoryNameListArr?: string[][] // mmt类目名称
    styleStructureList?: string[] // 款式结构
    queryDimensions?: DimensionsEnums
    startDate?: string
    endDate?: string
    dateTimeRange?: string[]
  }
  export interface Row {
    id: string
    styleImg?: string // 产品主图
    styleNo?: string // style
    reportName?: string
    createTime?: string
    createByName?: string
    reportStatus?: StatusEnum
    styleImagesList?: string[]
    reportType?: string
    styleCount?: number
    statusDesc?: string
  }
  export type List = Row[]
  export type Response = NewPageResponseData<Row>
  export type Request = Params & PageParams
}
/**
 *
 * 分页查询style数据
 */
export function queryVocReportPage(data: VocList.Request,) {
  return postJSON<VocList.Response>({
    url: `/vocReport/queryVocReportPage`,
    data,
  },)
}
/**
 * 查询款式结构下拉框
 */
export function queryStyleStructureBox(params,) {
  return get<NewBasicResponseData>({
    url: `/vocReport/queryStyleStructureBox`,
    params,
  },)
}
/**
 * 查询style编号下拉框
 */
export function queryStyleNoBox() {
  return get<NewBasicResponseData>({
    url: `/vocReport/queryStyleNoBox`,
  },)
}
