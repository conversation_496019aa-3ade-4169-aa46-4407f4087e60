import { get, postFormData, postJSON, } from "@/utils/fetch.ts"

export namespace CommentDetail {
  export interface Keyword {
    /**
     * VOC报告分析单id
     */
    businessId: string
    /**
     * 评论类型,advantage-优点，shortcoming-缺点
     */
    reviewType: string
  }

  export interface KeywordResponse {
    /**
     * 关键字
     */
    keyword?: string
    count?: string

    [property: string]: any
  }

  export interface CommentRequest {
    /**
     * VOC报告分析单id
     */
    businessId: string
    /**
     * 评论关键字,手动输入的
     */
    reviewText?: string
    /**
     * 评论分析后的关键字,用户从评论关键字中选择的
     */
    keywordTagList?: string[]
    /**
     * 评论时间-结束
     */
    reviewDateEnd?: string
    /**
     * 评论时间-开始
     */
    reviewDateStart?: string
    /**
     * 评论类型,advantage-优点，shortcoming-缺点
     */
    reviewType?: string
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 星级评分，1-5星,字典PLM_AIDC_STAR_RATING
     */
    starRating?: number[]
    DateTimeRange?: string[]
  }

  export interface CommentRow {
    id: string
    /**
     * ASIN 值
     */
    asin?: string
    /**
     * 业务唯一id
     */
    businessId?: number
    /**
     * 购买颜色
     */
    color?: string
    /**
     * 评论分析后的关键字
     */
    keywordTagList?: string[]
    /**
     * 评论中视频地址（数组）
     */
    mediaUrls?: string[]
    /**
     * 评论中图片缩略图地址（数组）
     */
    picUrls?: string[]
    /**
     * 评论时间
     */
    reviewDate?: string
    /**
     * 评论原文内容
     */
    reviewText?: string
    /**
     * 评论标题,星级后边的
     */
    reviewTitle?: string
    /**
     * 购买尺码
     */
    size?: string
    /**
     * 星级评分，1-5星
     */
    starRating?: number
    reviewLink?: string
    /**
     * Style值
     */
    style?: string
  }
  export type CommentRowResponse = NewPageResponseData<CommentRow>
  export type CommentRowRequest = CommentRequest & PageParams
}

/***
 刷新voc报告
 **/
export function queryReviewKeyword(data: CommentDetail.Keyword,) {
  return postJSON<NewResponseData<CommentDetail.KeywordResponse[]>>({
    url: `/vocReport/queryReviewKeyword`,
    data,
  },)
}
/***
 评论分页查询
 **/
export function queryReviewPage(data: CommentDetail.CommentRowRequest,) {
  return postJSON<CommentDetail.CommentRowResponse>({
    url: `/vocReport/queryReviewPage`,
    data,
  },)
}
export function downloadHtmlPdf(data,) {
  return postFormData<NewBasicResponseData>({
    url: `/base/htmlToPdf`,
    data,
    baseURL: "/aidc",
  },)
}
export function downloadPdf(path: string,) {
  return get<NewResponseData<any>>({
    url: `/base/downloadHtmlPdf`,
    params: {
      path,
    },
  },)
}
