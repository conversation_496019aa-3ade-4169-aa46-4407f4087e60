<script setup lang="ts">
import type { VocList, } from "@/views/VocList/apis"
import type { genReport, } from "@/views/VocList/apis/genReport.ts"
import { ContentWrap, } from "@/components/ContentWrap"
import { LayoutForm, } from "@/components/LayoutForm"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { StatusEnum, } from "@/enums"
import { findIdByPath, } from "@/utils"
import { trackDialogEvent, trackEvent, } from "@/utils/monitor.ts"
import { queryVocReportPage, } from "@/views/VocList/apis"
import { deleteVocReportByIds, getReportNum, refreshVocReport, } from "@/views/VocList/apis/genReport.ts"
import GenVocDialog from "@/views/VocList/components/GenVocDialog.vue"
import { FormConfig, } from "@/views/VocList/help.ts"

defineOptions({
  name: "VocList",
},)
const dialog = reactive<{
  visible: boolean
}>({
  visible: false,
},)
const deleteLoading = ref<boolean>(false,)
const CategoryRef = ref()
const formData = reactive<VocList.Params>({
  asin: "",
  styleNoList: [],
  styleStructureList: [],
  productCategoryNameListArr: [],
  productCategoryNameList: "",
  dateTimeRange: [],
},)
// 响应式数据
const {
  tableData,
  pager,
  pagerRef,
  tableRef,
  loading,
  maxHeight,
  handleReset,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: queryVocReportPage,
  formData,
  resetWithSearch: true,
  formatParams: (formData,) => {
    return {
      ...formData,
      productCategoryNameList: (formData?.productCategoryNameListArr ?? []).map(item => item.join("/",),),
      startDate: formData?.dateTimeRange?.[0],
      endDate: formData?.dateTimeRange?.[1],
    }
  },
  defaultPageSize: 20,
},)
onActivated(handleSearch,)
const router = useRouter()

// 查看
function handleViewReport(report: VocList.Row,) {
  trackDialogEvent("VOC分析列表查看报告",)
  router.push({ name: "VocReport", query: { id: report.id, type: report.reportType, }, },)
}
// 打开弹窗
async function openDialog() {
  trackDialogEvent("VOC分析列表生成报告",)
  const response = await getReportNum()
  if (response && response.success) {
    if (!response.data?.laveReportNum || response.data?.laveReportNum <= 0) {
      ElMessage.error("报告数量不足",)
    } else {
      const datas = response.data ?? {}
      const num = response.data?.laveReportNum ?? 0
      const str = `新功能试运行阶段，每天最多只能生成<span style="color: red">${datas.allowReportTotalNum ?? 0}份报告</span>，目前已生成${datas.usedReportNum ?? 0}份，还有<span style="color:red">[${num ?? 0}]份</span>可以使用哦，
期待您的继续体验`
      ElMessageBox.confirm(str, "提示", {
        type: "info",
        dangerouslyUseHTMLString: true,
      },).then(() => {
        dialog.visible = true
      },).catch(() => {},)
    }
  }
}
// 删除
async function deleteAction(report: VocList.Row,) {
  trackEvent("Action", "VOC分析列表页面删除报告",)
  deleteLoading.value = true
  const response = await deleteVocReportByIds({ idList: [report.id,], },).finally(() => {
    deleteLoading.value = false
  },)

  // 检查响应是否存在且成功
  if (response && response.success) {
    ElMessage.success("删除成功",)
  }
  handleSearch()
  deleteLoading.value = false
}
async function refreshAction(report: VocList.Row,) {
  trackEvent("Action", "VOC分析列表页面刷新报告",)
  deleteLoading.value = true
  await refreshVocReport({ id: [report.id,], },).finally(() => {
    deleteLoading.value = false
  },)
  handleSearch()
}
// 查找选中的类目id
function findCategoryId() {
  const formatOptions = CategoryRef.value?.[0]?.formatOptions
  if (formatOptions && formatOptions.length > 0 && formData?.productCategoryNameListArr && formData?.productCategoryNameListArr?.length > 0) {
    return formData?.productCategoryNameListArr?.map(item => findIdByPath(formatOptions, item,),)
  }
  return [0,]
}
// 修改formConfigComputed计算属性
const formConfigComputed = computed(() => {
  return FormConfig.map((item,) => {
    const newItem = { ...item, }
    switch (newItem.field) {
      case "styleStructureList":
        // 类目进行转换拿取id
        newItem.params = {
          ...newItem.params,
          categoryIdList: findCategoryId() as number[],
        }
        break
    }
    return newItem
  },)
},)
watch(formConfigComputed, (newConfig,) => {
  console.log("表单配置变更:", newConfig.map(c => ({
    field: c.field,
    params: c.params,
  }),),)
}, { immediate: true, deep: true, },)
</script>

<template>
  <ContentWrap>
    <div
      ref="tableRef"
      :style="{ height: `${maxHeight - 70}px` }"
      class="inner-content"
    >
      <div v-if="hasPermission('voc:search')" class="search">
        <LayoutForm
          :loading="loading"
          :model="formData"
          :span="6"
          query-form
          @reset="handleReset"
          @search="handleSearch"
        >
          <ElFormItem
            v-for="({ label, layout, field, props, ...other }, index) in formConfigComputed"
            :key="index"
            :label="label"
          >
            <component
              v-bind="{ ...other,
                        ...props, // 级联处理
                        key: field,
              }"
              :is="layout as Component"
              v-if="field === 'productCategoryNameListArr'"
              ref="CategoryRef"
              v-model="formData[field as keyof genReport.Params]"
            />
            <component
              :is="layout as Component"
              v-else
              v-model="formData[field as keyof VocList.Params]"
              v-bind="{ ...other,
                        key: JSON.stringify(other.params),
                        ...props, // 级联处理
              }"
            />
          </ElFormItem>
        </LayoutForm>
      </div>
      <div class="content">
        <h3 class="header">
          VOC分析报告<ElButton v-hasPermi="['voc:generate']" @click="openDialog">
            生成报告  <Icon
              class="mr-1"
              icon="ep:circle-plus-filled"
            />
          </ElButton>
        </h3>

        <ElRow v-loading="loading" :gutter="12">
          <ElCol
            v-for="report in tableData"
            :key="report.id"
            :span="6"
          >
            <div class="voc-report-card">
              <div class="header">
                <ElTooltip :content="report.reportName" placement="top">
                  <h3>{{ report.reportName }}</h3>
                </ElTooltip>
              </div>

              <div class="content">
                <div class="meta">
                  <div class="meta-item">
                    <span class="label">创建时间：</span>
                    <span v-if="report.createTime" class="value">{{ report.createTime }}</span>
                  </div>
                  <div v-if="report.createByName" class="meta-item">
                    <span class="value"> 创建人：{{ report.createByName }}</span>
                  </div>
                </div>

                <div :class="`status status-${report.reportStatus}`">
                  <p v-if="report.reportStatus === StatusEnum.success">
                    <Icon
                      class="mr-1"
                      icon="ep:circle-check"
                    />  分析完成
                  </p>
                  <p v-if="report.reportStatus === StatusEnum.analyzing">
                    <img alt="" src="@/assets/loading.gif" />分析中 <span style="color:#646464;font-size:14px;">请耐心等待...</span>
                  </p>
                  <p
                    v-if="report.reportStatus === StatusEnum.fail"
                  >
                    <Icon
                      class="mr-1"
                      icon="ep:circle-close"
                    />分析失败 <span style="font-size:14px;color:#646464;">{{ report.statusDesc }}</span>
                  </p>
                </div>
                <div class="display">
                  <div class="image-container">
                    <div class="image-scroll">
                      <ElImage
                        v-for="(src, index) in report.styleImagesList"
                        :key="index"
                        :initial-index="index"
                        :preview-src-list="report.styleImagesList"
                        :src="src"
                        class="image-item"
                        fit="contain"

                        hide-on-click-modal
                        lazy
                      >
                        <template #error>
                          <div class="image-slot">
                            <Icon
                              class="mr-1"
                              icon="ep:picture-filled"
                            />加载失败
                          </div>
                        </template>
                      </ElImage>
                    </div>
                  </div>
                  <span class="line" />
                  <span v-if="report.styleCount" class="count">共{{ report.styleCount }}个</span>
                </div>
              </div>

              <div class="footer">
                <ElButton
                  v-if="report.reportStatus === StatusEnum.fail"
                  v-hasPermi="['voc:refresh']"
                  :loading="deleteLoading"
                  class="operate-btn"
                  link
                  @click="refreshAction(report)"
                >
                  <ElTooltip content="刷新报告" placement="top">
                    <Icon class="mr-1" icon="ep:refresh-right" />
                  </ElTooltip>
                </ElButton>
                <ElPopconfirm
                  title="确定删除本条报告?"
                  width="200"
                  @confirm="deleteAction(report)"
                >
                  <template #reference>
                    <ElButton
                      v-if="report.reportStatus === StatusEnum.success"
                      v-hasPermi="['voc:delete']"
                      :loading="deleteLoading"
                      class="operate-btn"
                      link
                    >
                      <ElTooltip content="删除报告" placement="top">
                        <Icon class="mr-1" icon="ep:delete" />
                      </ElTooltip>
                    </ElButton>
                  </template>
                </ElPopconfirm>
                <ElButton
                  :disabled="!report.reportStatus || [StatusEnum.fail, StatusEnum.analyzing].includes(report.reportStatus)"
                  class="seeReport"
                  type="primary"
                  @click="handleViewReport(report)"
                >
                  <Icon class="mr-1" icon="ep:document" />查看报告
                </ElButton>
              </div>
            </div>
          </ElCol>
        </ElRow>
        <ElEmpty v-if="!loading && tableData.length === 0" description="暂无数据" />
      </div>
    </div>
    <Pagination
      v-if="tableData.length > 0"
      ref="pagerRef"
      :pager="pager"
      @change="handlePagerChange"
    />
    <GenVocDialog v-model="dialog.visible" @refresh="handleReset" />
  </ContentWrap>
</template>

<style scoped lang="scss">
.content{
  >.header{
     font-size:18px;
    margin-bottom:10px;
    margin-right:10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    >button{
      margin-left:10px;
      background: linear-gradient(135deg, #FF02FB 0%, #8D36FF 32%, #409EFF 67%, #00FFFF 100%);
      color: white;
      padding: 4px 16px;
      border-radius: 20px;
      border: none;
      height: 40px;
      .el-icon{
         margin-left: 4px;
        font-size: 21px;
      }
    }
  }
}
.inner-content{
  overflow-y: auto;
  overflow-x: hidden;
}
.image-slot{
  display: flex;
  align-items: center;
  flex-direction: row;
  .el-icon{
    font-size:40px;
    color:rgb(192,196,204);
  }
  font-size:14px;
  color:#646464;

}
.voc-report-card {
  border-radius: 8px;
  box-shadow: 0 4px 10px 0 rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom:10px;
  background-color: #fff;
  .header {
    padding: 16px 16px 8px 16px;
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .content {
    margin:0 16px;
    .meta{
      background: #F7F7F7;
      padding:8px 8px 2px;
      border-radius: 6px;
    }
    .meta-item {
      margin-bottom: 2px;
      font-size: 14px;
      font-weight: 300;
      color:#969696
    }
  }
}

.status {
  margin-top: 12px;
  font-size: 16px;
  border-radius: 6px;
  line-height: 14px;
  padding: 0 16px;
  margin-bottom: 8px;
  p{
    display: flex;
    flex-direction: row;
    overflow: hidden;
    align-items: center;
    height: 32px;
    text-overflow: ellipsis;
    line-height: 32px;
  }
  &.status-fail{
  background-color: #FEF0F0;
    p{
      color: #F56C6C;
      white-space: nowrap;
      i{
        vertical-align: bottom;
        font-weight:bold;
      }
      span{
        margin-left: 5px;
      }
    }
  }
  &.status-analyzing{
    background-color: #ECF5FF;
    img{
      width: 30px;
    }
    p{
      color: #409EFF;
      i{
        vertical-align: bottom
      }
    }
  }
  &.status-success{
    background-color:#F0F9EB;
    p{
      color: #67C23A;
      i{
        vertical-align: bottom
      }
    }
  }
}
.display{
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  width: 100%; // 确保容器占据全宽
  justify-content: space-between; // 左右对齐

  .image-container {
    flex: 1;
    max-width: calc(100% - 70px); // 给“共几个”文本留出空间
    overflow: hidden; // 隐藏溢出内容
    position: relative;
    padding: 4px 0;

    .image-scroll {
      display: flex;
      flex-wrap: nowrap; // 不换行
      overflow-x: hidden; // 隐藏水平滚动条
      width: 100%;

      .image-item {
        flex: 0 0 auto; // 不缩放，保持原始大小
        width: 40px;
        height: 40px;
        margin: 0 4px;
        border-radius: 4px;
        padding: 1px;
      }
    }
  }
}
.line{
  width: 6px;
  height: 48px;
  background: linear-gradient( 270deg, rgba(216,216,216,0) 0%, #D8D8D8 100%);
}
.count {
  margin-left: auto; // 使用 margin-left: auto 将元素推到右侧
  color: #666;
  min-width: 60px;
  text-align: right; // 文本右对齐
  padding-right: 5px; // 右侧添加一些内边距
}

.footer {
  margin:  0 16px 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
  text-align: right;
  display: flex;
  justify-content: space-between;
  .operate-btn{
      &:hover{
        color:var(--el-color-primary)
      }
  }
  >.el-button{
    font-size:24px;
    &.seeReport{
      font-size:16px;
    }
  }
}

.view-btn {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: var(--el-color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.view-btn:hover {
  background-color: #40a9ff;
}

.view-btn svg {
  margin-left: 4px;
}
</style>
