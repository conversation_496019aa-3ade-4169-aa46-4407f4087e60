<script lang="ts" setup>
import type { ReportDetails, } from "@/views/VocList/apis/Report.ts"
import { ContentWrap, } from "@/components/ContentWrap"
import { respotTypeEnum, } from "@/enums"
import { queryVocReportDetail, } from "@/views/VocList/apis/Report.ts"
import { AdAndDisAd, Detail, List, PorAndScene, Score, Trend, UnmetAndPur, } from "@/views/VocList/components"
import htmlPdf from "./index.ts"

defineOptions({
  name: "VocReport",
},)

const { currentRoute, } = useRouter()

const route = useRoute()
const loading = ref<boolean>(false,)
const type = computed(() => {
  // 确保返回字符串类型
  const queryType = route.query.type
  return typeof queryType === "string" ? queryType.toLowerCase() : ""
},)

const id = computed(() => {
  // 确保返回字符串类型
  const queryId = route.query.id
  return typeof queryId === "string" ? queryId : ""
},)
const basicData = ref<ReportDetails>()

async function handleQuery() {
  if (!id.value || !type.value) {
    return false
  }
  loading.value = true
  const response = await queryVocReportDetail({ id: id.value, type: type.value, reportType: type.value, },)
  if (response && response.success) {
    basicData.value = response.data
  }
  loading.value = false
}

const downloadLoaing = ref(false,)

async function downloadHtml(callback,) {
  downloadLoaing.value = true
  setTimeout(() => {
    htmlPdf.getPdf("VOC报告", document.querySelector("#custom-wrapper",), callback,)
    callback()
    downloadLoaing.value = false
  }, 4000,)
}

onMounted(handleQuery,)
onActivated(handleQuery,)
</script>

<template>
  <ContentWrap class="custom-wrapper">
    <div>
      <div id="custom-wrapper">
        <div
          v-if="respotTypeEnum.multi === type"
          v-loading="loading"
          class="list element"
        >
          <List @download="downloadHtml" />
        </div>
        <div
          v-if="respotTypeEnum.single === type"
          v-loading="loading"
          class="detail element"
        >
          <Detail :voc-head="basicData?.vocHead" @download="downloadHtml" />
        </div>
        <div v-loading="loading" class="element">
          <PorAndScene
            :portrait="{
              genderPortrait: basicData?.genderPortrait,
              whatAnalysis: basicData?.whatAnalysis,
              whenAnalysis: basicData?.whenAnalysis,
              whereAnalysis: basicData?.whereAnalysis,
              whoAnalysis: basicData?.whoAnalysis,
            }"
            :used-sence="basicData?.usedSence"
          />
        </div>
        <div v-loading="loading" class="element">
          <UnmetAndPur :buying-motivation="basicData?.buyingMotivation" :unmet-needs="basicData?.unmetNeeds" />
        </div>
        <div v-loading="loading" class="element">
          <AdAndDisAd :advantages="basicData?.advantages" :disadvantages="basicData?.disadvantages" />
        </div>
        <div v-if="respotTypeEnum.single === type" v-loading="loading" class="element">
          <Score :rating-keywords="basicData?.ratingKeywords" :star-rating-summary="basicData?.starRatingSummary" />
        </div>
        <div v-loading="loading" class="element" style="width:100%;">
          <!-- 确保数据正确传递到Trend组件 -->
          <Trend
            :monthly-review-stats="basicData?.monthlyReviewStats || {}"
            :monthly-style-stats="basicData?.monthlyStyleStats || {}"
            :type="type"
          />
        </div>
      </div>
      <div v-if="downloadLoaing" id="print-container">
        <div
          v-if="respotTypeEnum.multi === type"
          v-loading="loading"
          class="list element"
        >
          <List :is-download="true" @download="downloadHtml" />
        </div>
        <div
          v-if="respotTypeEnum.single === type"
          v-loading="loading"
          class="detail element"
        >
          <Detail :voc-head="basicData?.vocHead" @download="downloadHtml" />
        </div>
        <div v-loading="loading" class="element">
          <PorAndScene
            :portrait="{
              genderPortrait: basicData?.genderPortrait,
              whatAnalysis: basicData?.whatAnalysis,
              whenAnalysis: basicData?.whenAnalysis,
              whereAnalysis: basicData?.whereAnalysis,
              whoAnalysis: basicData?.whoAnalysis,
            }"
            :used-sence="basicData?.usedSence"
          />
        </div>
        <div v-loading="loading" class="element">
          <UnmetAndPur :buying-motivation="basicData?.buyingMotivation" :unmet-needs="basicData?.unmetNeeds" />
        </div>
        <div v-loading="loading" class="element">
          <AdAndDisAd :advantages="basicData?.advantages" :disadvantages="basicData?.disadvantages" />
        </div>
        <div v-if="respotTypeEnum.single === type" v-loading="loading" class="element">
          <Score :rating-keywords="basicData?.ratingKeywords" :star-rating-summary="basicData?.starRatingSummary" />
        </div>
        <div v-loading="loading" class="element" style="width:100%;">
          <!-- 确保数据正确传递到Trend组件 -->
          <Trend
            :monthly-review-stats="basicData?.monthlyReviewStats || {}"
            :monthly-style-stats="basicData?.monthlyStyleStats || {}"
            :type="type"
          />
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.list {
  position: relative;
}

#print-container {
  position: absolute;
  left: -9999px;
  width: 100%;
  top: -9999px;
  box-sizing: border-box;
  background: white;

  * {
    box-shadow: none !important;
  }
}
</style>
