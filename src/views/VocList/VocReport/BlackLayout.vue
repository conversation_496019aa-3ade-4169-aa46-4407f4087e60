<script lang="ts" setup>
import AppView from "@/layout/components/AppView.vue"

defineOptions({
  name: "BlackLayout",
},)
</script>

<template>
  <AppView />
</template>

<style lang="scss">
@use "@/styles/variables.module.scss" as *;

html, body {
  height: auto !important;
  min-width: auto;
  min-height: 100% !important;
  overflow: auto;
  margin: 0 auto;
}

.#{$adminNamespace}-customer-service {
  display: none !important;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  zoom: 60%;
}
</style>
