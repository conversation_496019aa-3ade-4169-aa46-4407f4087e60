<script setup lang="ts">
import type { KeywordPercent, StarRatingSummary, } from "@/views/VocList/apis/Report.ts"
import CommonCard from "@/views/VocList/components/CommonCard.vue"

// 定义接收的props
defineProps<{
  ratingKeywords?: KeywordPercent[]
  starRatingSummary?: StarRatingSummary
}>()
</script>

<template>
  <div class="wrapper">
    <ElRow :gutter="12">
      <!-- 评分星级占比 -->
      <ElCol :span="12">
        <CommonCard icon="svg-icon:star" title="评分星级占比">
          <div class="panel">
            <div class="left-panel">
              <p class="average">
                {{ starRatingSummary?.averageStar }}
              </p>
              <p>平均星级</p>
              <ElRate
                :model-value=" starRatingSummary?.averageStar"
                allow-half
                disabled
              />
            </div>
            <div class="star-progress-list">
              <p>评分数 / 评分占比</p>
              <div v-for="item in starRatingSummary?.stars" :key="item.star" class="star-progress-item">
                <div class="star-label">
                  {{ item?.star }} 星
                </div>
                <div class="progress">
                  <ElProgress
                    :percentage="parseFloat((item.percent || '0').replace('%', ''))"
                    :show-text="false"
                    :stroke-width="10"
                    status="warning"
                  />
                  <span>{{ item.count }} / {{ item.percent }}</span>
                </div>
              </div>
            </div>
          </div>
        </CommonCard>
      </ElCol>

      <!-- 评分关键字排行榜 -->
      <ElCol :span="12">
        <CommonCard icon="svg-icon:charts" title="评分关键字排行榜">
          <div class="panel" style="align-items: flex-start;">
            <div class="keyword-rating-list">
              <div v-for="(item, index) in ratingKeywords" :key="index" class="keyword-rating-item">
                <div class="keyword">
                  <img v-if="index === 0" alt="" src="@/assets/first.png" />
                  <img v-else-if="index === 1" alt="" src="@/assets/second.png" />
                  <img v-else-if="index === 2" alt="" src="@/assets/third.png" />
                  <span v-else>{{ index + 1 }}</span> {{ item.keyword }}
                </div>
                <div class="line" />
                <div class="rating">
                  <span class="rating-value">{{ item.percent }}</span>
                  <span style="margin-right:5px;"> {{ item.avg_rating }}</span>
                  <ElRate
                    :model-value="parseFloat((item.avg_rating || '0'))"
                    text-color="#ff9900"

                    allow-half
                    disabled
                  />
                </div>
              </div>
            </div>
          </div>
        </CommonCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped lang="scss">
.wrapper{
    padding-bottom: 20px;
}

.panel {
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: row;
  vertical-align: center;
  .left-panel{
      width:20%;
    text-align: right;
    padding-right: 12px;
    .average{
        font-size: 36px;
        color:#303133;
      p{
       color:#646464;
      }
    }
  }
  .star-progress-list {
    background-color: #F7F7F7;
    padding:10px 16px;
    border-radius: 6px;
    >p{
      text-align: right;
      font-size: 14px;
      color: #606266;
    }
    flex:1;
    height: 100%;
    margin:10px 0;
    .star-progress-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;
      .progress{
        width:100%;
        display: flex;
        >span{
          color:#303133;
          margin-left: 10px;
          font-size: 14px;
          font-weight: 400;
          width: 100px;
          display: inline-block;
          text-align: center;
        }
      }
      .star-label {
        margin-right: 10px;
        text-align: left;
        font-size: 14px;
        color: #606266;
      }

      .el-progress {
        flex: 1;
      }
    }
  }
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.summary-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .average-rating {
    display: flex;
    flex-direction: column;
    margin-right: 30px;

    .rating-value {
      font-size: 36px;
      font-weight: bold;
      color: #ff9900;
      line-height: 1;
      margin-bottom: 5px;
    }
  }

  .total-reviews {
    color: #666;
    font-size: 14px;
  }
}
.keyword-rating-list {
  width: 100%;
  .line{
    flex:1;
    border: 1px dashed #DCDFE6;
    margin: 0 30px;
  }
  .keyword-rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    &:last-child {
      border-bottom: none;
    }

    .keyword {
      font-size: 16px;
      color: #333;
      font-weight:400;
      width: 22%;
      img{
        width: 32px;
        vertical-align: middle;
        margin-top: -8px;
      }
    }

    .rating {
      display: flex;
      align-items: center;

      .rating-value {
        margin-left: 8px;
        color: #ff9900;
        font-weight: bold;
      }
    }
  }
}
</style>
