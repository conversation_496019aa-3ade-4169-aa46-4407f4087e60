<script setup lang="ts">
import type { MapInteger, } from "@/views/VocList/apis/Report.ts"
import { useEcharts, } from "@/components/EchartsUI"
import EchartsUI from "@/components/EchartsUI/src/EchartsUI.vue"
import CommonCard from "@/views/VocList/components/CommonCard.vue"

// 定义接收的props
const props = defineProps<{
  monthlyReviewStats?: MapInteger
  monthlyStyleStats?: MapInteger
  type?: string // 添加type参数，用于判断是否为multi类型
}>()
// 使用ECharts组件
const { chartRef: TrendChart, renderEcharts, } = useEcharts()

// 处理数据，将MapInteger转换为图表需要的格式
function processData() {
  // 收集所有的键（日期）
  const allKeys: Record<string, string> = {}
  const reviewData: number[] = []
  const styleData: number[] = []

  // 添加评论统计的键
  if (props.monthlyReviewStats) {
    Object.entries(props.monthlyReviewStats,).forEach(([key, value,],) => {
      allKeys[key] = key
      reviewData.push(value,)
    },)
  }

  // 添加样式统计的键
  if (props.monthlyStyleStats) {
    Object.entries(props.monthlyStyleStats,).forEach(([key, value,],) => {
      allKeys[key] = key
      styleData.push(value,)
    },)
  }

  // 获取所有日期并排序
  const dates = Object.keys(allKeys,)
  dates.sort() // 按日期升序排序
  // 如果没有数据，提供默认值
  if (dates.length === 0) {
    return {
      xAxis: ["No Data",],
      reviewData: [0,],
      styleData: [0,],
    }
  }

  return { xAxis: dates, reviewData, styleData, }
}

// 初始化图表
function initChart() {
  const { xAxis, reviewData, styleData, } = processData()

  renderEcharts({
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "0",
      right: "2%",
      bottom: "55px",
      top: "2%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxis,
      nameLocation: "middle",
      nameGap: 30,
    },

    yAxis: {
      type: "value",
      name: "分数",
      nameLocation: "middle",
      nameGap: 30,

    },
    dataZoom: [
      {
        show: true,
        type: "slider",
        height: 25,
      },
    ],
    series: [
      {
        name: "评论数量",
        type: "bar",
        barWidth: 20,
        data: reviewData,
        showBackground: true,
        label: {
          show: true,
          position: "top",
          color: "#303133",
        },
        backgroundStyle: {
          color: "#DCDFE6",
          borderRadius: [20, 20, 0, 0,],
        },
        itemStyle: {
          color: "#409EFF",
          borderRadius: [20, 20, 0, 0,],
        },
      },
      {
        name: "Style个数",
        type: props.type === "multi" ? "line" : "bar", // 如果type为multi，使用折线图，否则使用柱状图
        data: props.type === "multi" ? styleData : [],
        barWidth: 20,
        itemStyle: {
          color: "#019680",
        },

        // 折线图特有属性
        ...(props.type === "multi"
          ? {
              smooth: true,
              symbol: "circle",
              symbolSize: 6,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.2,
              },
            }
          : {}),
      },
    ],
  },)
}

// 监听props变化，重新渲染图表
watch(() => [props.monthlyReviewStats, props.monthlyStyleStats,], () => {
  initChart()
}, { deep: true, },)

// 组件挂载时初始化图表
onMounted(initChart,)
</script>

<template>
  <CommonCard icon="svg-icon:comments" title="评论趋势分析">
    <div class="trend-container">
      <EchartsUI ref="TrendChart" height="400px" />
    </div>
  </CommonCard>
</template>

<style scoped lang="scss">

</style>
