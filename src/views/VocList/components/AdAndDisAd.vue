<script setup lang="ts">
import type { KeywordDetail, } from "@/views/VocList/apis/Report.ts"
import { CommentTypeEnum, } from "@/enums"
import { trackEvent, } from "@/utils/monitor.ts"
import { computed, } from "vue"
import CommonCard from "./CommonCard.vue"

const props = defineProps<{
  advantages?: KeywordDetail[]
  disadvantages?: KeywordDetail[]
}>()
const route = useRoute()
const router = useRouter()
// 将百分比字符串转换为数字
function parsePercentage(percentStr?: string,): number {
  if (!percentStr) {
    return 0
  }
  // 移除百分号并转换为数字
  return Number.parseFloat(percentStr.replace("%", "",),)
}

// 处理优点数据
const advantagesData = computed(() => {
  if (!props.advantages || props.advantages.length === 0) {
    return [
      { label: "暂无数据", percentage: 0, description: "暂无优点数据", },
    ]
  }

  return props.advantages.map(item => ({
    label: item.keyword || "",
    percentage: parsePercentage(item.percent,),
    description: item.desc || "",
  }),)
},)

// 处理缺点数据
const disadvantagesData = computed(() => {
  if (!props.disadvantages || props.disadvantages.length === 0) {
    return [
      { label: "暂无数据", percentage: 0, description: "暂无缺点数据", },
    ]
  }

  return props.disadvantages.map(item => ({
    label: item.keyword || "",
    percentage: parsePercentage(item.percent,),
    description: item.desc || "",
  }),)
},)
function seeMore(type: string,) {
  const count: number = type === CommentTypeEnum.advantage ? advantagesData.value.length : disadvantagesData.value.length
  trackEvent("Action", `VOC报告页面查看更多评论-${type}`,)
  router.push({ name: "CommentList", query: { businessId: route.query.id, reviewType: type, count, }, },)
}
</script>

<template>
  <div class="wrapper">
    <ElRow :gutter="12">
      <ElCol :span="12">
        <CommonCard icon="svg-icon:thumbup" title="优点">
          <template #default>
            <div v-for="(item, index) in advantagesData" :key="index" class="item-container">
              <p><span>{{ item.label }}</span><span>{{ item.percentage }}%</span></p>
              <ElProgress :percentage=" item.percentage" :show-text="false" status="success" />
            </div>
          </template>
          <template #right>
            <ElButton type="text" @click="seeMore(CommentTypeEnum.advantage)">
              更多评论>
            </ElButton>
          </template>
        </CommonCard>
      </ElCol>
      <ElCol :span="12">
        <CommonCard icon="svg-icon:thumbdown" title="缺点">
          <template #default>
            <div v-for="(item, index) in disadvantagesData" :key="index" class="item-container">
              <p><span>{{ item.label }}</span><span>{{ item.percentage }}%</span></p>
              <ElProgress :percentage="item.percentage" :show-text="false" status="exception" />
            </div>
          </template>
          <template #right>
            <ElButton type="text" @click="seeMore(CommentTypeEnum.shortcoming)">
              更多评论>
            </ElButton>
          </template>
        </CommonCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped lang="scss">
.wrapper{
   padding-bottom: 20px;
}
.item-container{
  margin-bottom: 20px;
  p{
    display: flex;
    margin-bottom: 4px;
    font-size: 14px;
    color:#606266;
    justify-content: space-between;
  }
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  padding-left: 5px;
  border-left: 3px solid #409eff;
}

.percentage-cell {
  font-weight: 500;
  color: #409eff;
}
</style>
