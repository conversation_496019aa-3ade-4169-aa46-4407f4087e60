<script setup lang="ts">
import type { KeywordDetail, } from "@/views/VocList/apis/Report.ts"
import CommonCard from "@/views/VocList/components/CommonCard.vue"
import { computed, } from "vue"

const props = defineProps<{
  unmetNeeds?: KeywordDetail[]
  buyingMotivation?: KeywordDetail[]
}>()

// 将百分比字符串转换为数字
function parsePercentage(percentStr?: string,): number {
  if (!percentStr) {
    return 0
  }
  // 移除百分号并转换为数字
  return Number.parseFloat(percentStr.replace("%", "",),)
}

// 处理未被满足的需求数据
const unmetNeedsData = computed(() => {
  if (!props.unmetNeeds || props.unmetNeeds.length === 0) {
    return [
      { label: "暂无数据", percentage: 0, description: "暂无未被满足的需求数据", },
    ]
  }

  return props.unmetNeeds.map(item => ({
    label: item.keyword || "",
    percentage: parsePercentage(item.percent,),
    description: item.desc || "",
  }),)
},)

// 处理购买动机数据
const buyingMotivationData = computed(() => {
  if (!props.buyingMotivation || props.buyingMotivation.length === 0) {
    return [
      { label: "暂无数据", percentage: 0, description: "暂无购买动机数据", },
    ]
  }

  return props.buyingMotivation.map(item => ({
    label: item.keyword || "",
    percentage: parsePercentage(item.percent,),
    description: item.desc || "",
  }),)
},)
</script>

<template>
  <div class="wrapper">
    <ElRow :gutter="12">
      <ElCol :span="12">
        <CommonCard icon="svg-icon:satisfied" title="未被满足的需求">
          <div class="usage-scenario-table">
            <VxeTable
              :data="unmetNeedsData"
              :show-header="true"
              border="inner"
            >
              <VxeColumn field="label" title="标签" width="180" />
              <VxeColumn field="percentage" min-width="70" title="占比">
                <template #default="{ row }">
                  <ElProgress :percentage=" row.percentage" />
                </template>
              </VxeColumn>
              <VxeColumn field="description" min-width="200" title="描述" />
            </VxeTable>
          </div>
        </CommonCard>
      </ElCol>
      <ElCol :span="12">
        <CommonCard icon="svg-icon:bag" title="购买动机">
          <div class="usage-scenario-table">
            <VxeTable
              :data="buyingMotivationData"
              :show-header="true"
              border="inner"
            >
              <VxeColumn field="label" title="标签" width="180" />
              <VxeColumn field="percentage" min-width="70" title="占比">
                <template #default="{ row }">
                  <ElProgress :percentage=" row.percentage" />
                </template>
              </VxeColumn>
              <VxeColumn field="description" min-width="200" title="描述" />
            </VxeTable>
          </div>
        </CommonCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped lang="scss">
.wrapper{
    padding-bottom: 20px;
}
.usage-scenario-table {
  margin-top: 10px;
  :deep(.vxe-table) {
    border-radius: 4px;
    overflow: hidden;

    .vxe-header--column {
      background-color: #f5f7fa;
      font-weight: bold;
    }

    .vxe-body--row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

p {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  padding-left: 5px;
  border-left: 3px solid #409eff;
}

.percentage-cell {
  font-weight: 500;
  color: #409eff;
}
</style>
