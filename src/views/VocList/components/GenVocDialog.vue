<script setup lang="ts">
import type { LayoutFormInstance, } from "@/components/LayoutForm"
import type { genReport, } from "@/views/VocList/apis/genReport.ts"
import { LayoutForm, } from "@/components/LayoutForm"

import { DimensionsEnums, } from "@/enums"
import { useReportQuery, } from "@/hooks/useReportQuery.ts"
import { findIdByPath, } from "@/utils"
import { generateReport, queryStylePage, } from "@/views/VocList/apis/genReport.ts"
import { genarateConfig, } from "@/views/VocList/help.ts"
import { defineEmits, defineProps, } from "vue"

const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits(["update:modelValue", "submit", "refresh",],)
const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val,),
},)
const generateLoading = ref<boolean>(false,)

const CategoryRef = ref()
const formData = reactive<genReport.Params>({
  queryDimensions: DimensionsEnums.asin,
  siteList: [],
  dateTimeRange: [],
  productCategoryName: "",
  productCategoryNameList: [],
  styleNos: [],
  asins: "",
  styleStructure: "",
  queryCommentStartDate: "",
  queryCommentEndDate: "",
},)
const {
  tableData,
  tableRef,
  formRef,
  handleSearch,
  loading,
  maxHeight,
} = useReportQuery({
  api: queryStylePage,
  formData,
  formatParams(formData,) {
    // 类目进行转换拿取id
    return {
      ...formData,
      productCategoryId: findCategoryId(),
      productCategoryName: (formData?.productCategoryNameList ?? []).join("/",),
      queryCommentStartDate: formData?.dateTimeRange?.[0],
      queryCommentEndDate: formData?.dateTimeRange?.[1],
    }
  },
  hasPager: false,
},)

watch(() => formData.queryDimensions, () => {
  clearFormData()
},)
// 新打开清空
watch(() => props.modelValue, () => {
  formData.queryDimensions = DimensionsEnums.asin
  clearFormData()
},)
function clearFormData() {
  formData.styleNos = []
  formData.asins = ""
  formData.siteList = []
  formData.dateTimeRange = []
  formData.productCategoryNameList = []
  formData.styleStructure = ""
  tableData.value = []
}
// 修改formConfigComputed计算属性
const formConfigComputed = computed(() => {
  // 显式声明依赖项
  const formRefEl = (formRef.value as LayoutFormInstance)?.formRef
  formRefEl?.clearValidate()
  const { queryDimensions, } = formData

  const params: Record<string, string[]> = {
    [DimensionsEnums.asin]: ["asins",],
    [DimensionsEnums.style]: ["styleNos",],
    [DimensionsEnums.category]: ["productCategoryNameList", "styleStructure",],
    common: ["queryDimensions", "siteList", "dateTimeRange",],
  }
  const list = params?.[queryDimensions] || []
  const showList = genarateConfig.filter(item => list.includes(item.field,) || params.common.includes(item.field,),)
  // 处理款式结构依赖MMT类目
  return queryDimensions === DimensionsEnums.category
    ? showList.map((item,) => {
        if (item.field === "styleStructure") {
          const id = findCategoryId()
          item.params = {
            ...item.params,
            categoryIdList: [id,],
          }
        }
        return item
      },)
    : showList
},)
// 查找选中的类目id
function findCategoryId() {
  const formatOptions = CategoryRef.value?.[0]?.formatOptions
  if (formatOptions && formatOptions.length > 0 && formData?.productCategoryNameList && formData?.productCategoryNameList?.length > 0) {
    return findIdByPath(formatOptions, formData?.productCategoryNameList,)
  }
  return 0
}
async function handleVoc() {
  const result = formRef.value && await formRef.value.validate()
  const selectedRows = tableRef.value?.getCheckboxRecords()
  if (!selectedRows || selectedRows.length <= 0) {
    ElMessage.error("请选择Style",)
    return
  }
  if (result) {
    generateLoading.value = true
    const productCategoryName = Array.isArray(formData.productCategoryNameList,) ? formData.productCategoryNameList.join("/",) : formData.productCategoryNameList
    const response = await generateReport({
      ...formData,
      productCategoryName,
      vocReportDTOS: selectedRows,
      querySites: formData.siteList ?? [],
      queryStyleStructure: formData.styleStructure,
      queryCommentStartDate: formData.dateTimeRange?.[0],
      queryCommentEndDate: formData.dateTimeRange?.[1],
    },)
    // 检查响应是否存在且成功
    if (response && response.success) {
      ElMessage.success("提交成功",)
      dialogVisible.value = false
      emit("refresh",)
    }
    generateLoading.value = false
  }
}
watch(formConfigComputed, (newConfig,) => {
  console.log("表单配置变更:", newConfig.map(c => ({
    field: c.field,
    params: c.params,
  }),),)
}, { immediate: true, deep: true, },)
</script>

<template>
  <ElDrawer
    v-model="dialogVisible"

    direction="rtl"
    size="80%"
    title="生成VOC分析报告"
    @close="dialogVisible = false"
  >
    <LayoutForm
      ref="formRef"
      :loading="loading"
      :model="formData"
      :span="8"
      query-form
      @search="handleSearch"
    >
      <ElFormItem
        v-for="({ label, layout, field, props, rules, ...other }, index) in formConfigComputed"
        :key="index"
        :label="label"
        :prop="field"
        :rules="rules"
      >
        <component
          v-bind="{ ...other,
                    ...props, // 级联处理
                    key: field,
          }"
          :is="layout as Component"
          v-if="field === 'productCategoryNameList'"
          ref="CategoryRef"
          v-model="formData[field as keyof genReport.Params]"
        />
        <component
          :is="layout as Component"
          v-else
          v-model="formData[field as keyof genReport.Params]"
          v-bind="{ ...other,
                    ...props, // 级联处理
                    key: JSON.stringify(other.params),
          }"
        />
      </ElFormItem>
    </layoutform>
    <div class="content">
      <VxeTable
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        :max-height="maxHeight - 50"
        :min-height="300"
        :show-header="true"
        auto-resize
        border
      >
        <VxeColumn type="checkbox" width="60" />
        <VxeColumn field="styleImg" title="产品主图" width="120">
          <template #default="{ row }">
            <ElImage
              :preview-src-list="[row.styleImg]"
              :src="row.styleImg"
              fit="cover"
              style="width: 40px; height: 40px;"
              lazy
            />
          </template>
        </VxeColumn>
        <VxeColumn field="styleNo" title="Style" width="100" />
        <VxeColumn field="asin" min-width="30" title="ASIN" />
        <VxeColumn field="onlineDate" min-width="30" title="上架日期" />
        <VxeColumn field="starRating" min-width="60" title="评分星级" />
        <VxeColumn field="productCategoryName" min-width="30" title="MMT类目" />
        <VxeColumn field="styleStructure" min-width="50" title="款式结构" />
      </VxeTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton type="primary" @click="handleVoc">
          生成报告
        </ElButton>
        <ElButton @click="dialogVisible = false">
          取消
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style  lang="scss">

</style>
