import type { GenderPortrait, KeywordDetail, KeywordPercent, } from "@/views/VocList/apis/Report.ts"
import { useEcharts, } from "@/components/EchartsUI"

export function usePor(props: {
  portrait: {
    genderPortrait?: GenderPortrait
    whatAnalysis?: KeywordPercent[]
    whenAnalysis?: KeywordPercent[]
    whereAnalysis?: KeywordPercent[]
    whoAnalysis?: KeywordPercent[]
  }
},) {
  let heightPor = 0
  const { chartRef: chartDom, renderEcharts, } = useEcharts()
  function initChart() {
    const data = handleData()
    // 渲染图表
    renderEcharts({
      tooltip: {
        trigger: "item",
        triggerOn: "mousemove",
      },
      // 添加完整的坐标系统
      xAxis: [{
        type: "category",
        data: ["icon1", "icon2",],
        show: false,
        boundaryGap: true,
      },],
      yAxis: [{
        type: "value",
        min: 0,
        max: 1,
        show: false,
      },],
      grid: {
        top: "30%",
        left: "0%",
        right: "0%",
        bottom: "0",
        containLabel: false,
      },
      series: [
        // 主树图系列
        {
          type: "tree",
          left: 0,
          right: 0,
          orient: "TB",
          edgeShape: "polyline",
          edgeForkPosition: "20%",
          data: [data,],
          symbol: "none", // 隐藏默认节点
          // 调整节点整体大小以容纳多行文本
          // horizontalAlign: "center",
          symbolKeepAspect: true,
          label: {
            position: "inside",
            backgroundColor: "white",
            overflow: "break",
            ellipsis: "...",
            distance: 10, // 标签与节点的距离
            padding: [5, 0,],
            formatter(params: any,) {
              const { child = [], name, type, } = params.data
              const map = child?.map((e: KeywordPercent,) => `{describe|${e.keyword} (${e.percent})}`,) || []
              return type !== "svg"
                ? [
                    `{name|${name}}`,
                    ...map,
                  ].join("\n",)
                : [
                  ]
            },
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "bold",
                color: "#303133",
                align: "center",

                padding: [5, 0,],
              },
              describe: {
                fontSize: 12,
                color: "#646464",
                padding: [5, 0,],
              },
            },
            height: heightPor * 50,

          },
          leaves: {
            label: {
              show: true,
              distance: [0, 40,],
            },
          },
          lineStyle: {
            color: "#A0CEFF",
            width: 1.5,
            curveness: 1,
          },
          initialTreeDepth: 2,
          animationDuration: 550,
          animationDurationUpdate: 750,
          layout: "orthogonal",
          verticalAlign: "middle",
        },
      ],
    },)
  }
  function handleData() {
    heightPor = Math.max((props.portrait.whoAnalysis ?? []).length, (props.portrait.whenAnalysis ?? []).length, (props.portrait.whereAnalysis ?? []).length, (props.portrait.whatAnalysis ?? []).length,)
    return {
      name: "占比",
      type: "svg",
      children: [
        {
          name: "WHO",
          child: props.portrait.whoAnalysis,
        },
        {
          name: "WHEN",
          child: props.portrait.whenAnalysis,
        },
        {
          name: "WHERE",
          child: props.portrait.whereAnalysis,
        },
        {
          name: "WHAT",
          child: props.portrait.whatAnalysis,
        },
      ],
    }
  }
  return {
    chartDom,
    initChart,
  }
}

export function useScene(props: {
  usedSence?: KeywordDetail[]
},) {
  const { chartRef, renderEcharts, } = useEcharts()
  const sceneList = ref<KeywordDetail[]>([],)
  function initSceneChart() {
    if (props.usedSence && props.usedSence.length > 0) {
      const colors = generateHighContrastColors(props.usedSence?.length,)
      sceneList.value = props.usedSence.map((item, index,) => {
        return {
          ...item,
          value: Number.parseFloat((item.percent || "0").replace("%", "",),),
          name: item.keyword,
          desc: item.desc,
          itemStyle: {
            color: colors[index],
          },
        }
      },)
    }
    renderEcharts({
      tooltip: {
        trigger: "item",
        backgroundColor: "#fff",
        borderWidth: 0,
        padding: [15, 20,],
        position: ["50%", "50%",],
        textStyle: {
          color: "#646464",
          fontSize: 14,
        },
        formatter: (params,) => {
          return `
        <div class="tooltipContent">
          <div class="descripContent">
             <span><i style="background:${params.color}"></i>${params.name}</span>
             <span>${params.data.percent}</span>
          </div>
          <div class="description" v-if="${params.data.desc}">
             ${params.data.desc || ""}
         </div>
          </div>
      `
        },
      },
      series: [
        {
          name: "占比",
          type: "pie",
          silent: true,
          label: {
            show: false,
            position: "center",
          },
          labelLine: {
            show: false,
          },
          radius: ["40%", "80%",],
          itemStyle: {
            opacity: 1,
            color: "rgb(244,246,249)",
            // emphasis: {
            //   color: "rgb(244,246,249)",
            //   opacity: 1, // 保持不透明
            //   shadowBlur: 0,
            //   shadowOffsetX: 0,
            // },
          },
          data: props.usedSence?.map(item => ({
            value: Number.parseFloat((item.percent || "0").replace("%", "",),),
            name: item.keyword,
          }),) || [],
        },
        {
          name: "占比",
          type: "pie",
          radius: ["70%", "50%",],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: "rgb(244,246,249)",
            borderWidth: 6,
          },
          label: {
            show: false,
            position: "center",
          },
          labelLine: {
            show: false,
          },
          data: sceneList.value,
        },
      ],
    },)
  }
  return {
    chartRef,
    sceneList,
    initSceneChart,

  }
}
/**
 * 生成高对比度颜色数组
 * @param {number} count 需要生成的颜色数量（默认5种）
 * @returns {string[]} HEX格式的颜色数组
 */
function generateHighContrastColors(count = 5,) {
  const colors: string[] = []
  // 固定饱和度和亮度（可根据需求调整）
  const saturation = 80 // 饱和度 (0-100)
  const lightness = 50 // 亮度 (0-100)
  // 均匀分配色相（Hue 0-360）
  for (let i = 0; i < count; i++) {
    const hue = (i * 360) / count
    colors.push(hslToHex(hue, saturation, lightness,),)
  }
  return colors
}
/**
 * 将HSL颜色转换为HEX格式
 */
function hslToHex(h: number, s: number, l: number,) {
  h = h / 360
  s = s / 100
  l = l / 100

  const c = (1 - Math.abs(2 * l - 1,)) * s
  const x = c * (1 - Math.abs((h * 6) % 2 - 1,))
  const m = l - c / 2
  let r, g, b

  if (h >= 0 && h < 1 / 6) {
    [r, g, b,] = [c, x, 0,]
  } else if (1 / 6 <= h && h < 2 / 6) {
    [r, g, b,] = [x, c, 0,]
  } else if (2 / 6 <= h && h < 3 / 6) {
    [r, g, b,] = [0, c, x,]
  } else if (3 / 6 <= h && h < 4 / 6) {
    [r, g, b,] = [0, x, c,]
  } else if (4 / 6 <= h && h < 5 / 6) {
    [r, g, b,] = [x, 0, c,]
  } else {
    [r, g, b,] = [c, 0, x,]
  }

  const toHex = val =>
    Math.round((val + m) * 255,).toString(16,).padStart(2, "0",)

  return `#${toHex(r,)}${toHex(g,)}${toHex(b,)}`.toUpperCase()
}
