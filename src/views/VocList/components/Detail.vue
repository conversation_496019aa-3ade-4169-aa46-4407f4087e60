<script lang="ts" setup>
import type { VocHead, } from "@/views/VocList/apis/Report.ts"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { trackEvent, } from "@/utils/monitor.ts"
import { useClipboard, } from "@vueuse/core"

defineProps<{
  vocHead?: VocHead
}>()
const emits = defineEmits(["download", "loadSucess",],)
const downloadLoading = ref<boolean>(false,)
// 获取分享链接
const source = ref("",)

function onCopyLink() {
  const { copy, } = useClipboard({ source, },)
  source.value = window.location.href
  copy()
  trackEvent("Action", "VOC报告页面复制分享地址",)
  ElMessage.success("页面地址复制成功，可以分享给其他人查看!",)
}

async function downloadAction() {
  downloadLoading.value = true
  emits("download", () => {
    downloadLoading.value = false
  },)
}

onMounted(() => {
  emits("loadSucess",)
},)
</script>

<template>
  <div class="product-card">
    <div class="product-header">
      <div class="product-info">
        <div v-if="vocHead?.productImgUrl" class="product-image">
          <ElImage
            :preview-src-list="[vocHead.productImgUrl]"
            :src="`${vocHead.productImgUrl}?${new Date().getTime()}`"
            fit="contain"
          />
        </div>
        <div class="content">
          <h2 class="product-title">
            <a :href="vocHead?.amazonUrl" target="_blank"> {{ vocHead?.productNameDesc || '暂无产品名称' }}</a>
          </h2>
          <div class="product-details">
            <div v-if="vocHead?.styleNo" class="detail-row">
              <span class="detail-label">Style：</span>
              <span class="detail-value">{{ vocHead.styleNo }}</span>
            </div>
            <div v-if="vocHead?.reviewStar" class="detail-row">
              <span class="detail-label">评分/评分数：</span>
              <span class="detail-value">{{ vocHead.reviewStar }}/{{ vocHead.reviewNum }}</span>
            </div>
            <div v-if="vocHead?.reviewNum" class="detail-row">
              <span class="detail-label">评论样本：</span>
              <span class="detail-value">{{ vocHead.reviewNum }}</span>
            </div>
            <div v-if="vocHead?.price" class="detail-row">
              <span class="detail-label">价格：</span>
              <span class="detail-value">{{ vocHead.price }}</span>
            </div>
            <div v-if="vocHead?.shelfTime" class="detail-row">
              <span class="detail-label">上架时间：</span>
              <span class="detail-value">{{ vocHead.shelfTime }}（{{ vocHead.shelfDates }}天）</span>
            </div>
            <div v-if="vocHead?.firstReviewTime" class="detail-row">
              <span class="detail-label">首次留评：</span>
              <span class="detail-value">{{ vocHead.firstReviewTime }}</span>
            </div>

            <div v-if="vocHead?.recentReviewTime" class="detail-row">
              <span class="detail-label">最近留评：</span>
              <span class="detail-value">{{ vocHead.recentReviewTime }}</span>
            </div>

            <div v-if="vocHead?.price" class="price-row">
              <span class="price">{{ vocHead.price }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="hasPermission(['report:download', 'report:share'])" class="bg" />
      <div v-if="hasPermission(['report:download', 'report:share'])" class="action">
        <div class="product-actions">
          <ElButton
            v-hasPermi="['report:download']"
            :loading="downloadLoading"
            link
            @click="downloadAction"
          >
            <Icon class="mr-1" icon="svg-icon:download" />
          </ElButton>
        </div>

        <div class="product-actions">
          <ElButton v-hasPermi="['report:share']" link @click="onCopyLink">
            <Icon class="mr-1" icon="svg-icon:share" />
          </ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.product-card {
  border-radius: 8px;
  background-color: #fff;
}

.product-header {
  display: flex;
  padding: 10px 8px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
  margin-bottom: 24px;
  border-radius: 8px;
  border-right: 8px;
  position: relative;

  .product-image {
    width: 104px;
    flex-shrink: 0;
    overflow: hidden;
    height: 104px;

    img {
      width: 100%;
      height: 100%;
    }

    :deep(.el-image) {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .product-info {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    overflow: hidden;
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    margin-right: 12px;

    .content {
      width: calc(100% - 103px);
      padding-right: 16px;

      .product-title {
        line-height: 16px;
        margin: 8px 0;

        a {
          font-size: 16px;
          white-space: nowrap;
          word-break: break-all;
          color: var(--el-color-primary);
          text-decoration: none;
        }
      }
    }

    .product-details {
      background: #F7F7F7;
      display: flex;
      border-radius: 6px;
      padding: 8px 0;
      flex-wrap: wrap;

      > div {
        span {
          color: #646464;
          font-size: 12px;
        }
      }
    }
  }

  .bg {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(76, 87, 109, 0.102) 100%);
    width: 12px;
    position: absolute;
    right: 60px;
    top: 0;
    height: 100%;
  }

  .action {
    width: 52px;
    flex-shrink: 0;
    font-size: 40px;
    text-align: center;

    .el-button {
      font-size: 24px;
      color: #646464;

      &:hover {
        color: var(--el-color-primary)
      }
    }
  }

  .amazon-link {
    margin-top: 10px;
  }
}

.product-title {
  font-size: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-row {
  display: flex;
  align-items: center;
  width: 25%;
  padding: 4px 12px;
}

.detail-label {
  font-weight: bold;
  min-width: 80px;
  color: #555;
  text-align: right;
}

.price-row {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #B12704;
}
</style>
