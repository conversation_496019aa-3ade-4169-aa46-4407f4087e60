<script setup lang="ts">
defineProps<{
  title: string
  icon?: string
}>()
</script>

<template>
  <ElCard>
    <template #header>
      <div>
        <Icon v-if="icon" :icon="icon" class="title-icon" />
        <span class="title">{{ title }}</span>
      </div>
      <slot name="right" />
    </template>
    <template #default>
      <slot />
    </template>
  </ElCard>
</template>

<style scoped lang="scss">
:deep(.el-card__header){
  display: flex;
  justify-content: space-between;
}
  .el-card{
    width:100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 10px 0 rgba(0,0,0,0.2);
    :deep(.el-card__body){
      flex:1;
      padding:0 20px 16px 20px;
    }
    .title{
      color:#3D3D3D;
      font-weight: 700;
      font-size: 16px;
    }
    .title-icon{
      vertical-align: text-top;
      margin-right: 5px;
    }
  }
  :deep(.el-card__header){
    padding: 10px;
    border-bottom: 0;
    .el-button{
      color:#969696;
      font-size:16px;
      font-weight: 400;
    }
  }
</style>
