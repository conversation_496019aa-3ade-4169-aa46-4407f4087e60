<script setup lang="ts">
import type { GenderPortrait, KeywordDetail, KeywordPercent, } from "@/views/VocList/apis/Report.ts"
import EchartsUI from "@/components/EchartsUI/src/EchartsUI.vue"
import CommonCard from "@/views/VocList/components/CommonCard.vue"
import DisplaySvg from "@/views/VocList/components/DisplaySvg.vue"
import { usePor, useScene, } from "@/views/VocList/components/usePorAndScene.ts"

const props = defineProps<{
  portrait: {
    genderPortrait?: GenderPortrait
    whatAnalysis?: KeywordPercent[]
    whenAnalysis?: KeywordPercent[]
    whereAnalysis?: KeywordPercent[]
    whoAnalysis?: KeywordPercent[]
  }
  usedSence?: KeywordDetail[]
}>()
const { chartDom, initChart, } = usePor(props,)
const { chartRef, sceneList, initSceneChart, } = useScene(props,)
watchEffect(() => {
  if (props.portrait.whoAnalysis && props.portrait.whoAnalysis?.length > 0) {
    initChart()
    initSceneChart()
  }
},)
</script>

<template>
  <div class="wrapper">
    <ElRow :gutter="12">
      <ElCol :span="12">
        <CommonCard icon="svg-icon:consumer" title="消费者画像">
          <div style="height:100%;position:relative">
            <DisplaySvg :gender-portrait="portrait.genderPortrait" />
            <EchartsUI ref="chartDom" class="content" />
          </div>
        </CommonCard>
      </ElCol>
      <ElCol :span="12">
        <CommonCard icon="svg-icon:location" title="使用场景">
          <div class="usage-scenario">
            <EchartsUI ref="chartRef" />
            <div class="detail">
              <div v-for="item in sceneList" :key="item.keyword" class="scene-item">
                <span class="scene-label">  <i :style="{ background: item.itemStyle.color }" />{{ item.keyword }}</span>
                <span class="scene-value">{{ item.percent }}</span>
              </div>
            </div>
          </div>
        </CommonCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped lang="scss">
 .wrapper{
   height:364px;
   position: relative;
   padding-bottom: 20px;
   .el-row{
     height:100%;
     .el-col{
       height:100%;
     }
     .title{
     height:40px;
     line-height:40px;
   }
     .content{
       height:100%;
     }
   }
 }
 .usage-scenario{
   height:100%;
   display: flex;
   flex-direction: row;
    >div{
      width: 50%;
      height:100%;
    }
 }
 .detail{
   padding:20px;
   .scene-item{
     height: 33px;
     line-height: 33px;
     margin-bottom: 12px;
     display: flex;
     justify-content: space-between;
     color:#646464;
     .scene-value{
        color:#303133;
       flex:1;
       text-align: right;
     }
     .scene-label{
       white-space: nowrap;
       overflow: hidden;
       i{
         width: 4px;
         height: 12px;
         display: inline-block;
         margin-right: 5px;
       }
     }
   }
 }
</style>

<style>
.tooltipContent{
  width:150px;
  .descripContent{
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    margin-bottom: 5px;
     >span{
       display: inline-block;
       i{
         width: 4px;
         vertical-align: middle;
         margin-right: 2px;
         display: inline-block;
         height:12px;
       }
     }
  }
  .description{
    background-color: #F7F7F7;
    font-size: 14px;
    white-space: wrap;
    word-break: break-all;
    padding:4px 8px;
    border-radius: 4px;
  }
}
</style>
