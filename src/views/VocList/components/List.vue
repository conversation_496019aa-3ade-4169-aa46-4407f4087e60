<script lang="ts" setup>
import type { Report, } from "@/views/VocList/apis/Report.ts"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { trackEvent, } from "@/utils/monitor.ts"
import { queryMultiProductList, } from "@/views/VocList/apis/Report.ts"

defineProps({
  isDownload: {
    type: Boolean,
    default: false,
  },
},)
// 响应式数据
const emits = defineEmits(["download", "loadSucess",],)
const listData = ref<Report.List[]>([],)
const loading = ref<boolean>(false,)
const route = useRoute()
const itemsPerPage = ref<number>(5,)
const currentIndex = ref<number>(0,)
const downloadLoading = ref<boolean>(false,)
const carousel = ref()
const type = computed(() => {
  // 确保返回字符串类型
  const queryType = route.query.type
  return typeof queryType === "string" ? queryType.toLowerCase() : ""
},)

const id = computed(() => {
  // 确保返回字符串类型
  const queryId = route.query.id
  return typeof queryId === "string" ? queryId : ""
},)

async function handleQuery() {
  if (!id.value || !type.value) {
    return false
  }
  loading.value = true
  const response = await queryMultiProductList({ id: id.value, type: type.value, },)

  // 检查响应是否存在且成功
  const groups: Report.List[] = []
  if (response && response.success) {
    for (let i = 0; i < response.data?.length; i += itemsPerPage.value) {
      groups.push(response.data?.slice(i, i + itemsPerPage.value,),)
    }
    listData.value = groups
    emits("loadSucess",)
  }
  loading.value = false
}

// 手动控制轮播索引（模拟滚动）
function handleCarouselChange(newIndex: number,) {
  currentIndex.value = newIndex * itemsPerPage.value
}

function nextGroup() {
  carousel.value?.next()
}

function prevGroup() {
  carousel.value?.prev()
}

// 获取分享链接
const source = ref("",)

function onCopyLink() {
  const { copy, } = useClipboard({ source, },)
  source.value = window.location.href
  copy()
  trackEvent("Action", "VOC报告页面复制分享地址",)
  ElMessage.success("页面地址复制成功，可以分享给其他人查看!",)
}

async function downloadAction() {
  downloadLoading.value = true
  emits("download", () => {
    downloadLoading.value = false
  },)
}

onMounted(handleQuery,)
</script>

<template>
  <div v-if="!isDownload" class="product-list">
    <div class="carouselBox">
      <ElCarousel
        ref="carousel"
        :autoplay="false"
        :interval="0"
        arrow="always"
        height="124px"
        @change="handleCarouselChange"
        @next="nextGroup"
        @prev="prevGroup"
      >
        <ElCarouselItem v-for="(group, index) in listData" :key="index" class="multi-item">
          <div class="item-container">
            <div v-for="(product, productIndex) in group" :key="productIndex">
              <div class="card-header">
                <div class="card-left">
                  <ElImage
                    :initial-index="(index * itemsPerPage) + productIndex"
                    :preview-src-list="listData.flat().map(item => item.productImg)"
                    :preview-teleported="true"
                    :src="product?.productImg"
                    fit="contain"
                  />
                </div>
                <div class="card-right">
                  <h3 class="card-title">
                    {{ product?.asin }}
                  </h3>
                  <div class="card-value">
                    <p> style: {{ product?.styleNo }}</p>
                    <p>评论样本数: {{ product?.reviewCount }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElCarouselItem>
      </ElCarousel>
    </div>
    <div v-if="hasPermission(['report:download', 'report:share'])" class="bg" />
    <div v-if="hasPermission(['report:download', 'report:share'])" class="action">
      <div class="product-actions">
        <ElButton
          v-hasPermi="['report:download']"
          :loading="downloadLoading"
          link
          @click="downloadAction"
        >
          <Icon class="mr-1" icon="svg-icon:download" />
        </ElButton>
      </div>
      <div class="product-actions">
        <ElButton v-hasPermi="['report:share']" link @click="onCopyLink">
          <Icon class="mr-1" icon="svg-icon:share" />
        </ElButton>
      </div>
    </div>
  </div>
  <div v-if="isDownload" class="download-list">
    <ElRow :gutter="24">
      <ElCol v-for="(product, index) in listData.flat()" :key="index" :span="6">
        <div class="card-header">
          <div class="card-left">
            <img :src="`${product?.productImg}?${new Date().getTime()}`" alt="" crossorigin="anonymous" />
          </div>
          <div class="card-right">
            <h3 class="card-title">
              {{ product?.asin }}
            </h3>
            <div class="card-value">
              <p> style: {{ product?.styleNo }}</p>
              <p>评论样本数: {{ product?.reviewCount }}</p>
            </div>
          </div>
        </div>
      </ElCol>
    </ElRow>
  </div>
</template>

<style lang="scss" scoped>
/* 让轮播项横向排列 */
.multi-item .el-carousel__item {
  display: flex !important;
  justify-content: flex-start;
  overflow: visible !important; /* 允许内容溢出 */
}

:deep(.el-carousel__indicators) {
  display: none;
}

/* 单屏容器 */
.item-container {
  display: flex;
  gap: 12px;
  width: 100%;
  margin-top: 8px;

  > div {
    height: 108px;
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    width: calc(100% / 5);
  }
}

.title {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.product-card {
  width: 289px;
  height: 108px;

  > div {
    border: 1px solid #DCDFE6;
    border-right: 8px;
    margin: 0
  }
}

.card-header {
  display: flex;
  padding: 4px 16px;
  width: 100%;
  height: 100%;

  .card-left {
    width: 104px;
    height: 95px;
    vertical-align: middle;
    display: flex;
  }

  .card-right {
    overflow: hidden;
    flex: 1;

    .card-title {
      color: var(--el-color-primary);
      font-size: 16px;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 3px;
    }

    .card-value {
      padding: 4px 8px;
      line-height: 18px;
      background: #F7F7F7;
      color: #969696;
      font-size: 12px;
      border-right: 6px;

      p {
        margin: 4px 0;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
}

.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

.product-list {
  display: flex;
  flex-direction: row;
  position: relative;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
  margin-bottom: 24px;
  border-radius: 8px;
  align-items: center;

  .carouselBox {
    flex: 1;
    padding: 0 8px;
  }

  .bg {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(76, 87, 109, 0.102) 100%);
    width: 12px;
    position: absolute;
    right: 60px;
    top: 0;
    height: 100%;
  }

  .action {
    width: 64px;
    flex-shrink: 0;
    font-size: 40px;
    text-align: center;

    .el-button {
      font-size: 24px;
      color: #646464;

      &:hover {
        color: var(--el-color-primary)
      }
    }
  }
}
</style>
