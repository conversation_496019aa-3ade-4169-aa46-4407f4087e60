<script setup lang="ts">
import type { GenderPortrait, } from "@/views/VocList/apis/Report.ts"

const props = defineProps<{
  genderPortrait?: GenderPortrait
}>()
const manPercent = computed(() => Number.parseFloat((props.genderPortrait?.malePercent || "0").replace("%", "",),),)
const womanPercent = computed(() => Number.parseFloat((props.genderPortrait?.femalePercent || "0").replace("%", "",),),)
</script>

<template>
  <div class="svg-center">
    <span>{{ genderPortrait?.malePercent }}</span>
    <svg
      fill="none"
      height="64"
      version="1.1"
      viewBox="0 0 64 64"
      width="64"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <defs>
        <clipPath id="master_svg0_19_06052/19_6454">
          <rect
            height="64"
            rx="0"
            width="64"
            x="0"
            y="0"
          />
        </clipPath>
        <!-- 新增进度遮罩 -->
        <mask id="male-progress-mask">
          <rect
            :height="`${womanPercent}%`"
            fill="white"
            width="100%"
            x="0"
            y="0"
          /> <!-- 修改 width 控制进度 -->
        </mask>
      </defs>
      <g clip-path="url(#master_svg0_19_06052/19_6454)">
        <g>
          <path
            d="M6.6715901665625,56.3081259765625C6.6715901665625,56.8742259765625,7.1440569765625,57.3331259765625,7.7269059765625,57.3331259765625L56.2697259765625,57.3333259765625C56.8525259765625,57.3333259765625,57.3250259765625,56.8743259765625,57.3250259765625,56.3083259765625L57.3250259765625,55.0841259765625C57.3493259765625,54.7151259765625,57.3986259765625,52.8739259765625,56.1850259765625,50.8385259765625C55.4197259765625,49.5550259765625,54.3090259765625,48.4462259765625,52.8837259765625,47.5430259765625C51.1594259765625,46.4503259765625,48.9659259765625,45.6586259765625,46.3129259765625,45.1819259765625C46.2933259765625,45.1794259765625,44.3261259765625,44.9186259765625,42.3107259765625,44.4039259765625C38.8013259765625,43.5075259765625,38.4945259765625,42.7142259765625,38.492525976562504,42.7065259765625C38.4718259765625,42.6281259765625,38.4419259765625,42.5527259765625,38.4037259765625,42.4819259765625C38.3750259765625,42.3339259765625,38.3041259765625,41.7770259765625,38.4397259765625,40.2847259765625C38.7839259765625,36.4946259765625,40.8170259765625,34.2546259765625,42.4506259765625,32.4549259765625C42.9658259765625,31.8873259765625,43.4523259765625,31.3511259765625,43.8271259765625,30.8254259765625C45.4435259765625,28.5574259765625,45.5935259765625,25.9785259765625,45.6003259765625,25.8186259765625C45.6003259765625,25.4947259765625,45.5631259765625,25.2285259765625,45.4835259765625,24.9817259765625C45.3249259765625,24.4878259765625,45.0263259765625,24.1801259765625,44.8083259765625,23.9554259765625L44.8069259765625,23.9538259765625C44.7519259765625,23.8973259765625,44.7001259765625,23.8437259765625,44.6577259765625,23.7945259765625C44.6415259765625,23.7757259765625,44.5986259765625,23.7259259765625,44.6377259765625,23.4706259765625C44.7811259765625,22.5307259765625,44.8673259765625,21.743825976562498,44.9086259765625,20.9941259765625C44.9822259765625,19.6583259765625,45.0397259765625,17.6607259765625,44.6951259765625,15.7166259765625C44.6526259765625,15.3846259765625,44.5795259765625,15.0339559765625,44.4658259765625,14.6162259765625C44.1018259765625,13.2774759765625,43.5170259765625,12.132905976562501,42.7045259765625,11.188275976562501C42.5647259765625,11.036245976562501,39.1674259765625,7.4570239765625,29.3050259765625,6.7226371765625C27.9413259765625,6.6211038765625,26.5931259765625,6.6757971465625,25.2661259765625,6.7436243765625C24.9462259765625,6.7594375765625,24.5082259765625,6.7811439765625,24.0983259765625,6.8873439765625C23.0801259765625,7.1511169765625,22.8083259765625,7.7965359765625,22.7370259765625,8.1577459765625C22.6187259765625,8.7560659765625,22.8266259765625,9.2214859765625,22.9641259765625,9.5295359765625C22.9841259765625,9.5742459765625,23.0087259765625,9.6294659765625,22.9657259765625,9.7732459765625C22.7367259765625,10.1277959765625,22.3766259765625,10.447435976562499,22.0094259765625,10.750265976562499C21.9033259765625,10.8404659765625,19.4302259765625,12.972865976562499,19.2942259765625,15.7584959765625C18.9275259765625,17.8770259765625,18.9553259765625,21.177825976562502,19.3889259765625,23.4591259765625C19.414125976562502,23.5851259765625,19.4513259765625,23.7717259765625,19.3909259765625,23.8977259765625C18.9246259765625,24.3155259765625,18.3961259765625,24.7891259765625,18.397425976562502,25.8698259765625C18.4030259765625,25.9785259765625,18.553025976562502,28.5574259765625,20.1695259765625,30.8254259765625C20.543925976562498,31.3507259765625,21.030125976562502,31.8865259765625,21.5449259765625,32.453725976562495L21.5449259765625,32.453725976562495C23.1797259765625,34.2547259765625,25.2126259765625,36.4946259765625,25.5570259765625,40.2846259765625C25.6925259765625,41.7769259765625,25.6215259765625,42.3339259765625,25.5929259765625,42.4818259765625C25.5546259765625,42.5526259765625,25.5247259765625,42.6279259765625,25.5042259765625,42.7063259765625C25.5021259765625,42.7141259765625,25.1965259765625,43.5049259765625,21.7029259765625,44.3994259765625C19.6874259765625,44.9155259765625,17.7033259765625,45.1793259765625,17.6441259765625,45.1878259765625C15.0658359765625,45.6230259765625,12.8858059765625,46.3950259765625,11.1646459765625,47.4819259765625C9.7441359765625,48.3791259765625,8.6313459765625,49.4899259765625,7.8571459765625,50.7835259765625C6.6201902765625,52.8505259765625,6.6536305765625,54.7329259765625,6.6715901665625,55.0765259765625L6.6715901665625,56.3081259765625Z"
            fill="#1890FF"
            fill-opacity="1"
          />
        </g>
        <!-- 新增进度条层 -->
        <g mask="url(#male-progress-mask)">
          <path
            d="M6.6715901665625,56.3081259765625C6.6715901665625,56.8742259765625,7.1440569765625,57.3331259765625,7.7269059765625,57.3331259765625L56.2697259765625,57.3333259765625C56.8525259765625,57.3333259765625,57.3250259765625,56.8743259765625,57.3250259765625,56.3083259765625L57.3250259765625,55.0841259765625C57.3493259765625,54.7151259765625,57.3986259765625,52.8739259765625,56.1850259765625,50.8385259765625C55.4197259765625,49.5550259765625,54.3090259765625,48.4462259765625,52.8837259765625,47.5430259765625C51.1594259765625,46.4503259765625,48.9659259765625,45.6586259765625,46.3129259765625,45.1819259765625C46.2933259765625,45.1794259765625,44.3261259765625,44.9186259765625,42.3107259765625,44.4039259765625C38.8013259765625,43.5075259765625,38.4945259765625,42.7142259765625,38.492525976562504,42.7065259765625C38.4718259765625,42.6281259765625,38.4419259765625,42.5527259765625,38.4037259765625,42.4819259765625C38.3750259765625,42.3339259765625,38.3041259765625,41.7770259765625,38.4397259765625,40.2847259765625C38.7839259765625,36.4946259765625,40.8170259765625,34.2546259765625,42.4506259765625,32.4549259765625C42.9658259765625,31.8873259765625,43.4523259765625,31.3511259765625,43.8271259765625,30.8254259765625C45.4435259765625,28.5574259765625,45.5935259765625,25.9785259765625,45.6003259765625,25.8186259765625C45.6003259765625,25.4947259765625,45.5631259765625,25.2285259765625,45.4835259765625,24.9817259765625C45.3249259765625,24.4878259765625,45.0263259765625,24.1801259765625,44.8083259765625,23.9554259765625L44.8069259765625,23.9538259765625C44.7519259765625,23.8973259765625,44.7001259765625,23.8437259765625,44.6577259765625,23.7945259765625C44.6415259765625,23.7757259765625,44.5986259765625,23.7259259765625,44.6377259765625,23.4706259765625C44.7811259765625,22.5307259765625,44.8673259765625,21.743825976562498,44.9086259765625,20.9941259765625C44.9822259765625,19.6583259765625,45.0397259765625,17.6607259765625,44.6951259765625,15.7166259765625C44.6526259765625,15.3846259765625,44.5795259765625,15.0339559765625,44.4658259765625,14.6162259765625C44.1018259765625,13.2774759765625,43.5170259765625,12.132905976562501,42.7045259765625,11.188275976562501C42.5647259765625,11.036245976562501,39.1674259765625,7.4570239765625,29.3050259765625,6.7226371765625C27.9413259765625,6.6211038765625,26.5931259765625,6.6757971465625,25.2661259765625,6.7436243765625C24.9462259765625,6.7594375765625,24.5082259765625,6.7811439765625,24.0983259765625,6.8873439765625C23.0801259765625,7.1511169765625,22.8083259765625,7.7965359765625,22.7370259765625,8.1577459765625C22.6187259765625,8.7560659765625,22.8266259765625,9.2214859765625,22.9641259765625,9.5295359765625C22.9841259765625,9.5742459765625,23.0087259765625,9.6294659765625,22.9657259765625,9.7732459765625C22.7367259765625,10.1277959765625,22.3766259765625,10.447435976562499,22.0094259765625,10.750265976562499C21.9033259765625,10.8404659765625,19.4302259765625,12.972865976562499,19.2942259765625,15.7584959765625C18.9275259765625,17.8770259765625,18.9553259765625,21.177825976562502,19.3889259765625,23.4591259765625C19.414125976562502,23.5851259765625,19.4513259765625,23.7717259765625,19.3909259765625,23.8977259765625C18.9246259765625,24.3155259765625,18.3961259765625,24.7891259765625,18.397425976562502,25.8698259765625C18.4030259765625,25.9785259765625,18.553025976562502,28.5574259765625,20.1695259765625,30.8254259765625C20.543925976562498,31.3507259765625,21.030125976562502,31.8865259765625,21.5449259765625,32.453725976562495L21.5449259765625,32.453725976562495C23.1797259765625,34.2547259765625,25.2126259765625,36.4946259765625,25.5570259765625,40.2846259765625C25.6925259765625,41.7769259765625,25.6215259765625,42.3339259765625,25.5929259765625,42.4818259765625C25.5546259765625,42.5526259765625,25.5247259765625,42.6279259765625,25.5042259765625,42.7063259765625C25.5021259765625,42.7141259765625,25.1965259765625,43.5049259765625,21.7029259765625,44.3994259765625C19.6874259765625,44.9155259765625,17.7033259765625,45.1793259765625,17.6441259765625,45.1878259765625C15.0658359765625,45.6230259765625,12.8858059765625,46.3950259765625,11.1646459765625,47.4819259765625C9.7441359765625,48.3791259765625,8.6313459765625,49.4899259765625,7.8571459765625,50.7835259765625C6.6201902765625,52.8505259765625,6.6536305765625,54.7329259765625,6.6715901665625,55.0765259765625L6.6715901665625,56.3081259765625Z"
            fill="#D9ECFF"
          />
        </g>
      </g>
    </svg>

    <svg
      fill="none"
      height="64"
      version="1.1"
      viewBox="0 0 64 64"
      width="64"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <defs>
        <clipPath id="master_svg0_19_06052/19_6460">
          <rect
            height="64"
            rx="0"
            width="64"
            x="0"
            y="0"
          />
        </clipPath>
        <!-- 新增进度遮罩 -->
        <mask id="female-progress-mask">
          <rect
            :height="`${manPercent}%`"
            fill="white"
            width="100%"
            x="0"
            y="0"
          /> <!-- 修改 width 控制进度 -->
        </mask>
      </defs>
      <g clip-path="url(#master_svg0_19_06052/19_6460)">
        <g>
          <path
            d="M16.138365625,31.6668740234375Q14.796225625,31.3293740234375,14.796225625,30.3168740234375C14.796225625,29.3043740234375,17.816065625,25.2542740234375,18.151665625,22.8916740234375C18.822665625,20.1916740234375,20.164865624999997,12.091504023437501,23.184765625,8.7164340234375C26.301865625,5.2674791234375,29.129465625,5.3138522234375,30.591365625,5.3378281734375C30.703065625,5.3396607934375,30.806865625,5.3413625634375,30.902165625,5.3413625634375C32.244365625,5.3413625634375,38.284065625,5.6788700234375,40.297365625,9.728954023437499C43.317165625,15.1290640234375,44.323765625,24.2417740234375,46.001565625,26.2667740234375C47.679265625,28.2918740234375,49.021365625,31.3293740234375,46.672565625,31.3293740234375Q47.988665625,34.6387740234375,46.867265625,36.3616740234375Q46.273165625,37.2744740234375,44.994865625,37.7420740234375Q42.310565625,38.7545740234375,38.619665625,38.0795740234375L37.770365625,38.0795740234375C37.750465625000004,39.5166740234375,37.789765625,41.0478740234375,37.809365625,41.1488740234375C37.847565625,41.2196740234375,37.877465625,41.2949740234375,37.898165625000004,41.3733740234375C37.900165625,41.3810740234375,38.206965625,42.1744740234375,41.716365625,43.0708740234375C43.731765625,43.5854740234375,45.698965625,43.8462740234375,45.718565625,43.8488740234375C48.371565625,44.3254740234375,50.565065625,45.1172740234375,52.289365625,46.2098740234375C53.714665625,47.1130740234375,54.825365625,48.2218740234375,55.590665625,49.5053740234375C56.804265625,51.5408740234375,56.754965625,53.3820740234375,56.730665625,53.7509740234375L56.730665625,54.9752740234375C56.730665625,55.5412740234375,56.258165625,56.0001740234375,55.675365625,56.0001740234375L7.132545625,56.0000740234375C6.549696625,56.0000740234375,6.077229895,55.5410740234375,6.077229895,54.9749740234375L6.077229895,53.7433740234375C6.059270325,53.3997740234375,6.025830025,51.5173740234375,7.262785625,49.4504740234375C8.036985625,48.1568740234375,9.149775625,47.0460740234375,10.570285625,46.1488740234375C12.291445625,45.0618740234375,14.471475625,44.2898740234375,17.049765625,43.8546740234375C17.108965625,43.8461740234375,19.093065625,43.5824740234375,21.108565625,43.0662740234375C24.602165625,42.1717740234375,24.907765625,41.3809740234375,24.909865625,41.3732740234375Q24.930365625,41.2948740234375,24.998565625,41.1486740234375L24.976065625,38.0795740234375L24.862465625,38.0795740234375Q19.158265625,38.7545740234375,17.144965624999998,36.729474023437504C15.296185625,34.869874023437504,15.711135625,33.2947740234375,16.051105624999998,32.0043740234375C16.081365625,31.8896740234375,16.110965625,31.7771740234375,16.138365625,31.6668740234375Z"
            fill="#BE4EFF"
            fill-rule="evenodd"
          />
        </g>
        <!-- 新增进度条层 -->
        <g mask="url(#female-progress-mask)">
          <path
            d="M16.138365625,31.6668740234375Q14.796225625,31.3293740234375,14.796225625,30.3168740234375C14.796225625,29.3043740234375,17.816065625,25.2542740234375,18.151665625,22.8916740234375C18.822665625,20.1916740234375,20.164865624999997,12.091504023437501,23.184765625,8.7164340234375C26.301865625,5.2674791234375,29.129465625,5.3138522234375,30.591365625,5.3378281734375C30.703065625,5.3396607934375,30.806865625,5.3413625634375,30.902165625,5.3413625634375C32.244365625,5.3413625634375,38.284065625,5.6788700234375,40.297365625,9.728954023437499C43.317165625,15.1290640234375,44.323765625,24.2417740234375,46.001565625,26.2667740234375C47.679265625,28.2918740234375,49.021365625,31.3293740234375,46.672565625,31.3293740234375Q47.988665625,34.6387740234375,46.867265625,36.3616740234375Q46.273165625,37.2744740234375,44.994865625,37.7420740234375Q42.310565625,38.7545740234375,38.619665625,38.0795740234375L37.770365625,38.0795740234375C37.750465625000004,39.5166740234375,37.789765625,41.0478740234375,37.809365625,41.1488740234375C37.847565625,41.2196740234375,37.877465625,41.2949740234375,37.898165625000004,41.3733740234375C37.900165625,41.3810740234375,38.206965625,42.1744740234375,41.716365625,43.0708740234375C43.731765625,43.5854740234375,45.698965625,43.8462740234375,45.718565625,43.8488740234375C48.371565625,44.3254740234375,50.565065625,45.1172740234375,52.289365625,46.2098740234375C53.714665625,47.1130740234375,54.825365625,48.2218740234375,55.590665625,49.5053740234375C56.804265625,51.5408740234375,56.754965625,53.3820740234375,56.730665625,53.7509740234375L56.730665625,54.9752740234375C56.730665625,55.5412740234375,56.258165625,56.0001740234375,55.675365625,56.0001740234375L7.132545625,56.0000740234375C6.549696625,56.0000740234375,6.077229895,55.5410740234375,6.077229895,54.9749740234375L6.077229895,53.7433740234375C6.059270325,53.3997740234375,6.025830025,51.5173740234375,7.262785625,49.4504740234375C8.036985625,48.1568740234375,9.149775625,47.0460740234375,10.570285625,46.1488740234375C12.291445625,45.0618740234375,14.471475625,44.2898740234375,17.049765625,43.8546740234375C17.108965625,43.8461740234375,19.093065625,43.5824740234375,21.108565625,43.0662740234375C24.602165625,42.1717740234375,24.907765625,41.3809740234375,24.909865625,41.3732740234375Q24.930365625,41.2948740234375,24.998565625,41.1486740234375L24.976065625,38.0795740234375L24.862465625,38.0795740234375Q19.158265625,38.7545740234375,17.144965624999998,36.729474023437504C15.296185625,34.869874023437504,15.711135625,33.2947740234375,16.051105624999998,32.0043740234375C16.081365625,31.8896740234375,16.110965625,31.7771740234375,16.138365625,31.6668740234375Z"
            fill="rgb(242,220,255)"
          />
        </g>
      </g>
    </svg>
    <span>{{ genderPortrait?.femalePercent }}</span>
  </div>
</template>

<style scoped lang="scss">
.svg-center{
  text-align: center;
  position: absolute;
  top:0px;
  left:50%;
  z-index: 2;
  display: flex;
  align-items: center;
  transform: translateX(-50%);
  span{
    vertical-align: middle;
  }
  img{
    width: 64px;
    height:64px
  }
}
</style>
