<script setup lang="ts">
import type { CommentDetail, } from "@/views/VocList/apis/commentDetail.ts"
import { computed, } from "vue"

const props = defineProps<{
  comment: CommentDetail.CommentRow
}>()
// 判断是否有图片
const hasImages = computed(() => {
  return !!props.comment?.picUrls && props.comment.picUrls.length > 0
},)
// 判断是否有标签
const hasTags = computed(() => {
  return !!props.comment?.keywordTagList && props.comment.keywordTagList.length > 0
},)
</script>

<template>
  <div class="comment-item">
    <div class="comment-header">
      <div class="rating-container">
        <ElRate :model-value="comment.starRating" size="large" />
        <span class="review-date">{{ comment.reviewTitle }}</span>
      </div>
    </div>

    <div class="comment-body">
      <p v-if="comment?.reviewText" class="comment-content" v-html="comment.reviewText" />

      <div v-if="hasImages" class="media-gallery">
        <ElImage
          v-for="(url, index) in comment.picUrls"
          :key="`img-${index}`"
          :preview-src-list="comment.picUrls"
          :src="url"
          :z-index="9999"
          class="gallery-image"
          fit="cover"
          lazy
        >
          <template #error>
            <div class="image-error">
              <Icon icon="ep:picture" />
              <span>图片加载失败</span>
            </div>
          </template>
        </ElImage>
      </div>
    </div>
    <div class="product-info">
      <span v-if="comment.asin" style="cursor: pointer"><a :href="comment?.reviewLink" target="_blank">ASIN:{{ comment.asin }}</a></span>
      <span v-if="comment.reviewDate ">评论时间: {{ comment.reviewDate }}</span>
      <span v-if="comment.size"> Color: {{ comment.size }}</span>
    </div>
    <div class="comment-footer">
      <div v-if="hasTags" class="keyword-tags">
        <ElTag
          v-for="tag in comment.keywordTagList"
          :key="tag"
          class="keyword-tag"
          effect="light"
          size="small"
        >
          {{ tag }}
        </ElTag>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.el-card{
  border:none;
}
.comment-item {
  border-radius: 8px;
  border-bottom: 1px solid #F0F0F0;
  padding: 16px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

    .rating-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .review-date {
        color:#303133;
        font-size: 16px;
      }
    }

    .product-info {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      span{
        color:#969696;
        font-size: 12px;
        a{
          color:#969696;
          text-decoration: none;
          &:hover{
            color:var(--el-color-primary);
          }
        }
      }
    }

  .comment-body {
    margin-bottom: 2px;

    .comment-title {
      font-size: 14px;
      margin-bottom: 8px;
      color: #646464;
    }
    .comment-content {
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
      white-space: pre-wrap;
    }

    .media-gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .gallery-image {
        width: 100px;
        height: 100px;
        border-radius: 4px;
        object-fit: cover;
        cursor: pointer;

        .image-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;
          font-size: 12px;
          background-color: #f5f7fa;

          .icon {
            font-size: 24px;
            margin-bottom: 4px;
          }
        }
      }
    }

    .video-gallery {
      margin-bottom: 12px;

      .video-container {
        margin-bottom: 8px;

        .gallery-video {
          max-width: 100%;
          max-height: 300px;
          border-radius: 4px;
        }
      }
    }
  }

  .comment-footer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 12px;

    .variant-info {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .keyword-tags {
      display: flex;
      flex-wrap: wrap;
      margin-top:5px;

      .keyword-tag {
        cursor: pointer;
        transition: all 0.2s;
        background: #F7F7F7;
        border-radius: 16px;
        border:none;
        color:#969696;
        margin-right: 16px;
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}
</style>
