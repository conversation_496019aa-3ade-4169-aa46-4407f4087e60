<script setup lang="ts">
import type { CommentDetail, } from "@/views/VocList/apis/commentDetail.ts"
import { hasPermission, } from "@/directives/permission/hasPermi.ts"
import { useHandleExport, } from "@/hooks/useExport.tsx"
import { useReportQuery, } from "@/hooks/useReportQuery"
import { queryReviewKeyword, queryReviewPage, } from "@/views/VocList/apis/commentDetail.ts"
import { ElMessage, } from "element-plus"
import { computed, onMounted, reactive, ref, } from "vue"
import CommentItem from "./components/CommentItem.vue"

defineOptions({
  name: "CommentList",
},)
const route = useRoute()
const reviewType = computed(() => {
  // 确保返回字符串类型
  const reviewType = route.query.reviewType
  return typeof reviewType === "string" ? reviewType.toLowerCase() : ""
},)
const keyWordCount = computed(() => {
  return typeof route.query.count === "string" ? Number(route.query.count,) : 0
},)
const keyWordLoading = ref<boolean>(false,)
const businessId = computed(() => {
  // 确保返回字符串类型
  const businessId = route.query.businessId
  return typeof businessId === "string" ? businessId : ""
},)
const keyWord = ref<CommentDetail.KeywordResponse[]>([],)
// 筛选条件
const filter = reactive<CommentDetail.CommentRequest>({
  keywordTagList: [],
  reviewText: "",
  starRating: [],
  DateTimeRange: [],
  businessId: businessId.value,
  reviewType: reviewType.value,
  reviewDateStart: "",
  reviewDateEnd: "",
},)
const {
  tableData,
  pager,
  pagerRef,
  loading,
  handleSearch,
  handlePagerChange,
} = useReportQuery({
  api: queryReviewPage,
  formData: filter,
  formatParams: () => {
    return {
      ...filter,
      reviewDateStart: filter.DateTimeRange?.[0],
      reviewDateEnd: filter.DateTimeRange?.[1],
    }
  },
},)
// 获取关键词数据
async function queryKeyWord() {
  keyWordLoading.value = true
  try {
    const response = await queryReviewKeyword({
      businessId: businessId.value,
      reviewType: reviewType.value,
    },)

    if (response && response.success) {
      keyWord.value = response.data || []
    }
    filter.keywordTagList = keyWord.value?.slice(0, keyWordCount.value,)?.map((e: CommentDetail.KeywordResponse,) => e.keyword,) as string[]
    keyWordLoading.value = false
  } catch (error) {
    console.error("获取关键词失败:", error,)
    ElMessage.error("获取关键词数据失败",)
    keyWordLoading.value = false
  }
}
// 添加或移除关键词标签
function toggleKeywordTag(keyword: string,) {
  const index: number = filter.keywordTagList?.indexOf(keyword,) as number
  if (index > -1) {
    filter.keywordTagList?.splice(index, 1,)
  } else {
    filter.keywordTagList?.push(keyword,)
  }
  handleSearch()
}
const { handleExport: exportFn, loading: exportLoading, } = useHandleExport()
function downLoadAction() {
  exportFn({
    exportType: "reportReview-export",
    reqParam: JSON.stringify({
      ...filter,
      reviewDateStart: filter.DateTimeRange?.[0],
      reviewDateEnd: filter.DateTimeRange?.[1],
    },),
  },)
}
// 组件挂载时获取关键词数据
onMounted(() => {
  queryKeyWord()
},)
</script>

<template>
  <ContentWrap>
    <div class="comment-analysis">
      <!-- 筛选区域 -->
      <div v-if="hasPermission(['comment:search'])" class="filter-container">
        <!-- 关键字统计 -->
        <div class="comment">
          <h2>评论关键字</h2>
          <div v-if="keyWordLoading" class="loading-container">
            <ElSkeleton :rows="3" animated />
          </div>

          <div v-else-if="keyWord.length === 0" class="empty-data">
            <ElEmpty description="暂无关键字数据" />
          </div>

          <div v-else class="keyword-list">
            <div
              v-for="stat in keyWord"
              :key="stat.keyword"
              :class="{ 'is-active': stat?.keyword ? filter.keywordTagList?.includes(stat?.keyword) : '' }"
              class="keyword-item"
              @click="toggleKeywordTag(stat?.keyword || '')"
            >
              <span class="keyword-text">{{ stat.keyword }}( {{ stat.count }})</span>
            </div>
          </div>
        </div>
      </div>
      <div class="filters">
        <h3>评论列表</h3>
        <div class="filter-content">
          <div class="filter-group">
            <div v-if="hasPermission(['comment:search'])" class="star-filter">
              <ElCheckboxGroup v-model="filter.starRating">
                <ElCheckbox v-for="n in 5" :key="n" :label="n">
                  {{ n }}星
                </ElCheckbox>
              </ElCheckboxGroup>
            </div>
          </div>

          <div v-if="hasPermission(['comment:search'])" class="filter-group">
            <ElDatePicker
              v-model="filter.DateTimeRange"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始时间"
              style="width: 100%"
              type="datetimerange"
              value-format="YYYY-MM-DD"
            />
          </div>

          <div v-if="hasPermission(['comment:search'])" class="filter-group">
            <ElInput
              v-model="filter.reviewText"
              placeholder="输入评论关键字"
              clearable
            />
          </div>
          <div class="filter-actions">
            <ElButton
              v-hasPermi="['comment:search']"
              :loading="loading"
              type="primary"
              @click="handleSearch"
            >
              <Icon class="mr-1" icon="ep:search" />
              搜索
            </ElButton>
            <ElButton
              v-hasPermi="['comment:download']"
              :loading="exportLoading"
              link
              @click="downLoadAction"
            >
              导出
            </ElButton>
          </div>
        </div>
      </div>
      <ElRow :gutter="24">
        <!-- 评论列表 -->
        <ElCol :span="24">
          <ElCard>
            <div v-if="loading" class="loading-container">
              <ElSkeleton :rows="3" animated />
            </div>

            <div v-else-if="tableData.length === 0" class="empty-data">
              <ElEmpty description="暂无评论数据" />
            </div>

            <div v-else class="comment-list">
              <CommentItem
                v-for="comment in tableData"
                :key="comment.id"
                :comment="comment"
              />
            </div>

            <div class="pagination-container">
              <Pagination ref="pagerRef" :pager="pager" @change="handlePagerChange" />
            </div>
          </ElCard>
        </ElCol>
      </ElRow>
    </div>
  </ContentWrap>
</template>

<style lang="scss" scoped>
.comment{
  h2{
   font-size:16px;
    color:#3D3D3D;
    margin-bottom:16px;
  }
   .keyword-list{
     display: flex;
     flex-wrap: wrap;
     .keyword-item{
       padding:4px 8px;
       margin-bottom:16px;
       font-size:16px;
       margin-right:16px;
       cursor: pointer;
       line-height: 24px;
       font-weight: 400;
       background: #F7F7F7;
       color:#969696;
       border-radius:16px;
       &.is-active {
         background: #ECF5FF;
         color:#646464;
         border:1px solid #409EFF;
       }
     }
     .keyword-text {
       flex: 1;
       overflow: hidden;
       text-overflow: ellipsis;
       white-space: nowrap;
     }
   }
}
.filter-container {
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
}
.filters {
  h3{
    font-size:16px;
    color:#3D3D3D;
    margin-bottom:16px;
  }
  .filter-content{
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .star-filter {
      display: flex;
      flex-wrap: nowrap;
      gap: 7px;
      :deep(.el-checkbox-group){
         flex:1;
        display: flex;
      }
    }
    .filter-group {
      flex: 1;
      font-weight:400;
      .filter-label {
        font-weight: 400;
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }
}
.keyword-card {
  height: 100%;
}
.comment-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.el-card{
  border:none
}
.loading-container,
.empty-data {
  padding: 40px 0;
  display: flex;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
