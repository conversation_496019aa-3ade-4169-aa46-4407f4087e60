import { ApiSelect, } from "@/components/ApiSelect"
import { DictSelect, } from "@/components/DictSelect"
import { ElCascader, } from "@/components/ElCascader"
import { DimensionsEnums, } from "@/enums"
import { queryCategoryTree, } from "@/views/Label/apis/Info.ts"
import { queryStyleNoBox, queryStyleStructureBox, } from "@/views/VocList/apis/index.ts"
import { ElInput, } from "element-plus"

export const FormConfig = [
  {
    label: "ASIN",
    layout: ElInput,
    field: "asin",
    props: {
      clearable: true,
      placeholder: "请输入完整的ASIN",
    },
  },
  {
    "label": "Style编码",
    "layout": ApiSelect,
    "span": 12,
    "component": ElSelect,
    "field": "styleNoList",
    "api-config": {
      api: queryStyleNoBox,
    },
    "props": {
      "clearable": true,
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "span": 12,
    "field": "productCategoryNameListArr",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      "clearable": true,
      "filterable": true,
      "collapse-tags": true,
      "props": {
        multiple: true,
      },
    },
  },
  {
    "label": "款式结构",
    "layout": ApiSelect,
    "component": ElSelect,
    "span": 12,
    "field": "styleStructureList",
    "params": {
      categoryIdList: [] as number[],
    },
    "api-config": {
      api: queryStyleStructureBox,
      config: {
        label: "key",
        value: "value",
      },
    },
    "props": {
      "clearable": true,
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    label: "报告时间范围",
    layout: ElDatePicker,
    field: "dateTimeRange",
    span: 12,
    props: {
      type: "daterange",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
    },
  },
]

// 生成报告的配置
export const genarateConfig = [
  {
    "label": "查询维度",
    "layout": ApiSelect,
    "component": ElRadioGroup,
    "childComponent": ElRadio,
    "rules": [
      {
        required: true,
        message: "请选择维度",
        trigger: "change",
      },
    ],
    "field": "queryDimensions",
    "props": {
      clearable: true,
    },
    "api-config": {
      api: () => {
        return {
          code: 0,
          data: [
            { label: "按ASIN", value: DimensionsEnums.asin, },
            { label: "按Style", value: DimensionsEnums.style, },
            { label: "按MMT类目", value: DimensionsEnums.category, },
          ],
        }
      },
      config: {
        label: "label",
        value: "value",
      },

    },
  },
  {
    label: "ASIN",
    layout: ElInput,
    rules: [
      {
        required: true,
        message: "请输入ASIN",
        trigger: "change",
      },
    ],
    field: "asins",
    props: {
      clearable: true,
      maxlength: 200,
      placeholder: "请输入完整的ASIN，多个ASIN用逗号分隔",
      showWordLimit: true,
    },
  },
  {
    "label": "Style编码",
    "layout": ApiSelect,
    "rules": [
      {
        required: true,
        message: "请选择Style编码",
        trigger: "change",
      },
    ],
    "component": ElSelect,
    "field": "styleNos",
    "api-config": {
      api: queryStyleNoBox,
    },
    "props": {
      "clearable": true,
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
    },
  },
  {
    "label": "MMT类目",
    "layout": ApiSelect,
    "component": ElCascader,
    "span": 12,
    "field": "productCategoryNameList",
    "api-config": {
      api: queryCategoryTree,
      config: {
        label: "categoryName",
        value: "categoryName",
        children: "sonCategory",
      },
    },
    "props": {
      clearable: true,
    },
  },
  {
    "label": "款式结构",
    "layout": ApiSelect,
    "component": ElSelect,
    "field": "styleStructure",
    "api-config": {
      api: queryStyleStructureBox,
      config: {
        label: "key",
        value: "value",
      },
    },
    "params": {},
    "props": {
      filterable: true,
      clearable: true,
    },
  },
  {
    label: "站点",
    layout: DictSelect,
    field: "siteList",
    props: {
      "clearable": true,
      "multiple": true,
      "filterable": true,
      "collapse-tags": true,
      "dictCode": "VOC_REPORT_STATION",
    },
  },
  {
    label: "评论时间范围",
    layout: ElDatePicker,
    field: "dateTimeRange",
    props: {
      "type": "daterange",
      "startPlaceholder": "开始时间",
      "endPlaceholder": "结束时间",
      "format": "YYYY-MM-DD",
      "valueFormat": "YYYY-MM-DD",
      "disabled-date": (time,) => {
        // 禁用今天之前的日期
        return time.getTime() < new Date(2022, 0, 1,).getTime()
      },
    },
  },
]
