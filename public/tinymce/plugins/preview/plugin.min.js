/**
 * TinyMCE version 7.6.1 (2025-01-22)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.Env"),n=tinymce.util.Tools.resolve("tinymce.util.Tools");const o=e=>t=>t.options.get(e),i=o("content_style"),s=o("content_css_cors"),c=o("body_class"),r=o("body_id");e.add("preview",(e=>{(e=>{e.addCommand("mcePreview",(()=>{(e=>{const o=(e=>{var o;let a="";const l=e.dom.encode,d=null!==(o=i(e))&&void 0!==o?o:"";a+='<base href="'+l(e.documentBaseURI.getURI())+'">';const m=s(e)?' crossorigin="anonymous"':"";n.each(e.contentCSS,(t=>{a+='<link type="text/css" rel="stylesheet" href="'+l(e.documentBaseURI.toAbsolute(t))+'"'+m+">"})),d&&(a+='<style type="text/css">'+d+"</style>");const y=r(e),u=c(e),v='<script>document.addEventListener && document.addEventListener("click", function(e) {for (var elm = e.target; elm; elm = elm.parentNode) {if (elm.nodeName === "A" && !('+(t.os.isMacOS()||t.os.isiOS()?"e.metaKey":"e.ctrlKey && !e.altKey")+")) {e.preventDefault();}}}, false);<\/script> ",p=e.getBody().dir,w=p?' dir="'+l(p)+'"':"";return"<!DOCTYPE html><html><head>"+a+'</head><body id="'+l(y)+'" class="mce-content-body '+l(u)+'"'+w+">"+e.getContent()+v+"</body></html>"})(e);e.windowManager.open({title:"Preview",size:"large",body:{type:"panel",items:[{name:"preview",type:"iframe",sandboxed:!0,transparent:!1}]},buttons:[{type:"cancel",name:"close",text:"Close",primary:!0}],initialData:{preview:o}}).focus("close")})(e)}))})(e),(e=>{const t=()=>e.execCommand("mcePreview");e.ui.registry.addButton("preview",{icon:"preview",tooltip:"Preview",onAction:t,context:"any"}),e.ui.registry.addMenuItem("preview",{icon:"preview",text:"Preview",onAction:t,context:"any"})})(e)}))}();